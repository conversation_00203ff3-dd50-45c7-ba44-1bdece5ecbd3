import { NextRequest } from 'next/server'
import { createAPIHandler, APIResponse } from '@/lib/api-middleware'
import { emailService } from '@/lib/email-service'
import { z } from 'zod'

const emailTestSchema = z.object({
  smtpHost: z.string().min(1),
  smtpPort: z.number().min(1).max(65535),
  smtpUser: z.string().min(1),
  smtpPassword: z.string().min(1),
  smtpSecure: z.boolean(),
  fromEmail: z.string().email(),
  fromName: z.string().min(1)
})

// POST /api/admin/settings/test-email - Test email configuration
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    validateBody: emailTestSchema
  },
  async (request: NextRequest, { body, user }) => {
    try {
      const { smtpHost, smtpPort, smtpUser, smtpPassword, smtpSecure, fromEmail, fromName } = body

      // Validate that all required fields are provided
      if (!smtpHost || !smtpPort || !smtpUser || !smtpPassword || !fromEmail || !fromName) {
        return APIResponse.error('All email configuration fields are required', 400)
      }

      // Test the email configuration
      const testConfig = {
        smtpHost,
        smtpPort,
        smtpUser,
        smtpPassword,
        smtpSecure,
        fromEmail,
        fromName,
        enableEmailNotifications: true
      }

      // First test the connection
      const connectionTest = await emailService.testConnection(testConfig)

      if (!connectionTest.success) {
        return APIResponse.error(connectionTest.message, 400)
      }

      // If connection is successful, send a test email
      const testEmailResult = await emailService.sendTestEmail(user.email!, testConfig)

      if (!testEmailResult.success) {
        return APIResponse.error(testEmailResult.message, 500)
      }

      return APIResponse.success(
        {
          recipient: user.email,
          timestamp: new Date().toISOString(),
          configuration: {
            smtpHost,
            smtpPort,
            smtpSecure,
            fromEmail,
            fromName
          },
          connectionTest: connectionTest.message,
          emailTest: testEmailResult.message
        },
        'Email configuration test successful! Check your inbox for the test email.'
      )

    } catch (error: any) {
      console.error('Email test failed:', error)
      return APIResponse.error(
        'Email configuration test failed',
        500,
        'EMAIL_TEST_FAILED',
        { message: error.message }
      )
    }
  }
)
