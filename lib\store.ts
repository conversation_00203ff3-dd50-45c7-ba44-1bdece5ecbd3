import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import { Question } from './schemas'

// User role types
export type UserRole = 'STUDENT' | 'ADMIN'

// User interface
export interface User {
  id: string
  name?: string | null
  email?: string | null
  image?: string | null
  role: UserRole
}

// Authentication state
interface AuthState {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  setUser: (user: User | null) => void
  setLoading: (loading: boolean) => void
  signOut: () => void
}

// Quiz state
interface QuizState {
  questions: Question[]
  currentQuestionIndex: number
  answers: string[]
  isSubmitted: boolean
  score: number | null
  title: string
  setQuestions: (questions: Question[]) => void
  setCurrentQuestionIndex: (index: number) => void
  setAnswer: (index: number, answer: string) => void
  setIsSubmitted: (submitted: boolean) => void
  setScore: (score: number | null) => void
  setTitle: (title: string) => void
  resetQuiz: () => void
}

// UI state
interface UIState {
  theme: 'light' | 'dark' | 'system'
  sidebarOpen: boolean
  isLoading: boolean
  notifications: Notification[]
  setTheme: (theme: 'light' | 'dark' | 'system') => void
  setSidebarOpen: (open: boolean) => void
  setIsLoading: (loading: boolean) => void
  addNotification: (notification: Omit<Notification, 'id'>) => void
  removeNotification: (id: string) => void
}

// Notification interface
interface Notification {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message?: string
  duration?: number
}

// Combined store interface
interface AppStore extends AuthState, QuizState, UIState {}

// Create the store
export const useStore = create<AppStore>()(
  devtools(
    persist(
      (set, get) => ({
        // Auth state
        user: null,
        isAuthenticated: false,
        isLoading: false,
        setUser: (user) => set({ user, isAuthenticated: !!user }, false, 'setUser'),
        setLoading: (isLoading) => set({ isLoading }, false, 'setLoading'),
        signOut: () => set({ user: null, isAuthenticated: false }, false, 'signOut'),

        // Quiz state
        questions: [],
        currentQuestionIndex: 0,
        answers: [],
        isSubmitted: false,
        score: null,
        title: 'Quiz',
        setQuestions: (questions) => 
          set({ 
            questions, 
            answers: Array(questions.length).fill(null),
            currentQuestionIndex: 0,
            isSubmitted: false,
            score: null
          }, false, 'setQuestions'),
        setCurrentQuestionIndex: (currentQuestionIndex) => 
          set({ currentQuestionIndex }, false, 'setCurrentQuestionIndex'),
        setAnswer: (index, answer) => {
          const answers = [...get().answers]
          answers[index] = answer
          set({ answers }, false, 'setAnswer')
        },
        setIsSubmitted: (isSubmitted) => set({ isSubmitted }, false, 'setIsSubmitted'),
        setScore: (score) => set({ score }, false, 'setScore'),
        setTitle: (title) => set({ title }, false, 'setTitle'),
        resetQuiz: () => set({
          questions: [],
          currentQuestionIndex: 0,
          answers: [],
          isSubmitted: false,
          score: null,
          title: 'Quiz'
        }, false, 'resetQuiz'),

        // UI state
        theme: 'system',
        sidebarOpen: false,
        notifications: [],
        setTheme: (theme) => set({ theme }, false, 'setTheme'),
        setSidebarOpen: (sidebarOpen) => set({ sidebarOpen }, false, 'setSidebarOpen'),
        setIsLoading: (isLoading) => set({ isLoading }, false, 'setIsLoading'),
        addNotification: (notification) => {
          const id = Math.random().toString(36).substr(2, 9)
          const newNotification = { ...notification, id }
          set(
            (state) => ({ notifications: [...state.notifications, newNotification] }),
            false,
            'addNotification'
          )

          // Auto remove notification after duration
          if (notification.duration !== 0) {
            setTimeout(() => {
              // Use set directly instead of get().removeNotification to avoid potential loops
              set(
                (state) => ({ notifications: state.notifications.filter(n => n.id !== id) }),
                false,
                'autoRemoveNotification'
              )
            }, notification.duration || 5000)
          }
        },
        removeNotification: (id) => set(
          (state) => ({ notifications: state.notifications.filter(n => n.id !== id) }),
          false,
          'removeNotification'
        ),
      }),
      {
        name: 'app-store',
        partialize: (state) => ({
          theme: state.theme,
          // Don't persist sensitive auth data or temporary quiz state
        }),
        skipHydration: true, // Prevent SSR hydration issues
        getStorage: () => ({
          getItem: (name) => {
            // Only access localStorage on client side
            if (typeof window === 'undefined') return null
            try {
              return localStorage.getItem(name)
            } catch {
              return null
            }
          },
          setItem: (name, value) => {
            // Only access localStorage on client side
            if (typeof window === 'undefined') return
            try {
              localStorage.setItem(name, value)
            } catch {
              // Silently fail if localStorage is not available
            }
          },
          removeItem: (name) => {
            // Only access localStorage on client side
            if (typeof window === 'undefined') return
            try {
              localStorage.removeItem(name)
            } catch {
              // Silently fail if localStorage is not available
            }
          },
        }),
      }
    ),
    {
      name: 'app-store',
    }
  )
)

// Cached selectors to prevent infinite loops
const authSelector = (state: AppStore) => ({
  user: state.user,
  isAuthenticated: state.isAuthenticated,
  isLoading: state.isLoading,
  setUser: state.setUser,
  setLoading: state.setLoading,
  signOut: state.signOut,
})

const quizSelector = (state: AppStore) => ({
  questions: state.questions,
  currentQuestionIndex: state.currentQuestionIndex,
  answers: state.answers,
  isSubmitted: state.isSubmitted,
  score: state.score,
  title: state.title,
  setQuestions: state.setQuestions,
  setCurrentQuestionIndex: state.setCurrentQuestionIndex,
  setAnswer: state.setAnswer,
  setIsSubmitted: state.setIsSubmitted,
  setScore: state.setScore,
  setTitle: state.setTitle,
  resetQuiz: state.resetQuiz,
})

const uiSelector = (state: AppStore) => ({
  theme: state.theme,
  sidebarOpen: state.sidebarOpen,
  isLoading: state.isLoading,
  notifications: state.notifications,
  setTheme: state.setTheme,
  setSidebarOpen: state.setSidebarOpen,
  setIsLoading: state.setIsLoading,
  addNotification: state.addNotification,
  removeNotification: state.removeNotification,
})

// Selectors for better performance
export const useAuth = () => useStore(authSelector)
export const useQuiz = () => useStore(quizSelector)
export const useUI = () => useStore(uiSelector)


