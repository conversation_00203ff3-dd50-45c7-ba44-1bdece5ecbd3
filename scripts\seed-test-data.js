const { PrismaClient } = require('../lib/generated/prisma')

const prisma = new PrismaClient()

async function seedTestData() {
  try {
    console.log('🌱 Seeding test data...')

    // Create test subjects, chapters, and topics
    const subject = await prisma.subject.upsert({
      where: { name: 'Mathematics' },
      update: {},
      create: {
        name: 'Mathematics',
        description: 'Mathematical concepts and problem solving'
      }
    })

    const chapter = await prisma.chapter.upsert({
      where: {
        subjectId_name: {
          subjectId: subject.id,
          name: 'Algebra'
        }
      },
      update: {},
      create: {
        name: 'Algebra',
        description: 'Basic algebraic concepts',
        subjectId: subject.id
      }
    })

    const topic = await prisma.topic.upsert({
      where: {
        chapterId_name: {
          chapterId: chapter.id,
          name: 'Linear Equations'
        }
      },
      update: {},
      create: {
        name: 'Linear Equations',
        description: 'Solving linear equations',
        chapterId: chapter.id
      }
    })

    // Get or create admin user
    let adminUser = await prisma.user.findFirst({
      where: { role: 'ADMIN' }
    })

    if (!adminUser) {
      adminUser = await prisma.user.create({
        data: {
          name: 'Test Admin',
          email: '<EMAIL>',
          role: 'ADMIN',
          level: 1,
          totalPoints: 0
        }
      })
    }

    // Create test quizzes
    let quiz1 = await prisma.quiz.findFirst({
      where: { title: 'Basic Algebra Quiz' }
    })

    if (!quiz1) {
      quiz1 = await prisma.quiz.create({
        data: {
          title: 'Basic Algebra Quiz',
        description: 'Test your knowledge of basic algebraic concepts including linear equations, variables, and simple problem solving.',
        instructions: 'Answer all questions to the best of your ability. You have 30 minutes to complete this quiz.',
        type: 'QUIZ',
        difficulty: 'EASY',
        timeLimit: 30,
        passingScore: 70,
        maxAttempts: 3,
        isPublished: true,
        tags: ['algebra', 'mathematics', 'basic'],
        createdBy: adminUser.id,
        subjectId: subject.id,
        chapterId: chapter.id,
        topicId: topic.id
        }
      })
    }

    let quiz2 = await prisma.quiz.findFirst({
      where: { title: 'Advanced Algebra Challenge' }
    })

    if (!quiz2) {
      quiz2 = await prisma.quiz.create({
        data: {
          title: 'Advanced Algebra Challenge',
        description: 'Challenge yourself with complex algebraic problems including quadratic equations, systems of equations, and advanced problem solving.',
        instructions: 'This is a challenging quiz. Take your time and show your work where possible.',
        type: 'TEST_SERIES',
        difficulty: 'HARD',
        timeLimit: 60,
        passingScore: 80,
        maxAttempts: 2,
        isPublished: true,
        tags: ['algebra', 'advanced', 'challenge'],
        createdBy: adminUser.id,
        subjectId: subject.id,
        chapterId: chapter.id,
        topicId: topic.id
        }
      })
    }

    // Create some test questions for the quizzes
    await prisma.question.createMany({
      data: [
        {
          quizId: quiz1.id,
          text: 'What is the value of x in the equation: 2x + 5 = 13?',
          type: 'MCQ',
          options: ['x = 3', 'x = 4', 'x = 5', 'x = 6'],
          correctAnswer: 'x = 4',
          explanation: 'Subtract 5 from both sides: 2x = 8, then divide by 2: x = 4',
          points: 10,
          order: 1
        },
        {
          quizId: quiz1.id,
          text: 'Simplify the expression: 3x + 2x - x',
          type: 'MCQ',
          options: ['4x', '5x', '6x', '2x'],
          correctAnswer: '4x',
          explanation: 'Combine like terms: 3x + 2x - x = 5x - x = 4x',
          points: 10,
          order: 2
        },
        {
          quizId: quiz2.id,
          text: 'Solve the quadratic equation: x² - 5x + 6 = 0',
          type: 'MCQ',
          options: ['x = 2, 3', 'x = 1, 6', 'x = -2, -3', 'x = 0, 5'],
          correctAnswer: 'x = 2, 3',
          explanation: 'Factor: (x-2)(x-3) = 0, so x = 2 or x = 3',
          points: 15,
          order: 1
        }
      ]
    })

    console.log('✅ Test data seeded successfully!')
    console.log(`📚 Created quizzes:`)
    console.log(`   - ${quiz1.title} (${quiz1.difficulty})`)
    console.log(`   - ${quiz2.title} (${quiz2.difficulty})`)
    console.log(`👤 Admin user: ${adminUser.name} (${adminUser.email})`)

  } catch (error) {
    console.error('❌ Error seeding test data:', error)
  } finally {
    await prisma.$disconnect()
  }
}

seedTestData()
