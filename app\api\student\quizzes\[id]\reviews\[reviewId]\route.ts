import { NextRequest } from 'next/server'
import { z } from 'zod'
import { createAPIHandler, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'

const updateReviewSchema = z.object({
  rating: z.number().min(1).max(5).optional(),
  title: z.string().optional(),
  comment: z.string().optional(),
  isPublic: z.boolean().optional()
})

// GET /api/student/quizzes/[id]/reviews/[reviewId] - Get a specific review
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT'
  },
  async (request: NextRequest, { user, params }) => {
    const resolvedParams = await params
    const quizId = resolvedParams?.id as string
    const reviewId = resolvedParams?.reviewId as string

    if (!quizId || !reviewId) {
      return APIResponse.error('Quiz ID and Review ID are required', 400)
    }

    const review = await prisma.quizReview.findFirst({
      where: {
        id: reviewId,
        quizId,
        OR: [
          { userId: user.id }, // User's own review
          { isPublic: true }   // Public reviews
        ]
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            image: true
          }
        }
      }
    })

    if (!review) {
      return APIResponse.error('Review not found', 404)
    }

    return APIResponse.success(review)
  }
)

// PUT /api/student/quizzes/[id]/reviews/[reviewId] - Update a review
export const PUT = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT',
    validateBody: updateReviewSchema
  },
  async (request: NextRequest, { user, params, validatedBody }) => {
    const resolvedParams = await params
    const quizId = resolvedParams?.id as string
    const reviewId = resolvedParams?.reviewId as string

    if (!quizId || !reviewId) {
      return APIResponse.error('Quiz ID and Review ID are required', 400)
    }

    // Check if review exists and belongs to user
    const existingReview = await prisma.quizReview.findFirst({
      where: {
        id: reviewId,
        quizId,
        userId: user.id
      }
    })

    if (!existingReview) {
      return APIResponse.error('Review not found or you do not have permission to edit it', 404)
    }

    // Update review
    const updatedReview = await prisma.quizReview.update({
      where: {
        id: reviewId
      },
      data: validatedBody,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            image: true
          }
        }
      }
    })

    return APIResponse.success(updatedReview)
  }
)

// DELETE /api/student/quizzes/[id]/reviews/[reviewId] - Delete a review
export const DELETE = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT'
  },
  async (request: NextRequest, { user, params }) => {
    const resolvedParams = await params
    const quizId = resolvedParams?.id as string
    const reviewId = resolvedParams?.reviewId as string

    if (!quizId || !reviewId) {
      return APIResponse.error('Quiz ID and Review ID are required', 400)
    }

    // Check if review exists and belongs to user
    const existingReview = await prisma.quizReview.findFirst({
      where: {
        id: reviewId,
        quizId,
        userId: user.id
      }
    })

    if (!existingReview) {
      return APIResponse.error('Review not found or you do not have permission to delete it', 404)
    }

    // Delete review
    await prisma.quizReview.delete({
      where: {
        id: reviewId
      }
    })

    return APIResponse.success({ message: 'Review deleted successfully' })
  }
)
