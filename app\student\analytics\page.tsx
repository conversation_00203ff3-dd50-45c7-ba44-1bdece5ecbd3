"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  BarChart3, 
  TrendingUp, 
  Target, 
  Clock,
  Trophy,
  Star,
  Calendar,
  BookOpen,
  Award,
  Zap,
  Brain,
  Users,
  CheckCircle,
  AlertTriangle,
  RefreshCw,
  Download,
  Filter,
  Eye
} from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { motion } from "framer-motion"
import { toast } from "sonner"
import { AnalyticsExportButton } from "@/components/pdf/pdf-export-button"

interface AnalyticsData {
  overview: {
    totalAttempts: number
    averageScore: number
    totalTimeSpent: number
    quizzesCompleted: number
    currentStreak: number
    longestStreak: number
    totalPoints: number
    rank: number
    totalStudents: number
    improvementRate: number
  }
  performanceByCategory: Array<{
    category: string
    attempts: number
    averageScore: number
    totalTime: number
    improvement: number
  }>
  performanceByDifficulty: Array<{
    difficulty: string
    attempts: number
    averageScore: number
    successRate: number
  }>
  weeklyProgress: Array<{
    week: string
    attempts: number
    averageScore: number
    timeSpent: number
  }>
  strengths: string[]
  weaknesses: string[]
  recommendations: string[]
  achievements: Array<{
    id: string
    title: string
    description: string
    icon: string
    unlockedAt: string
    rarity: 'common' | 'rare' | 'epic' | 'legendary'
  }>
}

export default function StudentAnalytics() {
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [timeRange, setTimeRange] = useState("30d")

  useEffect(() => {
    fetchAnalytics()
  }, [timeRange])

  const fetchAnalytics = async () => {
    setLoading(true)
    try {
      // Extract days from timeRange (e.g., "30d" -> "30")
      const days = timeRange.replace(/[^0-9]/g, '')

      const response = await fetch(`/api/student/analytics?timeRange=${days}`)

      if (!response.ok) {
        throw new Error('Failed to fetch analytics')
      }

      const data = await response.json()

      if (data.success) {
        // The API already returns data in the correct format
        setAnalytics(data.data)
      } else {
        throw new Error(data.message || 'Failed to fetch analytics')
      }
    } catch (error) {
      console.error('Error fetching analytics:', error)
      toast.error('Failed to load analytics')
    } finally {
      setLoading(false)
    }
  }

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common':
        return 'border-gray-300 bg-gray-50 dark:border-gray-600 dark:bg-gray-800'
      case 'rare':
        return 'border-blue-300 bg-blue-50 dark:border-blue-600 dark:bg-blue-900/20'
      case 'epic':
        return 'border-purple-300 bg-purple-50 dark:border-purple-600 dark:bg-purple-900/20'
      case 'legendary':
        return 'border-yellow-300 bg-yellow-50 dark:border-yellow-600 dark:bg-yellow-900/20'
      default:
        return 'border-gray-300 bg-gray-50 dark:border-gray-600 dark:bg-gray-800'
    }
  }

  const exportAnalytics = () => {
    if (!analytics) return
    
    const data = {
      overview: analytics.overview,
      performanceByCategory: analytics.performanceByCategory,
      performanceByDifficulty: analytics.performanceByDifficulty,
      weeklyProgress: analytics.weeklyProgress,
      exportedAt: new Date().toISOString()
    }
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'student-analytics.json'
    a.click()
    URL.revokeObjectURL(url)
    
    toast.success('Analytics exported successfully!')
  }

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-muted rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-muted rounded"></div>
            ))}
          </div>
          <div className="h-64 bg-muted rounded"></div>
        </div>
      </div>
    )
  }

  if (!analytics) {
    return (
      <div className="p-6">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-12">
              <AlertTriangle className="h-16 w-16 text-destructive mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">Analytics Not Available</h3>
              <p className="text-muted-foreground mb-6">
                Unable to load your analytics data. Please try again later.
              </p>
              <Button onClick={fetchAnalytics}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Learning Analytics</h1>
          <p className="text-muted-foreground mt-1">
            Detailed insights into your learning progress and performance
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
            </SelectContent>
          </Select>
          <AnalyticsExportButton
            variant="outline"
            size="sm"
          />
          <Button variant="outline" onClick={fetchAnalytics}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Card className="hover:shadow-lg transition-shadow bg-gradient-to-br from-blue-500 to-blue-600 text-white border-0">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-3xl font-bold">{analytics.overview.totalAttempts}</div>
                  <p className="text-blue-100">Total Attempts</p>
                </div>
                <BookOpen className="h-8 w-8 text-blue-200" />
              </div>
              <div className="mt-4 flex items-center gap-2">
                <TrendingUp className="h-4 w-4 text-blue-200" />
                <p className="text-xs text-blue-100">
                  +{analytics.overview.improvementRate}% improvement
                </p>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <Card className="hover:shadow-lg transition-shadow bg-gradient-to-br from-green-500 to-green-600 text-white border-0">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-3xl font-bold">{analytics.overview.averageScore}%</div>
                  <p className="text-green-100">Average Score</p>
                </div>
                <Target className="h-8 w-8 text-green-200" />
              </div>
              <div className="mt-4">
                <Progress 
                  value={analytics.overview.averageScore} 
                  className="bg-green-400"
                />
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <Card className="hover:shadow-lg transition-shadow bg-gradient-to-br from-purple-500 to-purple-600 text-white border-0">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-3xl font-bold">#{analytics.overview.rank}</div>
                  <p className="text-purple-100">Global Rank</p>
                </div>
                <Trophy className="h-8 w-8 text-purple-200" />
              </div>
              <div className="mt-4">
                <p className="text-xs text-purple-100">
                  Top {Math.round((analytics.overview.rank / analytics.overview.totalStudents) * 100)}% of students
                </p>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          <Card className="hover:shadow-lg transition-shadow bg-gradient-to-br from-orange-500 to-orange-600 text-white border-0">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-3xl font-bold">{analytics.overview.currentStreak}</div>
                  <p className="text-orange-100">Current Streak</p>
                </div>
                <Zap className="h-8 w-8 text-orange-200" />
              </div>
              <div className="mt-4 flex items-center gap-2">
                <Star className="h-4 w-4 text-orange-200" />
                <p className="text-xs text-orange-100">
                  Best: {analytics.overview.longestStreak} days
                </p>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Detailed Analytics */}
      <Tabs defaultValue="performance" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="categories">Categories</TabsTrigger>
          <TabsTrigger value="progress">Progress</TabsTrigger>
          <TabsTrigger value="insights">Insights</TabsTrigger>
          <TabsTrigger value="achievements">Achievements</TabsTrigger>
        </TabsList>

        <TabsContent value="performance" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Performance by Difficulty
                </CardTitle>
                <CardDescription>
                  How you perform across different difficulty levels
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {analytics.performanceByDifficulty.map((item) => (
                    <div key={item.difficulty} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className={
                            item.difficulty === 'EASY' ? 'text-green-600 border-green-600' :
                            item.difficulty === 'MEDIUM' ? 'text-yellow-600 border-yellow-600' :
                            'text-red-600 border-red-600'
                          }>
                            {item.difficulty}
                          </Badge>
                          <span className="text-sm">{item.attempts} attempts</span>
                        </div>
                        <span className="font-semibold">{item.averageScore}%</span>
                      </div>
                      <Progress value={item.averageScore} className="h-2" />
                      <div className="text-xs text-muted-foreground">
                        Success rate: {item.successRate}%
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  Time Analysis
                </CardTitle>
                <CardDescription>
                  Your time management and efficiency metrics
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Total Time Spent</span>
                    <span className="text-2xl font-bold">{Math.round(analytics.overview.totalTimeSpent / 60)}h</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Average per Quiz</span>
                    <span className="text-lg font-semibold">
                      {Math.round(analytics.overview.totalTimeSpent / analytics.overview.totalAttempts)} min
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Efficiency Score</span>
                    <div className="flex items-center gap-2">
                      <Progress value={analytics.overview.averageScore} className="w-20 h-2" />
                      <span className="text-sm font-semibold">{Math.round(analytics.overview.averageScore)}%</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Best Streak</span>
                    <span className="text-lg font-semibold text-orange-600">
                      {analytics.overview.longestStreak} days
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="categories" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BookOpen className="h-5 w-5" />
                Performance by Category
              </CardTitle>
              <CardDescription>
                Your performance across different subject areas
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {analytics.performanceByCategory.map((category) => (
                  <div key={category.category} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-semibold">{category.category}</h4>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">
                          {category.attempts} attempts
                        </Badge>
                        {category.improvement > 0 ? (
                          <Badge className="bg-green-500">
                            <TrendingUp className="h-3 w-3 mr-1" />
                            +{category.improvement}%
                          </Badge>
                        ) : (
                          <Badge variant="destructive">
                            <TrendingUp className="h-3 w-3 mr-1 rotate-180" />
                            {category.improvement}%
                          </Badge>
                        )}
                      </div>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                        <div className="text-2xl font-bold text-blue-600">{category.averageScore}%</div>
                        <div className="text-xs text-muted-foreground">Average Score</div>
                      </div>
                      <div className="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                        <div className="text-2xl font-bold text-green-600">{category.attempts}</div>
                        <div className="text-xs text-muted-foreground">Total Attempts</div>
                      </div>
                      <div className="text-center p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                        <div className="text-2xl font-bold text-purple-600">{category.totalTime}m</div>
                        <div className="text-xs text-muted-foreground">Time Spent</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="progress" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Weekly Progress
              </CardTitle>
              <CardDescription>
                Your learning progress over the past weeks
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analytics.weeklyProgress.map((week, index) => (
                  <div key={week.week} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center gap-4">
                      <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                        <span className="text-sm font-bold text-primary">{index + 1}</span>
                      </div>
                      <div>
                        <h4 className="font-semibold">{week.week}</h4>
                        <p className="text-sm text-muted-foreground">
                          {week.attempts} attempts • {week.timeSpent} minutes
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-green-600">{week.averageScore}%</div>
                      <div className="text-xs text-muted-foreground">Average Score</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="insights" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  Your Strengths
                </CardTitle>
                <CardDescription>
                  Areas where you excel
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3">
                  {analytics.strengths.map((strength, index) => (
                    <li key={index} className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-sm">{strength}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5 text-yellow-600" />
                  Areas for Improvement
                </CardTitle>
                <CardDescription>
                  Topics that need more attention
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3">
                  {analytics.weaknesses.map((weakness, index) => (
                    <li key={index} className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                      <span className="text-sm">{weakness}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="h-5 w-5" />
                Personalized Recommendations
              </CardTitle>
              <CardDescription>
                AI-powered suggestions to improve your learning
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="space-y-4">
                {analytics.recommendations.map((recommendation, index) => (
                  <li key={index} className="flex items-start gap-3">
                    <div className="w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center mt-0.5">
                      <span className="text-xs font-bold text-primary">{index + 1}</span>
                    </div>
                    <span className="text-sm leading-relaxed">{recommendation}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="achievements" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Award className="h-5 w-5" />
                Recent Achievements
              </CardTitle>
              <CardDescription>
                Badges and milestones you've unlocked
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {analytics.achievements.map((achievement, index) => (
                  <motion.div
                    key={achievement.id}
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.3, delay: 0.1 * index }}
                    className={`p-4 rounded-lg border-2 ${getRarityColor(achievement.rarity)}`}
                  >
                    <div className="flex items-center gap-3">
                      <div className="text-3xl">{achievement.icon}</div>
                      <div className="flex-1">
                        <h4 className="font-semibold">{achievement.title}</h4>
                        <p className="text-sm text-muted-foreground">
                          {achievement.description}
                        </p>
                        <div className="flex items-center gap-2 mt-2">
                          <Badge variant="outline" className="text-xs">
                            {achievement.rarity}
                          </Badge>
                          <span className="text-xs text-muted-foreground">
                            {new Date(achievement.unlockedAt).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
              <div className="mt-6 text-center">
                <Button variant="outline" asChild>
                  <a href="/student/achievements">
                    <Eye className="h-4 w-4 mr-2" />
                    View All Achievements
                  </a>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
