"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel
} from "@/components/ui/dropdown-menu"
import { 
  Download, 
  FileText, 
  Award, 
  BarChart3, 
  Loader2,
  CheckCircle,
  AlertCircle
} from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import { toast } from "sonner"
import {
  generateQuizResultPDF,
  generateAnalyticsReportPDF,
  generateCertificatePDF,
  QuizAttemptData,
  AnalyticsData,
  CertificateData
} from "@/lib/enhanced-pdf-generator"

// Utility function to download a blob as a file
const downloadPDF = (blob: Blob, filename: string) => {
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
}

interface PDFExportButtonProps {
  type: 'quiz-result' | 'analytics' | 'certificate' | 'element' | 'multiple'
  data?: QuizAttemptData | AnalyticsData | any
  elementId?: string
  filename?: string
  variant?: 'default' | 'outline' | 'ghost'
  size?: 'sm' | 'default' | 'lg'
  className?: string
  children?: React.ReactNode
}

export function PDFExportButton({
  type,
  data,
  elementId,
  filename,
  variant = 'default',
  size = 'default',
  className = '',
  children
}: PDFExportButtonProps) {
  const [isExporting, setIsExporting] = useState(false)
  const [exportStatus, setExportStatus] = useState<'idle' | 'success' | 'error'>('idle')

  const getIcon = () => {
    switch (type) {
      case 'quiz-result':
        return <FileText className="h-4 w-4" />
      case 'analytics':
        return <BarChart3 className="h-4 w-4" />
      case 'certificate':
        return <Award className="h-4 w-4" />
      default:
        return <Download className="h-4 w-4" />
    }
  }

  const getDefaultFilename = () => {
    const timestamp = new Date().toISOString().split('T')[0]
    switch (type) {
      case 'quiz-result':
        return `quiz-result-${timestamp}.pdf`
      case 'analytics':
        return `analytics-report-${timestamp}.pdf`
      case 'certificate':
        return `certificate-${timestamp}.pdf`
      default:
        return `document-${timestamp}.pdf`
    }
  }

  const exportPDF = async (exportType: string) => {
    setIsExporting(true)
    setExportStatus('idle')

    try {
      let response: Response
      let fileName = filename || getDefaultFilename()

      switch (exportType) {
        case 'quiz-result':
          if (!data || !(data as any).attemptId) {
            throw new Error('Quiz attempt ID is required')
          }
          response = await fetch(`/api/student/quiz-results/${(data as any).attemptId}/pdf`)
          break

        case 'analytics':
          // Use the student analytics PDF endpoint for immediate download
          response = await fetch('/api/student/analytics/pdf')
          break

        case 'certificate':
          if (!data) throw new Error('Certificate data is required')
          // Use client-side generation for certificates
          const blob = await generateCertificatePDF(data as CertificateData)
          downloadPDF(blob, `certificate-${(data as any).studentName?.replace(/\s+/g, '-') || 'student'}-${new Date().toISOString().split('T')[0]}.pdf`)
          setExportStatus('success')
          toast.success('Certificate exported successfully!')
          return

        case 'element':
          throw new Error('Element capture not yet implemented')

        default:
          throw new Error('Invalid export type')
      }

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to generate PDF')
      }

      // Get filename from response headers if available
      const contentDisposition = response.headers.get('content-disposition')
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+)"/)
        if (filenameMatch) {
          fileName = filenameMatch[1]
        }
      }

      // Convert response to blob and download
      const blob = await response.blob()
      downloadPDF(blob, fileName)
      setExportStatus('success')
      toast.success('PDF exported successfully!')

    } catch (error) {
      console.error('PDF export error:', error)
      setExportStatus('error')
      toast.error(error instanceof Error ? error.message : 'Failed to export PDF')
    } finally {
      setIsExporting(false)

      // Reset status after 3 seconds
      setTimeout(() => {
        setExportStatus('idle')
      }, 3000)
    }
  }

  const getStatusIcon = () => {
    if (isExporting) return <Loader2 className="h-4 w-4 animate-spin" />
    if (exportStatus === 'success') return <CheckCircle className="h-4 w-4 text-green-600" />
    if (exportStatus === 'error') return <AlertCircle className="h-4 w-4 text-red-600" />
    return getIcon()
  }

  const getButtonText = () => {
    if (isExporting) return 'Exporting...'
    if (exportStatus === 'success') return 'Exported!'
    if (exportStatus === 'error') return 'Export Failed'
    return children || 'Export PDF'
  }

  const getButtonVariant = () => {
    if (exportStatus === 'success') return 'default'
    if (exportStatus === 'error') return 'destructive'
    return variant
  }

  const getButtonClassName = () => {
    let baseClass = className
    if (exportStatus === 'success') {
      baseClass += ' bg-green-600 hover:bg-green-700 border-green-600'
    } else if (exportStatus === 'error') {
      baseClass += ' bg-red-600 hover:bg-red-700 border-red-600'
    }
    return baseClass
  }

  // Simple button for single export type
  if (type !== 'multiple') {
    return (
      <Button
        variant={getButtonVariant()}
        size={size}
        className={getButtonClassName()}
        onClick={() => exportPDF(type)}
        disabled={isExporting}
      >
        {getStatusIcon()}
        {size !== 'sm' && <span className="ml-2">{getButtonText()}</span>}
      </Button>
    )
  }

  // Dropdown for multiple export options
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant={getButtonVariant()}
          size={size}
          className={getButtonClassName()}
          disabled={isExporting}
        >
          {getStatusIcon()}
          {size !== 'sm' && <span className="ml-2">{getButtonText()}</span>}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuLabel>Export Options</DropdownMenuLabel>
        <DropdownMenuSeparator />
        
        <DropdownMenuItem onClick={() => exportPDF('quiz-result')}>
          <FileText className="h-4 w-4 mr-2" />
          Quiz Result Report
        </DropdownMenuItem>
        
        <DropdownMenuItem onClick={() => exportPDF('analytics')}>
          <BarChart3 className="h-4 w-4 mr-2" />
          Analytics Report
        </DropdownMenuItem>
        
        <DropdownMenuItem onClick={() => exportPDF('certificate')}>
          <Award className="h-4 w-4 mr-2" />
          Certificate
        </DropdownMenuItem>
        
        <DropdownMenuSeparator />
        
        <DropdownMenuItem onClick={() => exportPDF('element')}>
          <Download className="h-4 w-4 mr-2" />
          Current View
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

// Specialized export buttons
export function QuizResultExportButton({
  attemptId,
  variant = 'outline',
  size = 'sm',
  className = ''
}: {
  attemptId: string
  variant?: 'default' | 'outline' | 'ghost'
  size?: 'sm' | 'default' | 'lg'
  className?: string
}) {
  return (
    <PDFExportButton
      type="quiz-result"
      data={{ attemptId }}
      variant={variant}
      size={size}
      className={className}
    >
      Export Result
    </PDFExportButton>
  )
}

export function AnalyticsExportButton({
  variant = 'outline',
  size = 'sm',
  className = ''
}: {
  variant?: 'default' | 'outline' | 'ghost'
  size?: 'sm' | 'default' | 'lg'
  className?: string
}) {
  return (
    <PDFExportButton
      type="analytics"
      data={{}} // No data needed - API will fetch from current user session
      variant={variant}
      size={size}
      className={className}
    >
      Export Analytics
    </PDFExportButton>
  )
}

export function CertificateExportButton({ 
  certificateData,
  variant = 'default', 
  size = 'default',
  className = '' 
}: { 
  certificateData: {
    studentName: string
    quizTitle: string
    score: number
    completedAt: string
    certificateId: string
  }
  variant?: 'default' | 'outline' | 'ghost'
  size?: 'sm' | 'default' | 'lg'
  className?: string
}) {
  return (
    <PDFExportButton
      type="certificate"
      data={certificateData}
      variant={variant}
      size={size}
      className={className}
    >
      <Award className="h-4 w-4 mr-2" />
      Download Certificate
    </PDFExportButton>
  )
}

// PDF Export Status Card
export function PDFExportStatus({ 
  exports 
}: { 
  exports: Array<{
    id: string
    type: string
    filename: string
    status: 'pending' | 'processing' | 'completed' | 'failed'
    createdAt: Date
    downloadUrl?: string
  }>
}) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Download className="h-5 w-5" />
          PDF Exports
        </CardTitle>
        <CardDescription>
          Track your PDF export history and downloads
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <AnimatePresence>
            {exports.map((exportItem) => (
              <motion.div
                key={exportItem.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="flex items-center justify-between p-3 border rounded-lg"
              >
                <div className="flex items-center gap-3">
                  <div className="flex-shrink-0">
                    {exportItem.type === 'quiz-result' && <FileText className="h-4 w-4 text-blue-600" />}
                    {exportItem.type === 'analytics' && <BarChart3 className="h-4 w-4 text-green-600" />}
                    {exportItem.type === 'certificate' && <Award className="h-4 w-4 text-yellow-600" />}
                  </div>
                  <div>
                    <p className="font-medium text-sm">{exportItem.filename}</p>
                    <p className="text-xs text-muted-foreground">
                      {exportItem.createdAt.toLocaleString()}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <Badge variant={
                    exportItem.status === 'completed' ? 'default' :
                    exportItem.status === 'processing' ? 'secondary' :
                    exportItem.status === 'failed' ? 'destructive' : 'outline'
                  }>
                    {exportItem.status}
                  </Badge>
                  
                  {exportItem.status === 'completed' && exportItem.downloadUrl && (
                    <Button variant="ghost" size="sm" asChild>
                      <a href={exportItem.downloadUrl} download>
                        <Download className="h-3 w-3" />
                      </a>
                    </Button>
                  )}
                </div>
              </motion.div>
            ))}
          </AnimatePresence>
          
          {exports.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              <Download className="h-12 w-12 mx-auto mb-3 opacity-50" />
              <p>No PDF exports yet</p>
              <p className="text-sm">Your exported PDFs will appear here</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
