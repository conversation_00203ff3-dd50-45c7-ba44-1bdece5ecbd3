"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { 
  Search, 
  BookOpen, 
  Code, 
  Database, 
  Smartphone,
  Palette,
  Calculator,
  Atom,
  Globe,
  TrendingUp,
  Users,
  Star,
  ChevronRight
} from "lucide-react"
import { motion } from "framer-motion"
import Link from "next/link"

interface Category {
  id: string
  name: string
  description: string
  icon: React.ComponentType<any>
  color: string
  quizCount: number
  totalStudents: number
  averageRating: number
  subcategories: string[]
  featured: boolean
}

export default function QuizCategories() {
  const [searchQuery, setSearchQuery] = useState("")

  const categories: Category[] = [
    {
      id: "programming",
      name: "Programming",
      description: "Learn programming languages, algorithms, and software development concepts",
      icon: Code,
      color: "from-blue-500 to-blue-600",
      quizCount: 156,
      totalStudents: 12450,
      averageRating: 4.7,
      subcategories: ["JavaScript", "Python", "Java", "C++", "Go"],
      featured: true
    },
    {
      id: "web-development",
      name: "Web Development",
      description: "Master frontend and backend web development technologies",
      icon: Globe,
      color: "from-green-500 to-green-600",
      quizCount: 89,
      totalStudents: 8930,
      averageRating: 4.6,
      subcategories: ["HTML/CSS", "React", "Node.js", "Vue.js", "Angular"],
      featured: true
    },
    {
      id: "data-science",
      name: "Data Science",
      description: "Explore data analysis, machine learning, and statistical concepts",
      icon: TrendingUp,
      color: "from-purple-500 to-purple-600",
      quizCount: 67,
      totalStudents: 5670,
      averageRating: 4.8,
      subcategories: ["Python", "R", "SQL", "Machine Learning", "Statistics"],
      featured: true
    },
    {
      id: "mobile-development",
      name: "Mobile Development",
      description: "Build mobile applications for iOS and Android platforms",
      icon: Smartphone,
      color: "from-orange-500 to-orange-600",
      quizCount: 45,
      totalStudents: 3450,
      averageRating: 4.5,
      subcategories: ["React Native", "Flutter", "Swift", "Kotlin", "Xamarin"],
      featured: false
    },
    {
      id: "database",
      name: "Database",
      description: "Learn database design, SQL, and data management systems",
      icon: Database,
      color: "from-red-500 to-red-600",
      quizCount: 38,
      totalStudents: 2890,
      averageRating: 4.4,
      subcategories: ["SQL", "MongoDB", "PostgreSQL", "Redis", "Elasticsearch"],
      featured: false
    },
    {
      id: "design",
      name: "Design",
      description: "UI/UX design principles, tools, and creative processes",
      icon: Palette,
      color: "from-pink-500 to-pink-600",
      quizCount: 34,
      totalStudents: 2340,
      averageRating: 4.6,
      subcategories: ["UI/UX", "Figma", "Adobe XD", "Sketch", "Prototyping"],
      featured: false
    },
    {
      id: "mathematics",
      name: "Mathematics",
      description: "Mathematical concepts, calculus, algebra, and problem solving",
      icon: Calculator,
      color: "from-indigo-500 to-indigo-600",
      quizCount: 78,
      totalStudents: 4560,
      averageRating: 4.3,
      subcategories: ["Algebra", "Calculus", "Statistics", "Geometry", "Discrete Math"],
      featured: false
    },
    {
      id: "science",
      name: "Science",
      description: "Physics, chemistry, biology, and scientific methodology",
      icon: Atom,
      color: "from-teal-500 to-teal-600",
      quizCount: 92,
      totalStudents: 6780,
      averageRating: 4.5,
      subcategories: ["Physics", "Chemistry", "Biology", "Earth Science", "Astronomy"],
      featured: false
    }
  ]

  const filteredCategories = categories.filter(category =>
    category.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    category.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
    category.subcategories.some(sub => 
      sub.toLowerCase().includes(searchQuery.toLowerCase())
    )
  )

  const featuredCategories = filteredCategories.filter(cat => cat.featured)
  const otherCategories = filteredCategories.filter(cat => !cat.featured)

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-3 w-3 ${
          i < Math.floor(rating) ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'
        }`}
      />
    ))
  }

  return (
    <div className="p-6 space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Explore Quiz Categories
          </h1>
          <p className="text-lg text-muted-foreground mt-2">
            Discover quizzes organized by subject and skill level
          </p>
        </motion.div>
      </div>

      {/* Search */}
      <div className="max-w-md mx-auto">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Search categories..."
            className="pl-10"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>

      {/* Featured Categories */}
      {featuredCategories.length > 0 && (
        <div className="space-y-6">
          <div className="flex items-center gap-2">
            <Star className="h-5 w-5 text-yellow-500" />
            <h2 className="text-2xl font-bold">Featured Categories</h2>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {featuredCategories.map((category, index) => (
              <motion.div
                key={category.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 * index }}
              >
                <Card className="hover:shadow-lg transition-all duration-300 group cursor-pointer h-full">
                  <Link href={`/student/browse?category=${category.id}`}>
                    <CardContent className="pt-6">
                      <div className="space-y-4">
                        {/* Icon and Title */}
                        <div className="flex items-center gap-4">
                          <div className={`w-12 h-12 rounded-lg bg-gradient-to-br ${category.color} flex items-center justify-center group-hover:scale-110 transition-transform`}>
                            <category.icon className="h-6 w-6 text-white" />
                          </div>
                          <div className="flex-1">
                            <h3 className="text-xl font-semibold group-hover:text-primary transition-colors">
                              {category.name}
                            </h3>
                            <div className="flex items-center gap-1 mt-1">
                              {renderStars(category.averageRating)}
                              <span className="text-xs text-muted-foreground ml-1">
                                {category.averageRating}
                              </span>
                            </div>
                          </div>
                        </div>

                        {/* Description */}
                        <p className="text-sm text-muted-foreground line-clamp-2">
                          {category.description}
                        </p>

                        {/* Stats */}
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div className="flex items-center gap-2">
                            <BookOpen className="h-4 w-4 text-muted-foreground" />
                            <span>{category.quizCount} quizzes</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Users className="h-4 w-4 text-muted-foreground" />
                            <span>{category.totalStudents.toLocaleString()} students</span>
                          </div>
                        </div>

                        {/* Subcategories */}
                        <div className="space-y-2">
                          <p className="text-xs font-medium text-muted-foreground">Popular Topics:</p>
                          <div className="flex flex-wrap gap-1">
                            {category.subcategories.slice(0, 3).map((sub) => (
                              <Badge key={sub} variant="secondary" className="text-xs">
                                {sub}
                              </Badge>
                            ))}
                            {category.subcategories.length > 3 && (
                              <Badge variant="secondary" className="text-xs">
                                +{category.subcategories.length - 3} more
                              </Badge>
                            )}
                          </div>
                        </div>

                        {/* Action */}
                        <div className="flex items-center justify-between pt-2">
                          <span className="text-sm font-medium text-primary">
                            Explore Category
                          </span>
                          <ChevronRight className="h-4 w-4 text-primary group-hover:translate-x-1 transition-transform" />
                        </div>
                      </div>
                    </CardContent>
                  </Link>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      )}

      {/* All Categories */}
      {otherCategories.length > 0 && (
        <div className="space-y-6">
          <h2 className="text-2xl font-bold">All Categories</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {otherCategories.map((category, index) => (
              <motion.div
                key={category.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: 0.1 * index }}
              >
                <Card className="hover:shadow-md transition-shadow group cursor-pointer">
                  <Link href={`/student/browse?category=${category.id}`}>
                    <CardContent className="pt-4">
                      <div className="flex items-center gap-4">
                        <div className={`w-10 h-10 rounded-lg bg-gradient-to-br ${category.color} flex items-center justify-center group-hover:scale-110 transition-transform`}>
                          <category.icon className="h-5 w-5 text-white" />
                        </div>
                        
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <h3 className="font-semibold group-hover:text-primary transition-colors">
                              {category.name}
                            </h3>
                            <ChevronRight className="h-4 w-4 text-muted-foreground group-hover:text-primary group-hover:translate-x-1 transition-all" />
                          </div>
                          
                          <p className="text-sm text-muted-foreground line-clamp-1 mt-1">
                            {category.description}
                          </p>
                          
                          <div className="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
                            <span>{category.quizCount} quizzes</span>
                            <span>{category.totalStudents.toLocaleString()} students</span>
                            <div className="flex items-center gap-1">
                              {renderStars(category.averageRating)}
                              <span>{category.averageRating}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Link>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      )}

      {/* No Results */}
      {filteredCategories.length === 0 && (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-12">
              <Search className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">No categories found</h3>
              <p className="text-muted-foreground mb-6">
                Try adjusting your search terms to find relevant categories.
              </p>
              <Button onClick={() => setSearchQuery("")}>
                Clear Search
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {categories.reduce((sum, cat) => sum + cat.quizCount, 0)}
              </div>
              <p className="text-xs text-muted-foreground">Total Quizzes</p>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {categories.length}
              </div>
              <p className="text-xs text-muted-foreground">Categories</p>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {categories.reduce((sum, cat) => sum + cat.totalStudents, 0).toLocaleString()}
              </div>
              <p className="text-xs text-muted-foreground">Active Students</p>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {(categories.reduce((sum, cat) => sum + cat.averageRating, 0) / categories.length).toFixed(1)}
              </div>
              <p className="text-xs text-muted-foreground">Avg Rating</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
