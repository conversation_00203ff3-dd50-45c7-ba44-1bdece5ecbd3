"use client"

import Link from "next/link"
import { UserMenu } from "./user-menu"
import { But<PERSON> } from "./ui/button"
import { Sheet, <PERSON><PERSON><PERSON>ontent, Sheet<PERSON>rigger } from "./ui/sheet"
import { ScrollArea } from "./ui/scroll-area"
import { FileText, Home, BookOpen, Shield, Users, BarChart3, Menu, Search } from "lucide-react"
import { useSession } from "next-auth/react"
import { useState } from "react"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"

export function Header() {
  const { data: session } = useSession()
  const pathname = usePathname()
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const isAdmin = session?.user?.role === 'ADMIN'
  const isStudent = session?.user?.role === 'STUDENT'

  // Check if we're on pages that have their own sidebar
  const isStudentPage = pathname?.startsWith('/student')
  const isAdminPage = pathname?.startsWith('/admin')

  const navigationItems = [
    {
      href: "/",
      label: "Home",
      icon: Home,
      show: true
    },
    // {
    //   href: "/quiz-generator",
    //   label: "PDF Quiz Generator",
    //   icon: FileText,
    //   show: true
    // },
    {
      href: "/student",
      label: "Dashboard",
      icon: BookOpen,
      show: isStudent
    },
    {
      href: "/student/browse",
      label: "Browse Quizzes",
      icon: FileText,
      show: isStudent
    },
    {
      href: "/admin",
      label: "Admin Panel",
      icon: Shield,
      show: isAdmin
    },
    {
      href: "/admin/quizzes",
      label: "Manage Quizzes",
      icon: FileText,
      show: isAdmin
    },
    {
      href: "/admin/users",
      label: "User Management",
      icon: Users,
      show: isAdmin
    },
    {
      href: "/admin/analytics-dashboard",
      label: "Analytics",
      icon: BarChart3,
      show: isAdmin
    }
  ].filter(item => item.show)

  return (
    <header className={cn(
      "sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",
      isStudentPage && "md:pl-64", // Add left padding to account for student sidebar (256px)
      isAdminPage && "md:pl-80" // Add left padding to account for admin sidebar (320px)
    )}>
      <div className="container flex h-16 items-center px-4">
        {/* Mobile Menu Button */}
        <Sheet open={mobileMenuOpen} onOpenChange={setMobileMenuOpen}>
          <SheetTrigger asChild>
            <Button
              variant="ghost"
              className="mr-2 px-0 text-base hover:bg-transparent focus-visible:bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 md:hidden"
            >
              <Menu className="h-6 w-6" />
              <span className="sr-only">Toggle Menu</span>
            </Button>
          </SheetTrigger>
          <SheetContent side="left" className="w-80 pr-0">
            <div className="px-6 py-4">
              <Link
                href="/"
                className="flex items-center space-x-2"
                onClick={() => setMobileMenuOpen(false)}
              >
                <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                  <BookOpen className="h-5 w-5 text-white" />
                </div>
                <span className="font-bold text-lg bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  QuizMaster
                </span>
              </Link>
            </div>
            <ScrollArea className="my-4 h-[calc(100vh-8rem)] px-6">
              <div className="space-y-2">
                {navigationItems.map((item) => (
                  <Link
                    key={item.href}
                    href={item.href}
                    onClick={() => setMobileMenuOpen(false)}
                    className="flex items-center space-x-3 rounded-lg px-3 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground transition-colors"
                  >
                    <item.icon className="h-4 w-4" />
                    <span>{item.label}</span>
                  </Link>
                ))}
              </div>
            </ScrollArea>
          </SheetContent>
        </Sheet>

        {/* Logo */}
        <Link href="/" className="mr-6 flex items-center space-x-2">
          <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
            <BookOpen className="h-5 w-5 text-white" />
          </div>
          <span className="hidden font-bold sm:inline-block bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            QuizMaster
          </span>
        </Link>

        {/* Desktop Navigation */}
        <nav className="hidden md:flex items-center space-x-6 text-sm font-medium">
          {navigationItems.slice(0, 4).map((item) => (
            <Link
              key={item.href}
              href={item.href}
              className="flex items-center space-x-1 transition-colors hover:text-foreground/80 text-foreground/60"
            >
              <item.icon className="h-4 w-4" />
              <span>{item.label}</span>
            </Link>
          ))}
          {isAdmin && navigationItems.length > 4 && (
            <div className="flex items-center space-x-4">
              <div className="h-4 w-px bg-border" />
              {navigationItems.slice(4).map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className="flex items-center space-x-1 transition-colors hover:text-foreground/80 text-foreground/60"
                >
                  <item.icon className="h-4 w-4" />
                  <span className="hidden lg:inline">{item.label}</span>
                </Link>
              ))}
            </div>
          )}
        </nav>

        {/* Right Side */}
        <div className="flex flex-1 items-center justify-end space-x-4">


          {/* User Menu */}
          <UserMenu />
        </div>
      </div>
    </header>
  )
}
