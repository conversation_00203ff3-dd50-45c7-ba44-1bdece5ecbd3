import { NextRequest, NextResponse } from 'next/server'

import { prisma } from '@/lib/prisma'
import { auth } from '@/auth'

// GET /api/admin/scheduled-quizzes/[id]/participants - Get participants for a scheduled quiz
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth()
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { id } = await params

    // Get the scheduled quiz with enrollments
    const scheduledQuiz = await prisma.scheduledQuiz.findUnique({
      where: { id },
      include: {
        quiz: {
          select: {
            id: true,
            title: true,
            type: true
          }
        },
        enrollments: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                image: true
              }
            }
          }
        }
      }
    })

    if (!scheduledQuiz) {
      return NextResponse.json(
        { error: 'Scheduled quiz not found' },
        { status: 404 }
      )
    }

    // Get all attempts for this quiz from enrolled users
    const userIds = (scheduledQuiz as any).enrollments?.map((e: any) => e.user.id) || []
    const attempts = await prisma.quizAttempt.findMany({
      where: {
        quizId: scheduledQuiz.quizId,
        userId: { in: userIds }
      },
      orderBy: {
        startedAt: 'desc'
      }
    })

    // Format participants data
    const participants = ((scheduledQuiz as any).enrollments || []).map((enrollment: any) => {
      // Find the latest attempt for this user
      const userAttempts = attempts.filter(a => a.userId === enrollment.user.id)
      const latestAttempt = userAttempts[0]

      return {
        id: enrollment.user.id,
        name: enrollment.user.name,
        email: enrollment.user.email,
        image: enrollment.user.image,
        enrolledAt: enrollment.enrolledAt,
        status: latestAttempt?.isCompleted ? 'completed' :
                latestAttempt ? 'in_progress' : 'not_started',
        score: latestAttempt?.score || 0,
        percentage: latestAttempt?.percentage || 0,
        completedAt: latestAttempt?.completedAt,
        timeSpent: latestAttempt?.timeSpent || 0,
        attemptId: latestAttempt?.id
      }
    })

    // Calculate statistics
    const stats = {
      totalEnrolled: participants.length,
      completed: participants.filter((p: { status: string }) => p.status === 'completed').length,
      inProgress: participants.filter((p: { status: string }) => p.status === 'in_progress').length,
      notStarted: participants.filter((p: { status: string }) => p.status === 'not_started').length,
      averageScore: participants.length > 0 
        ? participants.reduce((sum: any, p: { percentage: any }) => sum + p.percentage, 0) / participants.length 
        : 0
    }

    return NextResponse.json({
      scheduledQuiz: {
        id: scheduledQuiz.id,
        title: scheduledQuiz.title,
        startTime: scheduledQuiz.startTime,
        endTime: scheduledQuiz.endTime,
        quiz: (scheduledQuiz as any).quiz
      },
      participants,
      stats,
      success: true
    })
  } catch (error) {
    console.error('Error fetching participants:', error)
    return NextResponse.json(
      { error: 'Failed to fetch participants' },
      { status: 500 }
    )
  }
}
