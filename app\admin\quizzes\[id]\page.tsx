"use client"

import { useState, useEffect } from "react"
import { useParams } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { 
  ArrowLeft, 
  Edit, 
  Trash2, 
  Globe, 
  Lock, 
  Users, 
  FileText, 
  Clock,
  Calendar,
  BarChart3,
  Download,
  Copy,
  Settings,
  Eye,
  CheckCircle,
  XCircle,
  TrendingUp
} from "lucide-react"
import Link from "next/link"
import { toast } from "sonner"

interface QuizDetails {
  id: string
  title: string
  description?: string
  type: string
  difficulty: string
  tags: string[]
  timeLimit?: number
  maxAttempts: number
  passingScore?: number
  instructions?: string
  isPublished: boolean
  createdAt: string
  updatedAt: string
  creator: {
    name: string
    email: string
  }
  questions: Array<{
    id: string
    type: string
    text: string
    options: string[]
    correctAnswer: string
    explanation?: string
    points: number
    order: number
  }>
  attempts: Array<{
    id: string
    user: {
      name: string
      email: string
    }
    score: number
    percentage: number
    timeSpent?: number
    isCompleted: boolean
    startedAt: string
    completedAt?: string
  }>
  stats: {
    totalAttempts: number
    averageScore: number
    completionRate: number
  }
}

export default function QuizDetailsPage() {
  const params = useParams()
  const [quiz, setQuiz] = useState<QuizDetails | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchQuizDetails()
  }, [params.id])

  const fetchQuizDetails = async () => {
    try {
      const response = await fetch(`/api/admin/quizzes/${params.id}`)

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        console.error('API Error:', response.status, errorData)
        throw new Error(errorData.error || `HTTP ${response.status}: Failed to fetch quiz details`)
      }

      const data = await response.json()
      console.log('Quiz data received:', data)
      setQuiz(data)
    } catch (error) {
      console.error('Error fetching quiz:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to load quiz details')
    } finally {
      setLoading(false)
    }
  }

  const handleTogglePublish = async () => {
    if (!quiz) return
    
    try {
      const response = await fetch(`/api/admin/quizzes/${quiz.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ isPublished: !quiz.isPublished })
      })
      
      if (!response.ok) throw new Error('Failed to update quiz')
      
      setQuiz(prev => prev ? { ...prev, isPublished: !prev.isPublished } : null)
      toast.success(quiz.isPublished ? 'Quiz unpublished' : 'Quiz published')
    } catch (error) {
      toast.error('Failed to update quiz status')
    }
  }

  const handleDeleteQuiz = async () => {
    if (!quiz) return
    
    if (!confirm('Are you sure you want to delete this quiz? This action cannot be undone.')) {
      return
    }
    
    try {
      const response = await fetch(`/api/admin/quizzes/${quiz.id}`, { method: 'DELETE' })
      if (!response.ok) throw new Error('Failed to delete quiz')
      
      toast.success('Quiz deleted successfully')
      window.location.href = '/admin/quizzes'
    } catch (error) {
      toast.error('Failed to delete quiz')
    }
  }

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-muted rounded w-1/4"></div>
          <div className="h-32 bg-muted rounded"></div>
          <div className="h-64 bg-muted rounded"></div>
        </div>
      </div>
    )
  }

  if (!quiz) {
    return (
      <div className="p-6">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-12">
              <XCircle className="h-16 w-16 text-destructive mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">Quiz Not Found</h3>
              <p className="text-muted-foreground mb-6">
                The quiz you're looking for doesn't exist or has been deleted.
              </p>
              <Button asChild>
                <Link href="/admin/quizzes">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Quizzes
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href={
              quiz.type === 'TEST_SERIES' ? '/admin/quizzes/test-series' :
              quiz.type === 'DAILY_PRACTICE' ? '/admin/quizzes/daily-practice' :
              '/admin/quizzes'
            }>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to {
                quiz.type === 'TEST_SERIES' ? 'Test Series' :
                quiz.type === 'DAILY_PRACTICE' ? 'Daily Practice' :
                'Quizzes'
              }
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold">{quiz.title}</h1>
            <p className="text-muted-foreground mt-1">
              Created by {quiz.creator.name} on {new Date(quiz.createdAt).toLocaleDateString()}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={handleTogglePublish}>
            {quiz.isPublished ? (
              <>
                <Lock className="h-4 w-4 mr-2" />
                Unpublish
              </>
            ) : (
              <>
                <Globe className="h-4 w-4 mr-2" />
                Publish
              </>
            )}
          </Button>
          <Button variant="outline" asChild>
            <Link href={`/admin/quizzes/${quiz.id}/edit`}>
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Link>
          </Button>
          <Button variant="destructive" onClick={handleDeleteQuiz}>
            <Trash2 className="h-4 w-4 mr-2" />
            Delete
          </Button>
        </div>
      </div>

      {/* Quiz Info Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-full">
                <FileText className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <div className="text-2xl font-bold">{quiz.questions.length}</div>
                <p className="text-xs text-muted-foreground">Questions</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-full">
                <Users className="h-5 w-5 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <div className="text-2xl font-bold">{quiz.stats.totalAttempts}</div>
                <p className="text-xs text-muted-foreground">Attempts</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-full">
                <TrendingUp className="h-5 w-5 text-purple-600 dark:text-purple-400" />
              </div>
              <div>
                <div className="text-2xl font-bold">{quiz.stats.averageScore}%</div>
                <p className="text-xs text-muted-foreground">Avg Score</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-orange-100 dark:bg-orange-900/30 rounded-full">
                <CheckCircle className="h-5 w-5 text-orange-600 dark:text-orange-400" />
              </div>
              <div>
                <div className="text-2xl font-bold">{quiz.stats.completionRate}%</div>
                <p className="text-xs text-muted-foreground">Completion</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="questions">Questions</TabsTrigger>
          <TabsTrigger value="attempts">Attempts</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Quiz Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="font-medium">Status:</span>
                  <Badge className={quiz.isPublished ? 'bg-green-500' : 'bg-yellow-500'}>
                    {quiz.isPublished ? 'Published' : 'Draft'}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="font-medium">Type:</span>
                  <span>{quiz.type.replace('_', ' ')}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="font-medium">Difficulty:</span>
                  <Badge variant="outline">{quiz.difficulty}</Badge>
                </div>
                {quiz.timeLimit && (
                  <div className="flex items-center justify-between">
                    <span className="font-medium">Time Limit:</span>
                    <span>{quiz.timeLimit} minutes</span>
                  </div>
                )}
                <div className="flex items-center justify-between">
                  <span className="font-medium">Max Attempts:</span>
                  <span>{quiz.maxAttempts}</span>
                </div>
                {quiz.passingScore && (
                  <div className="flex items-center justify-between">
                    <span className="font-medium">Passing Score:</span>
                    <span>{quiz.passingScore}%</span>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Description & Instructions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {quiz.description && (
                  <div>
                    <h4 className="font-medium mb-2">Description:</h4>
                    <p className="text-sm text-muted-foreground">{quiz.description}</p>
                  </div>
                )}
                {quiz.instructions && (
                  <div>
                    <h4 className="font-medium mb-2">Instructions:</h4>
                    <p className="text-sm text-muted-foreground">{quiz.instructions}</p>
                  </div>
                )}
                {quiz.tags.length > 0 && (
                  <div>
                    <h4 className="font-medium mb-2">Tags:</h4>
                    <div className="flex flex-wrap gap-2">
                      {quiz.tags.map((tag) => (
                        <Badge key={tag} variant="secondary">{tag}</Badge>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="questions" className="space-y-4">
          {quiz.questions.map((question, index) => (
            <Card key={question.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-base">
                    Question {index + 1}
                  </CardTitle>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline">{question.type}</Badge>
                    <Badge variant="secondary">{question.points} pt{question.points !== 1 ? 's' : ''}</Badge>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <p className="font-medium mb-3">{question.text}</p>
                {question.type === 'MCQ' && (
                  <div className="space-y-2">
                    {question.options.map((option, optionIndex) => (
                      <div
                        key={optionIndex}
                        className={`p-3 rounded-lg border ${
                          option === question.correctAnswer
                            ? 'bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800'
                            : 'bg-muted/50 border-muted'
                        }`}
                      >
                        <div className="flex items-center justify-between">
                          <span>{String.fromCharCode(65 + optionIndex)}. {option}</span>
                          {option === question.correctAnswer && (
                            <Badge className="bg-green-500">Correct</Badge>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
                {question.explanation && (
                  <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200 dark:bg-blue-900/20 dark:border-blue-800">
                    <p className="text-sm">
                      <strong className="text-blue-800 dark:text-blue-200">Explanation:</strong>{' '}
                      <span className="text-blue-700 dark:text-blue-300">{question.explanation}</span>
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        <TabsContent value="attempts" className="space-y-4">
          {quiz.attempts.length === 0 ? (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center py-8">
                  <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No attempts yet</h3>
                  <p className="text-muted-foreground">
                    Students haven't taken this quiz yet.
                  </p>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {quiz.attempts.map((attempt) => (
                <Card key={attempt.id}>
                  <CardContent className="pt-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-semibold">{attempt.user.name}</h4>
                        <p className="text-sm text-muted-foreground">{attempt.user.email}</p>
                        <p className="text-xs text-muted-foreground mt-1">
                          Started: {new Date(attempt.startedAt).toLocaleString()}
                          {attempt.completedAt && (
                            <> • Completed: {new Date(attempt.completedAt).toLocaleString()}</>
                          )}
                        </p>
                      </div>
                      <div className="text-right">
                        <div className="text-2xl font-bold text-green-600">
                          {attempt.score}/{quiz.questions.reduce((sum, q) => sum + q.points, 0)}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {attempt.percentage}%
                        </div>
                        {attempt.timeSpent && (
                          <div className="text-xs text-muted-foreground">
                            {Math.floor(attempt.timeSpent / 60)}m {attempt.timeSpent % 60}s
                          </div>
                        )}
                        <Badge className={attempt.isCompleted ? 'bg-green-500' : 'bg-yellow-500'}>
                          {attempt.isCompleted ? 'Completed' : 'In Progress'}
                        </Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="analytics">
          <Card>
            <CardHeader>
              <CardTitle>Analytics Dashboard</CardTitle>
              <CardDescription>
                Detailed performance metrics and insights
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <BarChart3 className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-2">Analytics Coming Soon</h3>
                <p className="text-muted-foreground">
                  Detailed analytics and reporting features will be available here.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
