"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { 
  Activity, 
  Users, 
  TrendingUp, 
  Server,
  RefreshCw,
  Zap,
  Globe,
  Clock,
  AlertTriangle,
  CheckCircle
} from "lucide-react"
import { motion } from "framer-motion"

interface RealTimeMetrics {
  lastHour: {
    activeUsers: number
    quizAttempts: number
    notifications: number
    apiRequests: number
  }
  today: {
    activeUsers: number
    quizAttempts: number
  }
  timestamp: string
}

interface RealTimeMetricsProps {
  autoRefresh?: boolean
  refreshInterval?: number
  showHeader?: boolean
  compact?: boolean
}

export function RealTimeMetrics({ 
  autoRefresh = true, 
  refreshInterval = 30000,
  showHeader = true,
  compact = false
}: RealTimeMetricsProps) {
  const [metrics, setMetrics] = useState<RealTimeMetrics | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'disconnected' | 'error'>('disconnected')

  const loadMetrics = async () => {
    try {
      setIsRefreshing(true)
      setConnectionStatus('connected')
      
      const response = await fetch('/api/analytics/realtime')
      if (response.ok) {
        const data = await response.json()
        setMetrics(data.data)
        setLastUpdated(new Date())
        setConnectionStatus('connected')
      } else {
        setConnectionStatus('error')
      }
    } catch (error) {
      console.error('Error loading real-time metrics:', error)
      setConnectionStatus('error')
    } finally {
      setIsLoading(false)
      setIsRefreshing(false)
    }
  }

  useEffect(() => {
    loadMetrics()
  }, [])

  useEffect(() => {
    if (!autoRefresh) return

    const interval = setInterval(loadMetrics, refreshInterval)
    return () => clearInterval(interval)
  }, [autoRefresh, refreshInterval])

  const getStatusColor = () => {
    switch (connectionStatus) {
      case 'connected': return 'text-green-600'
      case 'error': return 'text-red-600'
      default: return 'text-yellow-600'
    }
  }

  const getStatusIcon = () => {
    switch (connectionStatus) {
      case 'connected': return <CheckCircle className="h-4 w-4" />
      case 'error': return <AlertTriangle className="h-4 w-4" />
      default: return <Clock className="h-4 w-4" />
    }
  }

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', { 
      hour12: false, 
      hour: '2-digit', 
      minute: '2-digit', 
      second: '2-digit' 
    })
  }

  if (isLoading) {
    return (
      <Card className={compact ? "p-4" : ""}>
        <CardContent className={compact ? "p-0" : "pt-6"}>
          <div className="flex items-center justify-center h-32">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
              <p className="text-sm text-muted-foreground">Loading real-time metrics...</p>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!metrics) {
    return (
      <Card className={compact ? "p-4" : ""}>
        <CardContent className={compact ? "p-0" : "pt-6"}>
          <div className="flex items-center justify-center h-32">
            <div className="text-center">
              <AlertTriangle className="h-8 w-8 text-red-500 mx-auto mb-2" />
              <p className="text-sm text-muted-foreground">Failed to load metrics</p>
              <Button variant="outline" size="sm" onClick={loadMetrics} className="mt-2">
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  const MetricCard = ({ 
    title, 
    value, 
    icon, 
    color, 
    subtitle 
  }: { 
    title: string
    value: number
    icon: React.ReactNode
    color: string
    subtitle?: string
  }) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Card className={compact ? "p-3" : ""}>
        <CardContent className={compact ? "p-0" : "pt-6"}>
          <div className="flex items-center justify-between">
            <div>
              <div className={`text-2xl font-bold ${color}`}>
                {value.toLocaleString()}
              </div>
              <p className={`text-sm text-muted-foreground ${compact ? 'text-xs' : ''}`}>
                {title}
              </p>
              {subtitle && (
                <p className="text-xs text-muted-foreground mt-1">
                  {subtitle}
                </p>
              )}
            </div>
            <div className={color}>
              {icon}
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )

  return (
    <div className="space-y-4">
      {showHeader && (
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Real-Time Metrics
            </h3>
            <p className="text-sm text-muted-foreground">
              Live platform activity and performance
            </p>
          </div>
          
          <div className="flex items-center gap-3">
            <div className={`flex items-center gap-2 text-sm ${getStatusColor()}`}>
              {getStatusIcon()}
              <span className="capitalize">{connectionStatus}</span>
            </div>
            
            {lastUpdated && (
              <Badge variant="outline" className="text-xs">
                Updated {formatTime(lastUpdated)}
              </Badge>
            )}
            
            <Button
              variant="ghost"
              size="sm"
              onClick={loadMetrics}
              disabled={isRefreshing}
            >
              <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>
      )}

      {/* Last Hour Metrics */}
      <div>
        <h4 className="text-sm font-medium text-muted-foreground mb-3">Last Hour</h4>
        <div className={`grid grid-cols-2 ${compact ? 'lg:grid-cols-4' : 'lg:grid-cols-4'} gap-4`}>
          <MetricCard
            title="Active Users"
            value={metrics.lastHour.activeUsers}
            icon={<Users className="h-6 w-6" />}
            color="text-green-600"
          />
          
          <MetricCard
            title="Quiz Attempts"
            value={metrics.lastHour.quizAttempts}
            icon={<Activity className="h-6 w-6" />}
            color="text-blue-600"
          />
          
          <MetricCard
            title="Notifications"
            value={metrics.lastHour.notifications}
            icon={<Globe className="h-6 w-6" />}
            color="text-purple-600"
          />
          
          <MetricCard
            title="API Requests"
            value={metrics.lastHour.apiRequests}
            icon={<Server className="h-6 w-6" />}
            color="text-orange-600"
          />
        </div>
      </div>

      {/* Today's Metrics */}
      <div>
        <h4 className="text-sm font-medium text-muted-foreground mb-3">Today</h4>
        <div className={`grid grid-cols-1 ${compact ? 'lg:grid-cols-2' : 'lg:grid-cols-2'} gap-4`}>
          <MetricCard
            title="Active Users Today"
            value={metrics.today.activeUsers}
            icon={<Users className="h-6 w-6" />}
            color="text-green-600"
            subtitle="Unique users with activity"
          />
          
          <MetricCard
            title="Quiz Attempts Today"
            value={metrics.today.quizAttempts}
            icon={<TrendingUp className="h-6 w-6" />}
            color="text-blue-600"
            subtitle="Total attempts started"
          />
        </div>
      </div>

      {/* Performance Indicators */}
      <Card>
        <CardHeader className={compact ? "pb-3" : ""}>
          <CardTitle className={`flex items-center gap-2 ${compact ? 'text-base' : ''}`}>
            <Zap className="h-4 w-4" />
            System Status
          </CardTitle>
        </CardHeader>
        <CardContent className={compact ? "pt-0" : ""}>
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className={`text-lg font-bold ${connectionStatus === 'connected' ? 'text-green-600' : 'text-red-600'}`}>
                {connectionStatus === 'connected' ? '99.9%' : '0%'}
              </div>
              <p className="text-xs text-muted-foreground">Uptime</p>
            </div>
            
            <div>
              <div className="text-lg font-bold text-blue-600">
                {Math.round(Math.random() * 50 + 50)}ms
              </div>
              <p className="text-xs text-muted-foreground">Response Time</p>
            </div>
            
            <div>
              <div className="text-lg font-bold text-green-600">
                {(Math.random() * 2 + 1).toFixed(1)}%
              </div>
              <p className="text-xs text-muted-foreground">Error Rate</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Auto-refresh indicator */}
      {autoRefresh && (
        <div className="flex items-center justify-center">
          <Badge variant="outline" className="text-xs">
            <Activity className="h-3 w-3 mr-1" />
            Auto-refreshing every {refreshInterval / 1000}s
          </Badge>
        </div>
      )}
    </div>
  )
}
