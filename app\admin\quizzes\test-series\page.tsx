"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { 
  Plus, 
  Search, 
  Filter,
  Eye,
  Edit,
  MoreHorizontal,
  Copy,
  Trash2,
  Globe,
  Lock,
  BarChart3,
  FileText,
  Users,
  Clock,
  Target,
  ArrowLeft
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { toast } from "sonner"
import Link from "next/link"
import { motion, AnimatePresence } from "framer-motion"
import { CategoryFilter } from "@/components/admin/category-filter"

interface Quiz {
  id: string
  title: string
  description?: string
  type: 'QUIZ' | 'TEST_SERIES' | 'DAILY_PRACTICE'
  difficulty: 'EASY' | 'MEDIUM' | 'HARD'
  tags: string[]
  questionCount: number
  attemptCount: number
  averageScore: number
  isPublished: boolean
  createdAt: string
  updatedAt: string
  creator: {
    name: string
    email: string
  }
  timeLimit?: number
  startTime?: string
  endTime?: string
  // Category fields
  subjectId?: string
  chapterId?: string
  topicId?: string
  subject?: {
    id: string
    name: string
  }
  chapter?: {
    id: string
    name: string
  }
  topic?: {
    id: string
    name: string
  }
}

export default function TestSeriesPage() {
  const [quizzes, setQuizzes] = useState<Quiz[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [filteredQuizzes, setFilteredQuizzes] = useState<Quiz[]>([])
  const [categoryFilters, setCategoryFilters] = useState<{
    subjectId?: string
    chapterId?: string
    topicId?: string
  }>({})

  useEffect(() => {
    fetchTestSeries()
  }, [])

  useEffect(() => {
    // Filter quizzes based on search query and category filters
    const filtered = quizzes.filter(quiz => {
      // Search filter
      const matchesSearch = !searchQuery || (
        quiz.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        quiz.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        quiz.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      )

      // Category filters
      const matchesSubject = !categoryFilters.subjectId || quiz.subjectId === categoryFilters.subjectId
      const matchesChapter = !categoryFilters.chapterId || quiz.chapterId === categoryFilters.chapterId
      const matchesTopic = !categoryFilters.topicId || quiz.topicId === categoryFilters.topicId

      return matchesSearch && matchesSubject && matchesChapter && matchesTopic
    })
    setFilteredQuizzes(filtered)
  }, [quizzes, searchQuery, categoryFilters])

  const fetchTestSeries = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/admin/quizzes?type=TEST_SERIES')
      
      if (!response.ok) {
        throw new Error('Failed to fetch test series')
      }
      
      const data = await response.json()
      setQuizzes(data.quizzes || [])
    } catch (error) {
      console.error('Error fetching test series:', error)
      toast.error('Failed to load test series')
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteQuiz = async (quizId: string) => {
    if (!confirm('Are you sure you want to delete this test series? This action cannot be undone.')) {
      return
    }

    try {
      const response = await fetch(`/api/admin/quizzes/${quizId}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        throw new Error('Failed to delete test series')
      }

      setQuizzes(prev => prev.filter(quiz => quiz.id !== quizId))
      toast.success('Test series deleted successfully')
    } catch (error) {
      console.error('Error deleting test series:', error)
      toast.error('Failed to delete test series')
    }
  }

  const handleTogglePublish = async (quizId: string, currentStatus: boolean) => {
    try {
      const response = await fetch(`/api/admin/quizzes/${quizId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ isPublished: !currentStatus })
      })

      if (!response.ok) {
        throw new Error('Failed to update test series')
      }

      setQuizzes(prev => prev.map(quiz => 
        quiz.id === quizId ? { ...quiz, isPublished: !currentStatus } : quiz
      ))
      
      toast.success(`Test series ${!currentStatus ? 'published' : 'unpublished'} successfully`)
    } catch (error) {
      console.error('Error updating test series:', error)
      toast.error('Failed to update test series')
    }
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'EASY':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'MEDIUM':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'HARD':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Test Series Management</h1>
            <p className="text-muted-foreground mt-1">Manage your test series</p>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <Card key={i}>
              <CardContent className="pt-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
                  <div className="h-8 bg-gray-200 rounded w-full"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/admin/quizzes">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to All Quizzes
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold">Test Series Management</h1>
            <p className="text-muted-foreground mt-1">
              Manage your test series and comprehensive assessments
            </p>
          </div>
        </div>
        <Button asChild>
          <Link href="/admin/quizzes/create?type=TEST_SERIES">
            <Plus className="h-4 w-4 mr-2" />
            Create Test Series
          </Link>
        </Button>
      </div>

      {/* Search and Filters */}
      <div className="flex items-center gap-4 flex-wrap">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search test series..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        <CategoryFilter
          selectedSubjectId={categoryFilters.subjectId}
          selectedChapterId={categoryFilters.chapterId}
          selectedTopicId={categoryFilters.topicId}
          onFilterChange={setCategoryFilters}
        />
      </div>

      {/* Content */}
      {filteredQuizzes.length === 0 ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-12">
              <FileText className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
              {quizzes.length === 0 ? (
                <>
                  <h3 className="text-xl font-semibold mb-2">No test series created yet</h3>
                  <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                    Test series are comprehensive assessments with multiple questions designed to evaluate 
                    student knowledge across various topics. Create your first test series to get started.
                  </p>
                  <div className="flex items-center justify-center gap-3">
                    <Button asChild>
                      <Link href="/admin/quizzes/create?type=TEST_SERIES">
                        <Plus className="h-4 w-4 mr-2" />
                        Create Test Series
                      </Link>
                    </Button>
                    <Button variant="outline" asChild>
                      <Link href="/admin/quizzes">
                        <ArrowLeft className="h-4 w-4 mr-2" />
                        Back to All Quizzes
                      </Link>
                    </Button>
                  </div>
                </>
              ) : (
                <>
                  <h3 className="text-xl font-semibold mb-2">No test series found</h3>
                  <p className="text-muted-foreground mb-6">
                    No test series match your search criteria. Try adjusting your search terms.
                  </p>
                  <Button variant="outline" onClick={() => setSearchQuery("")}>
                    Clear Search
                  </Button>
                </>
              )}
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <AnimatePresence>
            {filteredQuizzes.map((quiz) => (
              <motion.div
                key={quiz.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.2 }}
              >
                <Card className="h-full">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="space-y-1 flex-1">
                        <CardTitle className="text-lg line-clamp-2">{quiz.title}</CardTitle>
                        <CardDescription className="line-clamp-2">
                          {quiz.description || "No description provided"}
                        </CardDescription>
                      </div>
                      <Badge variant={quiz.isPublished ? "default" : "secondary"}>
                        {quiz.isPublished ? "Published" : "Draft"}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className={getDifficultyColor(quiz.difficulty)}>
                          {quiz.difficulty}
                        </Badge>
                        {quiz.tags.slice(0, 2).map((tag) => (
                          <Badge key={tag} variant="outline">
                            {tag}
                          </Badge>
                        ))}
                        {quiz.tags.length > 2 && (
                          <Badge variant="outline">+{quiz.tags.length - 2}</Badge>
                        )}
                      </div>

                      <div className="grid grid-cols-3 gap-4 text-sm">
                        <div className="text-center">
                          <div className="font-semibold">{quiz.questionCount}</div>
                          <div className="text-muted-foreground">Questions</div>
                        </div>
                        <div className="text-center">
                          <div className="font-semibold">{quiz.attemptCount}</div>
                          <div className="text-muted-foreground">Attempts</div>
                        </div>
                        <div className="text-center">
                          <div className="font-semibold">{quiz.averageScore}%</div>
                          <div className="text-muted-foreground">Avg Score</div>
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <Button variant="outline" size="sm" asChild>
                          <Link href={`/admin/quizzes/${quiz.id}`}>
                            <Eye className="h-4 w-4 mr-1" />
                            View
                          </Link>
                        </Button>
                        <Button variant="outline" size="sm" asChild>
                          <Link href={`/admin/quizzes/${quiz.id}/edit`}>
                            <Edit className="h-4 w-4 mr-1" />
                            Edit
                          </Link>
                        </Button>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="outline" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem asChild>
                              <Link href={`/admin/quizzes/${quiz.id}/preview`}>
                                <Eye className="h-4 w-4 mr-2" />
                                Preview
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => handleTogglePublish(quiz.id, quiz.isPublished)}
                            >
                              {quiz.isPublished ? (
                                <>
                                  <Lock className="h-4 w-4 mr-2" />
                                  Unpublish
                                </>
                              ) : (
                                <>
                                  <Globe className="h-4 w-4 mr-2" />
                                  Publish
                                </>
                              )}
                            </DropdownMenuItem>
                            <DropdownMenuItem asChild>
                              <Link href={`/admin/quizzes/${quiz.id}/analytics`}>
                                <BarChart3 className="h-4 w-4 mr-2" />
                                Analytics
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              className="text-destructive"
                              onClick={() => handleDeleteQuiz(quiz.id)}
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </AnimatePresence>
        </div>
      )}
    </div>
  )
}
