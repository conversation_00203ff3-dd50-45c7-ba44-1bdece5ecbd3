# Google API Key Setup Guide

## The Issue
You're getting the error because the `GOOGLE_API_KEY` in your `.env` file is set to a placeholder value: `"your-google-api-key"`. The AI quiz generation requires a valid Google API key to work with the Gemini model.

## How to Get a Google API Key

### Step 1: Go to Google AI Studio
1. Visit [Google AI Studio](https://aistudio.google.com/)
2. Sign in with your Google account

### Step 2: Create an API Key
1. Click on "Get API key" in the left sidebar
2. Click "Create API key"
3. Choose "Create API key in new project" (recommended) or select an existing project
4. Copy the generated API key

### Step 3: Update Your Environment File
1. Open your `.env` file
2. Replace the placeholder with your actual API key:
   ```
   GOOGLE_API_KEY="your-actual-api-key-here"
   ```

### Step 4: Restart Your Development Server
After updating the `.env` file, restart your Next.js development server:
```bash
npm run dev
```

## Testing the Fix

### Option 1: Use the Test Script
Run the test script to verify the API works:
```bash
node test-api.js
```

### Option 2: Test in the Admin Panel
1. Go to your admin panel
2. Try generating a quiz using the AI generator
3. Check the terminal for any errors

## Expected Behavior
After setting up the API key correctly, you should see:
1. The request body logged in the terminal
2. "Processing text content, length: X" message
3. Streaming quiz generation progress
4. "Quiz generation completed successfully with X questions" message

## Troubleshooting

### If you still get errors:
1. **Check API key validity**: Make sure you copied the entire key
2. **Check quotas**: Ensure your Google Cloud project has sufficient quota
3. **Check billing**: Some Google AI services require billing to be enabled
4. **Check model availability**: Ensure `gemini-1.5-pro-latest` is available in your region

### Common Error Messages:
- `"Invalid API key"` - Your API key is incorrect or expired
- `"Quota exceeded"` - You've hit your usage limits
- `"Model not found"` - The model might not be available in your region

## Alternative Models
If you have issues with `gemini-1.5-pro-latest`, you can try other models by updating the API route:
- `gemini-1.5-pro`
- `gemini-1.5-flash`
- `gemini-pro`

## Security Note
Never commit your actual API key to version control. The `.env` file should be in your `.gitignore` file.
