"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { toast } from "sonner"

interface KeyboardShortcut {
  key: string
  ctrlKey?: boolean
  altKey?: boolean
  shiftKey?: boolean
  action: () => void
  description: string
}

interface KeyboardShortcutsProps {
  shortcuts: KeyboardShortcut[]
  disabled?: boolean
}

/**
 * Keyboard shortcuts component
 * Handles global keyboard shortcuts for the application
 */
export function KeyboardShortcuts({ shortcuts, disabled = false }: KeyboardShortcutsProps) {
  useEffect(() => {
    if (disabled) return

    const handleKeyDown = (event: KeyboardEvent) => {
      // Don't trigger shortcuts when user is typing in inputs
      const target = event.target as HTMLElement
      if (
        target.tagName === 'INPUT' ||
        target.tagName === 'TEXTAREA' ||
        target.contentEditable === 'true'
      ) {
        return
      }

      const matchingShortcut = shortcuts.find(shortcut => {
        return (
          event.key.toLowerCase() === shortcut.key.toLowerCase() &&
          !!event.ctrlKey === !!shortcut.ctrlKey &&
          !!event.altKey === !!shortcut.altKey &&
          !!event.shiftKey === !!shortcut.shiftKey
        )
      })

      if (matchingShortcut) {
        event.preventDefault()
        matchingShortcut.action()
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [shortcuts, disabled])

  return null
}

/**
 * Global keyboard shortcuts for the application
 */
export function GlobalKeyboardShortcuts() {
  const router = useRouter()

  const shortcuts: KeyboardShortcut[] = [
    {
      key: '/',
      action: () => {
        // Focus search input if available
        const searchInput = document.querySelector('input[type="search"]') as HTMLInputElement
        if (searchInput) {
          searchInput.focus()
        }
      },
      description: 'Focus search'
    },
    {
      key: 'h',
      action: () => router.push('/'),
      description: 'Go to home'
    },
    {
      key: 'd',
      action: () => router.push('/dashboard'),
      description: 'Go to dashboard'
    },
    {
      key: 'q',
      action: () => router.push('/quizzes'),
      description: 'Browse quizzes'
    },
    {
      key: 'a',
      action: () => router.push('/achievements'),
      description: 'View achievements'
    },
    {
      key: 'l',
      action: () => router.push('/leaderboard'),
      description: 'View leaderboard'
    },
    {
      key: '?',
      shiftKey: true,
      action: () => {
        toast.info('Keyboard Shortcuts', {
          description: 'Press / to search, h for home, d for dashboard, q for quizzes, a for achievements, l for leaderboard'
        })
      },
      description: 'Show keyboard shortcuts'
    }
  ]

  return <KeyboardShortcuts shortcuts={shortcuts} />
}

/**
 * Quiz-specific keyboard shortcuts
 */
export function QuizKeyboardShortcuts({
  onNext,
  onPrevious,
  onSubmit,
  onFlag,
  disabled = false
}: {
  onNext?: () => void
  onPrevious?: () => void
  onSubmit?: () => void
  onFlag?: () => void
  disabled?: boolean
}) {
  const shortcuts: KeyboardShortcut[] = [
    ...(onNext ? [{
      key: 'ArrowRight',
      action: onNext,
      description: 'Next question'
    }] : []),
    ...(onPrevious ? [{
      key: 'ArrowLeft',
      action: onPrevious,
      description: 'Previous question'
    }] : []),
    ...(onSubmit ? [{
      key: 'Enter',
      ctrlKey: true,
      action: onSubmit,
      description: 'Submit quiz'
    }] : []),
    ...(onFlag ? [{
      key: 'f',
      action: onFlag,
      description: 'Flag question'
    }] : []),
    {
      key: '1',
      action: () => selectAnswer(0),
      description: 'Select answer A'
    },
    {
      key: '2',
      action: () => selectAnswer(1),
      description: 'Select answer B'
    },
    {
      key: '3',
      action: () => selectAnswer(2),
      description: 'Select answer C'
    },
    {
      key: '4',
      action: () => selectAnswer(3),
      description: 'Select answer D'
    }
  ]

  const selectAnswer = (index: number) => {
    const answerButtons = document.querySelectorAll('[data-answer-option]')
    const button = answerButtons[index] as HTMLButtonElement
    if (button && !button.disabled) {
      button.click()
    }
  }

  return <KeyboardShortcuts shortcuts={shortcuts} disabled={disabled} />
}
