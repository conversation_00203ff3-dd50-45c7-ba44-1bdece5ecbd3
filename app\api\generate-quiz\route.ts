import { questionSchema, createQuestionsSchema } from "@/lib/schemas";
import { createOpenAI } from "@ai-sdk/openai";
import { streamObject } from "ai";
import { NextResponse } from "next/server";

export const maxDuration = 60;

// Helper function to create dynamic system prompt based on question types
function createSystemPrompt(questionTypes: string[], questionCount: number, difficulty: string, customPrompt?: string): string {
  const typeInstructions = questionTypes.map(type => {
    switch (type) {
      case 'MCQ':
        return `
**Multiple Choice Questions (MCQ):**
- Provide a clear question
- Include exactly 4 options (without A), B), C), D) prefixes - just the option text)
- Specify the correct answer as "A", "B", "C", or "D"
- Optionally include an explanation

Example:
{
  "type": "MCQ",
  "question": "What is data science?",
  "options": ["A field that extracts insights from data", "A programming language", "A database system", "A web framework"],
  "answer": "A",
  "explanation": "Data science combines statistics, programming, and domain expertise to extract insights from data."
}`;

      case 'TRUE_FALSE':
        return `
**True/False Questions:**
- Provide a clear statement
- Answer should be true or false (boolean)
- Optionally include an explanation

Example:
{
  "type": "TRUE_FALSE",
  "question": "Machine learning is a subset of artificial intelligence.",
  "answer": true,
  "explanation": "Machine learning is indeed a subset of AI that focuses on algorithms that can learn from data."
}`;

      case 'SHORT_ANSWER':
        return `
**Short Answer Questions:**
- Provide a clear question requiring a brief text response
- Include the expected answer as a string
- Optionally include keywords and explanation

Example:
{
  "type": "SHORT_ANSWER",
  "question": "What does API stand for?",
  "answer": "Application Programming Interface",
  "keywords": ["Application", "Programming", "Interface"],
  "explanation": "API stands for Application Programming Interface, which allows different software applications to communicate."
}`;

      case 'MATCHING':
        return `
**Matching Questions:**
- Provide clear instruction for matching
- Include exactly 4 pairs of items to match (IMPORTANT: exactly 4 pairs, no more, no less)
- Each pair must have a "left" and "right" property with string values
- Optionally include an explanation
- Format must be exactly as shown in the example

Example:
{
  "type": "MATCHING",
  "question": "Match the programming languages with their primary use cases:",
  "pairs": [
    {"left": "Python", "right": "Data Science and AI"},
    {"left": "JavaScript", "right": "Web Development"},
    {"left": "SQL", "right": "Database Management"},
    {"left": "Swift", "right": "iOS Development"}
  ],
  "explanation": "Each language has specific strengths that make it suitable for particular domains."
}`;

      default:
        return '';
    }
  }).filter(Boolean).join('\n');

  return `You are an expert teacher creating educational quiz questions. Generate exactly ${questionCount} questions based on the provided content.

**Question Types to Generate:**
${questionTypes.join(', ')}

**Instructions for each type:**
${typeInstructions}

**General Requirements:**
- Difficulty level: ${difficulty}
- Questions should be clear, educational, and based on the provided content
- Ensure variety in question difficulty within the specified level
- Make questions engaging and test understanding, not just memorization
${customPrompt ? `\n**Additional Instructions:** ${customPrompt}` : ''}

**Important:** Generate exactly ${questionCount} questions total, distributed across the selected question types: ${questionTypes.join(', ')}.`;
}

export async function POST(req: Request) {
  try {
    // Use the provided OpenAI API key
    const openaiApiKey = process.env.OPENAI_API_KEY || '********************************************************************************************************************************************************************';
    console.log("OpenAI API Key status:", openaiApiKey ? `Present (${openaiApiKey.substring(0, 10)}...)` : "Missing");

    if (!openaiApiKey) {
      console.error("OpenAI API key not configured properly");
      return NextResponse.json(
        { error: "OpenAI API key not configured." },
        { status: 500 }
      );
    }

    // Parse the request body
    const body = await req.json();
    console.log("Request body:", JSON.stringify(body, null, 2));

    // Extract parameters with defaults
    const {
      files,
      textContent,
      questionCount = 4,
      difficulty = 'MEDIUM',
      questionTypes = ['MCQ'],
      customPrompt
    } = body;

    // Validate input - either files or textContent must be provided
    const hasFiles = files && Array.isArray(files) && files.length > 0;
    const hasTextContent = textContent && typeof textContent === 'string' && textContent.trim().length > 0;

    if (!hasFiles && !hasTextContent) {
      console.error("No content provided:", { hasFiles, hasTextContent });
      return NextResponse.json(
        { error: "Please provide either files or text content to generate questions from." },
        { status: 400 }
      );
    }

    // Validate question types
    const validQuestionTypes = ['MCQ', 'TRUE_FALSE', 'SHORT_ANSWER', 'MATCHING'];
    const invalidTypes = questionTypes.filter((type: string) => !validQuestionTypes.includes(type));
    if (invalidTypes.length > 0) {
      console.error("Invalid question types:", invalidTypes);
      return NextResponse.json(
        { error: `Invalid question types: ${invalidTypes.join(', ')}. Valid types are: ${validQuestionTypes.join(', ')}` },
        { status: 400 }
      );
    }



    // Create dynamic system prompt based on selected question types
    const systemPrompt = createSystemPrompt(questionTypes, questionCount, difficulty, customPrompt);

    let prompt: string | undefined;
    let messages: any[] | undefined;

    if (hasFiles) {
      // Handle file-based generation
      const firstFile = files[0];
      if (!firstFile || !firstFile.data) {
        console.error("Invalid file structure:", firstFile);
        return NextResponse.json(
          { error: "Invalid file format. Please ensure the file is properly uploaded." },
          { status: 400 }
        );
      }

      console.log("Processing file:", firstFile.name || "unnamed file");

      // For file-based generation, use messages format
      messages = [
        {
          role: "system",
          content: systemPrompt,
        },
        {
          role: "user",
          content: [
            {
              type: "text",
              text: `Create a quiz with ${questionCount} questions of the following types: ${questionTypes.join(', ')} based on this document.`,
            },
            {
              type: "file",
              data: firstFile.data,
              mimeType: firstFile.type || "application/pdf",
            },
          ],
        },
      ];
    } else {
      // Handle text-based generation using prompt format
      console.log("Processing text content, length:", textContent.length);

      prompt = `${systemPrompt}\n\nCreate a quiz with ${questionCount} questions of the following types: ${questionTypes.join(', ')} based on this content:\n\n${textContent}`;
    }

    // Create custom OpenAI provider with API key
    const openai = createOpenAI({
      apiKey: "********************************************************************************************************************************************************************",
    });

    // Configure streamObject based on content type
    // Use dynamic schema based on question count
    const dynamicSchema = createQuestionsSchema(questionCount);

    const streamObjectConfig: any = {
      model: openai("gpt-4o-mini"),
      schema: dynamicSchema,
      output: "array",
      onError: ({ error }: { error: any }) => {
        console.error("StreamObject error:", error);
      },
      onFinish: ({ object }: { object: any }) => {
        console.log(`Quiz generation completed successfully with ${object?.length || 0} questions`);

        // Print the generated quiz data
        console.log("\n=== GENERATED QUIZ DATA ===");
        console.log(JSON.stringify(object, null, 2));
        console.log("=== END QUIZ DATA ===\n");

        // Print each question in a readable format
        if (object && Array.isArray(object)) {
          console.log("\n=== FORMATTED QUESTIONS ===");
          object.forEach((question: any, index: number) => {
            console.log(`\nQuestion ${index + 1} (${question.type || 'MCQ'}): ${question.question}`);

            // Handle different question types
            switch(question.type) {
              case 'MCQ':
                if (question.options && Array.isArray(question.options)) {
                  question.options.forEach((option: string, optIndex: number) => {
                    const label = ['A', 'B', 'C', 'D'][optIndex];
                    const isCorrect = question.answer === label ? ' ✓ CORRECT' : '';
                    console.log(`  ${label}. ${option}${isCorrect}`);
                  });
                }
                console.log(`  Answer: ${question.answer}`);
                break;

              case 'TRUE_FALSE':
                console.log(`  Answer: ${question.answer ? 'TRUE' : 'FALSE'} ✓ CORRECT`);
                break;

              case 'SHORT_ANSWER':
                console.log(`  Expected Answer: ${question.answer} ✓ CORRECT`);
                if (question.keywords) {
                  console.log(`  Keywords: ${question.keywords.join(', ')}`);
                }
                break;

              case 'MATCHING':
                console.log(`  Pairs to match:`);
                question.pairs?.forEach((pair: any, pairIndex: number) => {
                  console.log(`    ${pairIndex + 1}. ${pair.left} → ${pair.right}`);
                });
                break;

              default:
                // Legacy MCQ format
                if (question.options && Array.isArray(question.options)) {
                  question.options.forEach((option: string, optIndex: number) => {
                    const label = ['A', 'B', 'C', 'D'][optIndex];
                    const isCorrect = question.answer === label ? ' ✓ CORRECT' : '';
                    console.log(`  ${label}. ${option}${isCorrect}`);
                  });
                  console.log(`  Answer: ${question.answer}`);
                }
            }

            if (question.explanation) {
              console.log(`  Explanation: ${question.explanation}`);
            }
          });
          console.log("=== END FORMATTED QUESTIONS ===\n");
        }
      },
    };

    // Add either prompt or messages based on content type
    if (hasFiles) {
      streamObjectConfig.messages = messages;
    } else {
      streamObjectConfig.prompt = prompt;
    }

    console.log("Starting quiz generation stream...");
    console.log("Using model: gpt-4o-mini");
    console.log("Schema:", questionSchema);
    console.log("Prompt preview:", hasFiles ? "File-based generation" : prompt?.substring(0, 200) + "...");

    try {
      const result = streamObject(streamObjectConfig);

      // Add additional logging for the stream
      console.log("StreamObject created successfully");

      // Log when the stream starts
      const response = result.toTextStreamResponse();
      console.log("Text stream response created");

      return response;
    } catch (streamError) {
      console.error("Error creating stream object:", streamError);
      return NextResponse.json(
        { error: "Failed to create quiz generation stream" },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Error in generate-quiz API:", error);

    // Handle different types of errors
    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json(
      { error: "An unexpected error occurred while generating the quiz." },
      { status: 500 }
    );
  }
}
