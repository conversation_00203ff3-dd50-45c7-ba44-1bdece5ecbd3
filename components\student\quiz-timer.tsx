"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Clock, AlertTriangle } from "lucide-react"
import { motion } from "framer-motion"

interface QuizTimerProps {
  timeRemaining: number // in seconds
  isPaused: boolean
  onTimeUpdate: (timeRemaining: number) => void
  onTimeExpired: () => void
  warningThreshold?: number // seconds when to show warning (default: 300 = 5 minutes)
}

export function QuizTimer({ 
  timeRemaining, 
  isPaused, 
  onTimeUpdate, 
  onTimeExpired,
  warningThreshold = 300 
}: QuizTimerProps) {
  const [displayTime, setDisplayTime] = useState(timeRemaining)

  useEffect(() => {
    setDisplayTime(timeRemaining)
  }, [timeRemaining])

  useEffect(() => {
    if (isPaused || displayTime <= 0) return

    const interval = setInterval(() => {
      setDisplayTime(prev => {
        const newTime = prev - 1
        onTimeUpdate(newTime)
        
        if (newTime <= 0) {
          onTimeExpired()
          return 0
        }
        
        return newTime
      })
    }, 1000)

    return () => clearInterval(interval)
  }, [isPaused, displayTime, onTimeUpdate, onTimeExpired])

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = seconds % 60

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`
  }

  const getTimerColor = () => {
    if (displayTime <= 60) return 'text-red-600 bg-red-50 border-red-200' // Last minute
    if (displayTime <= warningThreshold) return 'text-orange-600 bg-orange-50 border-orange-200' // Warning
    return 'text-green-600 bg-green-50 border-green-200' // Normal
  }

  const getProgressPercentage = () => {
    const totalTime = timeRemaining > displayTime ? timeRemaining : displayTime
    return totalTime > 0 ? (displayTime / totalTime) * 100 : 0
  }

  const isWarning = displayTime <= warningThreshold && displayTime > 60
  const isCritical = displayTime <= 60

  return (
    <Card className={`${getTimerColor()} border-2 ${isCritical ? 'animate-pulse' : ''}`}>
      <CardContent className="pt-4">
        <div className="flex items-center gap-3">
          <div className="relative">
            <Clock className={`h-5 w-5 ${isCritical ? 'text-red-600' : isWarning ? 'text-orange-600' : 'text-green-600'}`} />
            {isCritical && (
              <motion.div
                className="absolute -top-1 -right-1"
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 1, repeat: Infinity }}
              >
                <AlertTriangle className="h-3 w-3 text-red-600" />
              </motion.div>
            )}
          </div>
          
          <div className="flex-1">
            <div className={`text-lg font-mono font-bold ${
              isCritical ? 'text-red-600' : 
              isWarning ? 'text-orange-600' : 
              'text-green-600'
            }`}>
              {formatTime(displayTime)}
            </div>
            
            <div className="text-xs text-muted-foreground">
              {isPaused ? 'Paused' : 'Time Remaining'}
            </div>
          </div>

          {(isWarning || isCritical) && (
            <Badge variant="outline" className={
              isCritical ? 'text-red-600 border-red-600' : 'text-orange-600 border-orange-600'
            }>
              {isCritical ? 'Critical' : 'Warning'}
            </Badge>
          )}
        </div>

        {/* Progress Bar */}
        <div className="mt-3">
          <div className="w-full bg-gray-200 rounded-full h-1.5">
            <motion.div
              className={`h-1.5 rounded-full transition-all duration-1000 ${
                isCritical ? 'bg-red-500' : 
                isWarning ? 'bg-orange-500' : 
                'bg-green-500'
              }`}
              style={{ width: `${getProgressPercentage()}%` }}
              animate={isCritical ? { opacity: [1, 0.5, 1] } : {}}
              transition={isCritical ? { duration: 1, repeat: Infinity } : {}}
            />
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
