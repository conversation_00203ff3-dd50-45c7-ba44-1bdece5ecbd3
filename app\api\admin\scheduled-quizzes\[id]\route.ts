import { NextRequest } from 'next/server'
import { z } from 'zod'
 import { createAPIHandler, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'

const updateScheduledQuizSchema = z.object({
  title: z.string().min(1, "Title is required").optional(),
  description: z.string().optional(),
  startTime: z.string().datetime("Invalid start time").optional(),
  endTime: z.string().datetime("Invalid end time").optional(),
  duration: z.number().min(1).optional(),
  maxAttempts: z.number().min(1).optional(),
  isActive: z.boolean().optional(),
  allowLateSubmission: z.boolean().optional(),
  showResults: z.boolean().optional(),
  shuffleQuestions: z.boolean().optional()
})

// GET /api/admin/scheduled-quizzes/[id] - Get specific scheduled quiz
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
     
  },
  async (request: NextRequest, { params }: { params: Promise<{ id: string } > }) => {
    const { id } = await params
    const scheduledQuizId = id

    const scheduledQuiz = await prisma.scheduledQuiz.findUnique({
      where: { id: scheduledQuizId },
      include: {
        quiz: {
          select: {
            id: true,
            title: true,
            type: true,
            difficulty: true,
            _count: {
              select: {
                questions: true
              }
            }
          }
        },
        creator: {
          select: {
            name: true,
            email: true
          }
        },
        enrollments: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true
              }
            }
          }
        }
      }
    })

    if (!scheduledQuiz) {
      return APIResponse.error('Scheduled quiz not found', 404, 'SCHEDULED_QUIZ_NOT_FOUND')
    }

    // Get attempt statistics
    const now = new Date()
    const [totalAttempts, completedAttempts, avgScore] = await Promise.all([
      prisma.quizAttempt.count({
        where: {
          quizId: scheduledQuiz.quizId,
          startedAt: {
            gte: scheduledQuiz.startTime,
            lte: scheduledQuiz.endTime
          }
        }
      }),
      prisma.quizAttempt.count({
        where: {
          quizId: scheduledQuiz.quizId,
          isCompleted: true,
          startedAt: {
            gte: scheduledQuiz.startTime,
            lte: scheduledQuiz.endTime
          }
        }
      }),
      prisma.quizAttempt.aggregate({
        where: {
          quizId: scheduledQuiz.quizId,
          isCompleted: true,
          startedAt: {
            gte: scheduledQuiz.startTime,
            lte: scheduledQuiz.endTime
          }
        },
        _avg: {
          percentage: true
        }
      })
    ])

    // Determine status
    let status: 'upcoming' | 'active' | 'completed' | 'expired'
    if (!scheduledQuiz.isActive) {
      status = 'expired'
    } else if (now < scheduledQuiz.startTime) {
      status = 'upcoming'
    } else if (now > scheduledQuiz.endTime) {
      status = 'completed'
    } else {
      status = 'active'
    }

    const result = {
      id: scheduledQuiz.id,
      quiz: {
        id: scheduledQuiz.quiz.id,
        title: scheduledQuiz.quiz.title,
        type: scheduledQuiz.quiz.type,
        difficulty: scheduledQuiz.quiz.difficulty,
        questionCount: scheduledQuiz.quiz._count.questions
      },
      title: scheduledQuiz.title,
      description: scheduledQuiz.description,
      startTime: scheduledQuiz.startTime.toISOString(),
      endTime: scheduledQuiz.endTime.toISOString(),
      duration: scheduledQuiz.duration,
      maxAttempts: scheduledQuiz.maxAttempts,
      isActive: scheduledQuiz.isActive,
      allowLateSubmission: scheduledQuiz.allowLateSubmission,
      showResults: scheduledQuiz.showResults,
      shuffleQuestions: scheduledQuiz.shuffleQuestions,
      status,
      participants: scheduledQuiz.enrollments.length,
      totalAttempts,
      completedAttempts,
      averageScore: Math.round(avgScore._avg.percentage || 0),
      createdAt: scheduledQuiz.createdAt.toISOString(),
      createdBy: scheduledQuiz.creator,
      enrollments: scheduledQuiz.enrollments.map(enrollment => ({
        id: enrollment.id,
        user: enrollment.user,
        enrolledAt: enrollment.enrolledAt.toISOString()
      }))
    }

    return APIResponse.success(result, 'Scheduled quiz retrieved successfully')
  }
)

// PUT /api/admin/scheduled-quizzes/[id] - Update scheduled quiz
export const PUT = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
     
    validateBody: updateScheduledQuizSchema
  },
  async (request: NextRequest, { params, validatedBody }: { params: Promise<{ id: string } >, validatedBody: any }) => {
    const { id } = await params
    const scheduledQuizId = id
    const updateData = validatedBody

    // Check if scheduled quiz exists
    const existingScheduledQuiz = await prisma.scheduledQuiz.findUnique({
      where: { id: scheduledQuizId }
    })

    if (!existingScheduledQuiz) {
      return APIResponse.error('Scheduled quiz not found', 404, 'SCHEDULED_QUIZ_NOT_FOUND')
    }

    // Validate time range if both times are provided
    if (updateData.startTime && updateData.endTime) {
      const start = new Date(updateData.startTime)
      const end = new Date(updateData.endTime)

      if (start >= end) {
        return APIResponse.error(
          'End time must be after start time',
          400,
          'INVALID_TIME_RANGE'
        )
      }
    }

    // Convert date strings to Date objects
    const processedData = {
      ...updateData,
      ...(updateData.startTime && { startTime: new Date(updateData.startTime) }),
      ...(updateData.endTime && { endTime: new Date(updateData.endTime) })
    }

    const updatedScheduledQuiz = await prisma.scheduledQuiz.update({
      where: { id: scheduledQuizId },
      data: processedData,
      include: {
        quiz: {
          select: {
            id: true,
            title: true,
            type: true,
            difficulty: true,
            _count: {
              select: {
                questions: true
              }
            }
          }
        },
        creator: {
          select: {
            name: true,
            email: true
          }
        }
      }
    })

    return APIResponse.success(
      {
        id: updatedScheduledQuiz.id,
        quiz: {
          id: updatedScheduledQuiz.quiz.id,
          title: updatedScheduledQuiz.quiz.title,
          type: updatedScheduledQuiz.quiz.type,
          difficulty: updatedScheduledQuiz.quiz.difficulty,
          questionCount: updatedScheduledQuiz.quiz._count.questions
        },
        title: updatedScheduledQuiz.title,
        description: updatedScheduledQuiz.description,
        startTime: updatedScheduledQuiz.startTime.toISOString(),
        endTime: updatedScheduledQuiz.endTime.toISOString(),
        duration: updatedScheduledQuiz.duration,
        maxAttempts: updatedScheduledQuiz.maxAttempts,
        isActive: updatedScheduledQuiz.isActive,
        allowLateSubmission: updatedScheduledQuiz.allowLateSubmission,
        showResults: updatedScheduledQuiz.showResults,
        shuffleQuestions: updatedScheduledQuiz.shuffleQuestions,
        createdAt: updatedScheduledQuiz.createdAt.toISOString(),
        createdBy: updatedScheduledQuiz.creator
      },
      'Scheduled quiz updated successfully'
    )
  }
)

// DELETE /api/admin/scheduled-quizzes/[id] - Delete scheduled quiz
export const DELETE = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
     
  },
  async (request: NextRequest, { params }: { params: Promise<{ id: string } > }) => {
    const { id } = await params
    const scheduledQuizId = id

    // Check if scheduled quiz exists
    const existingScheduledQuiz = await prisma.scheduledQuiz.findUnique({
      where: { id: scheduledQuizId }
    })

    if (!existingScheduledQuiz) {
      return APIResponse.error('Scheduled quiz not found', 404, 'SCHEDULED_QUIZ_NOT_FOUND')
    }

    await prisma.scheduledQuiz.delete({
      where: { id: scheduledQuizId }
    })

    return APIResponse.success(
      { deletedScheduledQuizId: scheduledQuizId },
      'Scheduled quiz deleted successfully'
    )
  }
)
