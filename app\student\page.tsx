"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { AnimatedCard, StaggeredCards } from "@/components/ui/animated-card"
import { AnimatedButton } from "@/components/ui/animated-button"
import { FloatingBackground, GradientOrb } from "@/components/ui/floating-shapes"
import { useIntersectionObserver } from "@/hooks/use-intersection-observer"
import { useCounterAnimation } from "@/hooks/use-counter-animation"

import {
  BookOpen,
  Trophy,
  Clock,
  Target,
  TrendingUp,
  Calendar,
  Play,
  Star,
  Award,
  Users,
  ChevronRight,
  Zap,
  Brain,
  CheckCircle,
  XCircle,
  RotateCcw,
  Sparkles,
  TrendingDown,
  Activity,
  BarChart3
} from "lucide-react"
import Link from "next/link"
import { motion, AnimatePresence } from "framer-motion"
import { toast } from "@/lib/toast-utils"
import { StudentError<PERSON><PERSON><PERSON> } from "@/lib/student-error-handler"

interface StudentStats {
  totalQuizzes: number
  completedQuizzes: number
  averageScore: number
  totalPoints: number
  currentStreak: number
  rank: number
  totalStudents: number
  hoursSpent: number
}

interface RecentQuiz {
  id: string
  title: string
  type: string
  score: number
  maxScore: number
  completedAt: string
  difficulty: string
  timeSpent: number
}

interface UpcomingQuiz {
  id: string
  title: string
  type: string
  difficulty: string
  startTime: string
  duration: number
  questionsCount: number
  isEnrolled: boolean
}

interface Achievement {
  id: string
  title: string
  description: string
  icon: string
  unlockedAt: string
  rarity: 'common' | 'rare' | 'epic' | 'legendary'
}

export default function StudentDashboard() {
  const [stats, setStats] = useState<StudentStats>({
    totalQuizzes: 0,
    completedQuizzes: 0,
    averageScore: 0,
    totalPoints: 0,
    currentStreak: 0,
    rank: 0,
    totalStudents: 0,
    hoursSpent: 0
  })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const [recentQuizzes, setRecentQuizzes] = useState<RecentQuiz[]>([])

  const [upcomingQuizzes, setUpcomingQuizzes] = useState<UpcomingQuiz[]>([])

  const [recentAchievements, setRecentAchievements] = useState<Achievement[]>([])

  // Fetch dashboard data
  const fetchDashboardData = async () => {
    setLoading(true)
    setError(null)

    try {
      const data = await StudentErrorHandler.fetchWithErrorHandling(
        '/api/student/dashboard',
        {},
        'Fetching dashboard data'
      )

      if (data.success) {
        setStats(data.data.stats)
        setRecentQuizzes(data.data.recentAttempts || [])
        setUpcomingQuizzes(data.data.enrolledQuizzes || [])
        setRecentAchievements(data.data.achievements || [])
      } else {
        throw new Error(data.message || 'Failed to fetch dashboard data')
      }
    } catch (error) {
      const errorMessage = StudentErrorHandler.handleError(
        error as Error,
        'Fetching dashboard data',
        {
          showToast: true,
          retryCallback: () => fetchDashboardData(),
          fallbackMessage: 'Failed to load dashboard data'
        }
      )
      setError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'EASY':
        return 'text-green-600 bg-green-50 border-green-200 dark:text-green-400 dark:bg-green-900/20 dark:border-green-800'
      case 'MEDIUM':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200 dark:text-yellow-400 dark:bg-yellow-900/20 dark:border-yellow-800'
      case 'HARD':
        return 'text-red-600 bg-red-50 border-red-200 dark:text-red-400 dark:bg-red-900/20 dark:border-red-800'
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200 dark:text-gray-400 dark:bg-gray-900/20 dark:border-gray-800'
    }
  }

  const getScoreColor = (score: number, maxScore: number) => {
    const percentage = (score / maxScore) * 100
    if (percentage >= 90) return 'text-green-600'
    if (percentage >= 70) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common':
        return 'border-gray-300 bg-gray-50'
      case 'rare':
        return 'border-blue-300 bg-blue-50'
      case 'epic':
        return 'border-purple-300 bg-purple-50'
      case 'legendary':
        return 'border-yellow-300 bg-yellow-50'
      default:
        return 'border-gray-300 bg-gray-50'
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-violet-50 via-white to-electric-50 dark:from-gray-900 dark:via-gray-800 dark:to-violet-900 relative overflow-hidden">
        {/* Background Elements */}
        <FloatingBackground density="low" colors={['violet', 'blue', 'cyan']} />

        <div className="relative z-10 container mx-auto p-6">
          <div className="flex items-center justify-center min-h-[400px]">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
              className="text-center"
            >
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                className="w-16 h-16 mx-auto mb-6"
              >
                <div className="w-full h-full rounded-full border-4 border-violet-200 dark:border-violet-800 border-t-violet-600 dark:border-t-violet-400"></div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className="space-y-2"
              >
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                  Loading Dashboard
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Preparing your learning experience...
                </p>
              </motion.div>
            </motion.div>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-violet-50 via-white to-electric-50 dark:from-gray-900 dark:via-gray-800 dark:to-violet-900 relative overflow-hidden">
        {/* Background Elements */}
        <FloatingBackground density="low" colors={['violet', 'blue', 'cyan']} />
        <GradientOrb
          className="top-20 right-20"
          size={200}
          colors={['#8b5cf6', '#3b82f6']}
        />

        <div className="relative z-10 container mx-auto p-6">
          <div className="flex items-center justify-center min-h-[400px]">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
            >
              <AnimatedCard variant="glass" className="max-w-md text-center">
                <CardContent className="pt-8 pb-8">
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ duration: 0.5, delay: 0.2 }}
                    className="mb-6"
                  >
                    <div className="w-16 h-16 mx-auto rounded-full bg-gradient-to-br from-red-500 to-pink-600 flex items-center justify-center mb-4">
                      <XCircle className="h-8 w-8 text-white" />
                    </div>
                  </motion.div>

                  <motion.h3
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.3 }}
                    className="text-xl font-semibold text-gray-900 dark:text-white mb-3"
                  >
                    Failed to Load Dashboard
                  </motion.h3>

                  <motion.p
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.4 }}
                    className="text-gray-600 dark:text-gray-400 mb-6"
                  >
                    {error}
                  </motion.p>

                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.5 }}
                  >
                    <AnimatedButton
                      onClick={fetchDashboardData}
                      variant="primary"
                      size="lg"
                      className="group"
                    >
                      <RotateCcw className="h-4 w-4 mr-2 group-hover:rotate-180 transition-transform duration-500" />
                      Try Again
                    </AnimatedButton>
                  </motion.div>
                </CardContent>
              </AnimatedCard>
            </motion.div>
          </div>
        </div>
      </div>
    )
  }

  const { elementRef, isVisible } = useIntersectionObserver({
    threshold: 0.1,
    freezeOnceVisible: true,
  });

  return (
    <div className="min-h-screen bg-gradient-to-br from-violet-50 via-white to-electric-50 dark:from-gray-900 dark:via-gray-800 dark:to-violet-900 relative overflow-hidden">
      {/* Background Elements */}
      <FloatingBackground density="medium" colors={['violet', 'blue', 'cyan', 'pink']} />
      <GradientOrb
        className="top-20 left-20"
        size={300}
        colors={['#8b5cf6', '#3b82f6']}
      />
      <GradientOrb
        className="bottom-20 right-20"
        size={250}
        colors={['#06b6d4', '#f472b6']}
      />

      <div ref={elementRef} className="relative z-10 p-6 space-y-8">
        {/* Welcome Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isVisible ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, ease: 'easeOut' }}
          className="text-center space-y-4"
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={isVisible ? { opacity: 1, scale: 1 } : {}}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="inline-flex items-center gap-2 px-4 py-2 rounded-full glass text-sm font-medium text-violet-700 dark:text-violet-300 mb-4"
          >
            <Sparkles className="w-4 h-4 text-yellow-500" />
            Student Dashboard
          </motion.div>

          <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 dark:text-white">
            Welcome back,{' '}
            <span className="text-gradient bg-gradient-primary">Student!</span>
            <span className="ml-2">👋</span>
          </h1>

          <p className="text-xl text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
            Ready to continue your learning journey? Let's achieve your goals together.
          </p>
        </motion.div>

        {/* Quick Stats */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isVisible ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.3 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={isVisible ? { opacity: 1, scale: 1 } : {}}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <AnimatedCard variant="glass" hover="lift" className="text-center p-6 group">
              <div className="w-14 h-14 mx-auto mb-4 rounded-2xl bg-gradient-to-br from-blue-500 to-cyan-600 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <BookOpen className="w-7 h-7 text-white" />
              </div>

              <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                {useCounterAnimation({ end: stats.completedQuizzes, isVisible, duration: 2000 })}
              </div>

              <div className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                Quizzes Completed
              </div>

              <div className="relative h-2 w-full overflow-hidden rounded-full bg-gray-200 dark:bg-gray-700">
                <motion.div
                  className="h-full bg-gradient-to-r from-blue-500 to-cyan-600 rounded-full"
                  initial={{ width: 0 }}
                  animate={isVisible ? {
                    width: `${Math.min((stats.completedQuizzes / Math.max(stats.totalQuizzes, 1)) * 100, 100)}%`
                  } : {}}
                  transition={{ duration: 1.5, delay: 0.8 }}
                />
              </div>

              <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                {stats.completedQuizzes} of {stats.totalQuizzes} total
              </p>
            </AnimatedCard>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={isVisible ? { opacity: 1, scale: 1 } : {}}
            transition={{ duration: 0.6, delay: 0.5 }}
          >
            <AnimatedCard variant="glass" hover="lift" className="text-center p-6 group">
              <div className="w-14 h-14 mx-auto mb-4 rounded-2xl bg-gradient-to-br from-green-500 to-emerald-600 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <Target className="w-7 h-7 text-white" />
              </div>

              <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                {useCounterAnimation({ end: stats.averageScore, suffix: '%', isVisible, duration: 2000 })}
              </div>

              <div className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                Average Score
              </div>

              <div className="flex items-center justify-center gap-2 text-green-600 dark:text-green-400">
                <TrendingUp className="h-4 w-4" />
                <p className="text-xs">+5.2% from last month</p>
              </div>
            </AnimatedCard>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={isVisible ? { opacity: 1, scale: 1 } : {}}
            transition={{ duration: 0.6, delay: 0.6 }}
          >
            <AnimatedCard variant="glass" hover="lift" className="text-center p-6 group">
              <div className="w-14 h-14 mx-auto mb-4 rounded-2xl bg-gradient-to-br from-purple-500 to-violet-600 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <Trophy className="w-7 h-7 text-white" />
              </div>

              <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                #{useCounterAnimation({ end: stats.rank, isVisible, duration: 2000 })}
              </div>

              <div className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                Your Rank
              </div>

              <p className="text-xs text-gray-500 dark:text-gray-400">
                Out of {stats.totalStudents.toLocaleString()} students
              </p>
            </AnimatedCard>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={isVisible ? { opacity: 1, scale: 1 } : {}}
            transition={{ duration: 0.6, delay: 0.7 }}
          >
            <AnimatedCard variant="glass" hover="lift" className="text-center p-6 group">
              <div className="w-14 h-14 mx-auto mb-4 rounded-2xl bg-gradient-to-br from-orange-500 to-amber-600 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <Zap className="w-7 h-7 text-white" />
              </div>

              <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                {useCounterAnimation({ end: stats.currentStreak, isVisible, duration: 2000 })}
              </div>

              <div className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                Day Streak
              </div>

              <div className="flex items-center justify-center gap-2 text-orange-600 dark:text-orange-400">
                <Star className="h-4 w-4" />
                <p className="text-xs">Keep it up!</p>
              </div>
            </AnimatedCard>
          </motion.div>
        </motion.div>

        {/* Main Content Grid */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isVisible ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="grid grid-cols-1 lg:grid-cols-3 gap-8"
        >
          {/* Left Column - Recent Activity & Upcoming */}
          <div className="lg:col-span-2 space-y-8">
            {/* Recent Quizzes */}
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              animate={isVisible ? { opacity: 1, x: 0 } : {}}
              transition={{ duration: 0.8, delay: 0.7 }}
            >
              <AnimatedCard variant="glass" hover="lift" className="p-0 overflow-hidden">
                <CardHeader className="pb-4">
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2 text-gray-900 dark:text-white">
                      <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-violet-500 to-purple-600 flex items-center justify-center">
                        <Clock className="h-4 w-4 text-white" />
                      </div>
                      Recent Activity
                    </CardTitle>
                    <AnimatedButton variant="outline" size="sm">
                      <Link href="/student/history" className="group">
                        View All
                        <ChevronRight className="h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform" />
                      </Link>
                    </AnimatedButton>
                  </div>
                  <CardDescription className="text-gray-600 dark:text-gray-400">
                    Your latest quiz attempts and scores
                  </CardDescription>
                </CardHeader>
                <CardContent className="p-6">
                  <div className="space-y-4">
                    {recentQuizzes.map((quiz, index) => (
                      <motion.div
                        key={quiz.id}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3, delay: 0.1 * index }}
                        className="flex items-center justify-between p-4 rounded-xl glass border border-white/20 dark:border-white/10 hover:shadow-lg transition-all duration-300 group"
                      >
                        <div className="flex-1">
                          <h4 className="font-semibold text-gray-900 dark:text-white truncate group-hover:text-violet-600 dark:group-hover:text-violet-400 transition-colors">
                            {quiz.title}
                          </h4>
                          <div className="flex items-center gap-2 mt-2">
                            <Badge variant="outline" className={`${getDifficultyColor(quiz.difficulty)} text-xs`}>
                              {quiz.difficulty}
                            </Badge>
                            <Badge variant="outline" className="text-xs">
                              {quiz.type.replace('_', ' ')}
                            </Badge>
                            <span className="text-xs text-gray-500 dark:text-gray-400 flex items-center gap-1">
                              <Clock className="w-3 h-3" />
                              {quiz.timeSpent} min
                            </span>
                          </div>
                          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            {new Date(quiz.completedAt).toLocaleDateString()}
                          </p>
                        </div>
                        <div className="text-right ml-4">
                          <div className={`text-2xl font-bold ${getScoreColor(quiz.score, quiz.maxScore)} group-hover:scale-110 transition-transform duration-300`}>
                            {Math.round((quiz.score / quiz.maxScore) * 100)}%
                          </div>
                          <p className="text-xs text-gray-500 dark:text-gray-400">
                            {quiz.score}/{quiz.maxScore} points
                          </p>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </CardContent>
              </AnimatedCard>
            </motion.div>

            {/* Upcoming Quizzes */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.6 }}
            >
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2">
                      <Calendar className="h-5 w-5" />
                      Upcoming Quizzes
                    </CardTitle>
                    <Button variant="outline" size="sm" asChild>
                      <Link href="/student/browse">
                        Browse All
                        <ChevronRight className="h-4 w-4 ml-1" />
                      </Link>
                    </Button>
                  </div>
                  <CardDescription>
                    Scheduled quizzes and assessments
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {upcomingQuizzes.map((quiz, index) => (
                      <motion.div
                        key={quiz.id}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3, delay: 0.1 * index }}
                        className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
                      >
                        <div className="flex-1">
                          <h4 className="font-semibold truncate">{quiz.title}</h4>
                          <div className="flex items-center gap-2 mt-1">
                            <Badge variant="outline" className={getDifficultyColor(quiz.difficulty)}>
                              {quiz.difficulty}
                            </Badge>
                            <Badge variant="outline">
                              {quiz.type.replace('_', ' ')}
                            </Badge>
                            <span className="text-xs text-muted-foreground">
                              {quiz.questionsCount} questions • {quiz.duration} min
                            </span>
                          </div>
                          <p className="text-xs text-muted-foreground mt-1">
                            {new Date(quiz.startTime).toLocaleString()}
                          </p>
                        </div>
                        <div className="flex items-center gap-2">
                          {quiz.isEnrolled ? (
                            <Badge className="bg-green-500">
                              <CheckCircle className="h-3 w-3 mr-1" />
                              Enrolled
                            </Badge>
                          ) : (
                            <Button size="sm">
                              Enroll
                            </Button>
                          )}
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>

          {/* Right Column - Achievements & Quick Actions */}
          <div className="space-y-8">
            {/* Recent Achievements */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.7 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Award className="h-5 w-5" />
                    Recent Achievements
                  </CardTitle>
                  <CardDescription>
                    Your latest unlocked badges
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {recentAchievements.map((achievement, index) => (
                      <motion.div
                        key={achievement.id}
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.3, delay: 0.1 * index }}
                        className={`p-3 rounded-lg border-2 ${getRarityColor(achievement.rarity)}`}
                      >
                        <div className="flex items-center gap-3">
                          <div className="text-2xl">{achievement.icon}</div>
                          <div className="flex-1">
                            <h4 className="font-semibold text-sm">{achievement.title}</h4>
                            <p className="text-xs text-muted-foreground">
                              {achievement.description}
                            </p>
                            <p className="text-xs text-muted-foreground mt-1">
                              {new Date(achievement.unlockedAt).toLocaleDateString()}
                            </p>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                  <Button variant="outline" className="w-full mt-4" asChild>
                    <Link href="/student/achievements">
                      View All Achievements
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            </motion.div>

            {/* Quick Actions */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.8 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Brain className="h-5 w-5" />
                    Quick Actions
                  </CardTitle>
                  <CardDescription>
                    Jump into learning
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button className="w-full justify-start" asChild>
                    <Link href="/student/browse">
                      <BookOpen className="h-4 w-4 mr-2" />
                      Browse Quizzes
                    </Link>
                  </Button>
                  <Button variant="outline" className="w-full justify-start" asChild>
                    <Link href="/student/practice">
                      <Play className="h-4 w-4 mr-2" />
                      Daily Practice
                    </Link>
                  </Button>
                  <Button variant="outline" className="w-full justify-start" asChild>
                    <Link href="/student/leaderboard">
                      <Trophy className="h-4 w-4 mr-2" />
                      Leaderboard
                    </Link>
                  </Button>
                  <Button variant="outline" className="w-full justify-start" asChild>
                    <Link href="/student/profile">
                      <Users className="h-4 w-4 mr-2" />
                      My Profile
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </div>
  )
}
