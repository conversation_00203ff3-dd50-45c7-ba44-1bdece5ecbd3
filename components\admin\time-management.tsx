"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { 
  Clock, 
  Timer, 
  Calendar,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Play,
  Pause,
  RotateCcw,
  Settings,
  Bell
} from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { motion } from "framer-motion"
import { toast } from "@/lib/toast-utils"

interface TimeSettings {
  quizId: string
  quizTitle: string
  timeLimit: number // in minutes
  warningTime: number // minutes before time runs out to show warning
  autoSubmit: boolean
  allowPause: boolean
  showTimer: boolean
  gracePeriod: number // extra seconds after time expires
  timeZone: string
  startTime?: string
  endTime?: string
  extendedTime?: {
    userId: string
    userName: string
    additionalMinutes: number
    reason: string
  }[]
}

interface TimeManagementProps {
  quizId: string
  initialSettings?: TimeSettings
  onSave: (settings: TimeSettings) => void
  onClose: () => void
}

export function TimeManagement({ quizId, initialSettings, onSave, onClose }: TimeManagementProps) {
  const [settings, setSettings] = useState<TimeSettings>(
    initialSettings || {
      quizId,
      quizTitle: "Quiz Title",
      timeLimit: 30,
      warningTime: 5,
      autoSubmit: true,
      allowPause: false,
      showTimer: true,
      gracePeriod: 30,
      timeZone: "UTC",
      extendedTime: []
    }
  )

  const [activeAttempts, setActiveAttempts] = useState([
    {
      id: "1",
      userId: "user1",
      userName: "John Doe",
      startTime: new Date(Date.now() - 15 * 60 * 1000), // 15 minutes ago
      timeRemaining: 15 * 60, // 15 minutes in seconds
      isPaused: false,
      warnings: 1
    },
    {
      id: "2",
      userId: "user2",
      userName: "Jane Smith",
      startTime: new Date(Date.now() - 25 * 60 * 1000), // 25 minutes ago
      timeRemaining: 5 * 60, // 5 minutes in seconds
      isPaused: false,
      warnings: 2
    },
    {
      id: "3",
      userId: "user3",
      userName: "Bob Johnson",
      startTime: new Date(Date.now() - 10 * 60 * 1000), // 10 minutes ago
      timeRemaining: 20 * 60, // 20 minutes in seconds
      isPaused: true,
      warnings: 0
    }
  ])

  const handleSettingChange = (field: keyof TimeSettings, value: any) => {
    setSettings(prev => ({ ...prev, [field]: value }))
  }

  const handleSave = () => {
    if (settings.timeLimit <= 0) {
      toast.error('Time limit must be greater than 0')
      return
    }
    if (settings.warningTime >= settings.timeLimit) {
      toast.error('Warning time must be less than total time limit')
      return
    }
    
    onSave(settings)
    toast.success('Time settings saved successfully')
  }

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = seconds % 60
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`
  }

  const getTimeStatus = (timeRemaining: number, totalTime: number) => {
    const percentage = (timeRemaining / totalTime) * 100
    if (percentage > 50) return { color: 'text-green-600', bg: 'bg-green-100', status: 'Good' }
    if (percentage > 20) return { color: 'text-yellow-600', bg: 'bg-yellow-100', status: 'Warning' }
    return { color: 'text-red-600', bg: 'bg-red-100', status: 'Critical' }
  }

  const handlePauseResume = (attemptId: string) => {
    setActiveAttempts(prev => 
      prev.map(attempt => 
        attempt.id === attemptId 
          ? { ...attempt, isPaused: !attempt.isPaused }
          : attempt
      )
    )
    toast.success('Timer updated')
  }

  const handleExtendTime = (attemptId: string, additionalMinutes: number) => {
    setActiveAttempts(prev => 
      prev.map(attempt => 
        attempt.id === attemptId 
          ? { ...attempt, timeRemaining: attempt.timeRemaining + (additionalMinutes * 60) }
          : attempt
      )
    )
    toast.success(`Extended time by ${additionalMinutes} minutes`)
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Time Management</h2>
          <p className="text-muted-foreground">
            Configure timing settings and monitor active quiz attempts
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSave}>
            <Settings className="h-4 w-4 mr-2" />
            Save Settings
          </Button>
        </div>
      </div>

      <Tabs defaultValue="settings" className="space-y-6">
        <TabsList>
          <TabsTrigger value="settings">Time Settings</TabsTrigger>
          <TabsTrigger value="active">Active Attempts</TabsTrigger>
          <TabsTrigger value="extensions">Time Extensions</TabsTrigger>
        </TabsList>

        <TabsContent value="settings" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Basic Time Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Timer className="h-5 w-5" />
                  Basic Time Settings
                </CardTitle>
                <CardDescription>
                  Configure the main timing parameters for this quiz
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="timeLimit">Time Limit (minutes)</Label>
                  <Input
                    id="timeLimit"
                    type="number"
                    min="1"
                    max="300"
                    value={settings.timeLimit}
                    onChange={(e) => handleSettingChange('timeLimit', parseInt(e.target.value) || 30)}
                  />
                </div>

                <div>
                  <Label htmlFor="warningTime">Warning Time (minutes before end)</Label>
                  <Input
                    id="warningTime"
                    type="number"
                    min="1"
                    max={settings.timeLimit - 1}
                    value={settings.warningTime}
                    onChange={(e) => handleSettingChange('warningTime', parseInt(e.target.value) || 5)}
                  />
                </div>

                <div>
                  <Label htmlFor="gracePeriod">Grace Period (seconds)</Label>
                  <Input
                    id="gracePeriod"
                    type="number"
                    min="0"
                    max="300"
                    value={settings.gracePeriod}
                    onChange={(e) => handleSettingChange('gracePeriod', parseInt(e.target.value) || 30)}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Extra time allowed after the timer expires
                  </p>
                </div>

                <div>
                  <Label htmlFor="timeZone">Time Zone</Label>
                  <Select value={settings.timeZone} onValueChange={(value) => handleSettingChange('timeZone', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="UTC">UTC</SelectItem>
                      <SelectItem value="America/New_York">Eastern Time</SelectItem>
                      <SelectItem value="America/Chicago">Central Time</SelectItem>
                      <SelectItem value="America/Denver">Mountain Time</SelectItem>
                      <SelectItem value="America/Los_Angeles">Pacific Time</SelectItem>
                      <SelectItem value="Europe/London">London</SelectItem>
                      <SelectItem value="Europe/Paris">Paris</SelectItem>
                      <SelectItem value="Asia/Tokyo">Tokyo</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>

            {/* Timer Behavior */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  Timer Behavior
                </CardTitle>
                <CardDescription>
                  Control how the timer behaves during the quiz
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="autoSubmit"
                    checked={settings.autoSubmit}
                    onCheckedChange={(checked) => handleSettingChange('autoSubmit', checked)}
                  />
                  <Label htmlFor="autoSubmit">Auto-submit when time expires</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="showTimer"
                    checked={settings.showTimer}
                    onCheckedChange={(checked) => handleSettingChange('showTimer', checked)}
                  />
                  <Label htmlFor="showTimer">Show timer to students</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="allowPause"
                    checked={settings.allowPause}
                    onCheckedChange={(checked) => handleSettingChange('allowPause', checked)}
                  />
                  <Label htmlFor="allowPause">Allow students to pause timer</Label>
                </div>

                <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                  <div className="flex items-start gap-3">
                    <Clock className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5" />
                    <div>
                      <h4 className="font-semibold text-blue-800 dark:text-blue-200 mb-1">
                        Current Settings Summary
                      </h4>
                      <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                        <li>• Total time: {settings.timeLimit} minutes</li>
                        <li>• Warning at: {settings.warningTime} minutes remaining</li>
                        <li>• Grace period: {settings.gracePeriod} seconds</li>
                        <li>• Auto-submit: {settings.autoSubmit ? 'Enabled' : 'Disabled'}</li>
                        <li>• Timer visible: {settings.showTimer ? 'Yes' : 'No'}</li>
                        <li>• Pause allowed: {settings.allowPause ? 'Yes' : 'No'}</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="active" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Play className="h-5 w-5" />
                Active Quiz Attempts
              </CardTitle>
              <CardDescription>
                Monitor and manage currently active quiz attempts
              </CardDescription>
            </CardHeader>
            <CardContent>
              {activeAttempts.length === 0 ? (
                <div className="text-center py-8">
                  <Clock className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No Active Attempts</h3>
                  <p className="text-muted-foreground">
                    No students are currently taking this quiz.
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {activeAttempts.map((attempt) => {
                    const timeStatus = getTimeStatus(attempt.timeRemaining, settings.timeLimit * 60)
                    return (
                      <motion.div
                        key={attempt.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="p-4 border rounded-lg"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-4">
                            <div>
                              <h4 className="font-semibold">{attempt.userName}</h4>
                              <p className="text-sm text-muted-foreground">
                                Started: {attempt.startTime.toLocaleTimeString()}
                              </p>
                            </div>
                            <div className="flex items-center gap-2">
                              <Badge className={timeStatus.bg + ' ' + timeStatus.color}>
                                {timeStatus.status}
                              </Badge>
                              {attempt.isPaused && (
                                <Badge variant="outline">
                                  <Pause className="h-3 w-3 mr-1" />
                                  Paused
                                </Badge>
                              )}
                              {attempt.warnings > 0 && (
                                <Badge variant="outline" className="text-orange-600">
                                  <Bell className="h-3 w-3 mr-1" />
                                  {attempt.warnings} warning{attempt.warnings !== 1 ? 's' : ''}
                                </Badge>
                              )}
                            </div>
                          </div>
                          
                          <div className="flex items-center gap-4">
                            <div className="text-right">
                              <div className={`text-lg font-bold ${timeStatus.color}`}>
                                {formatTime(attempt.timeRemaining)}
                              </div>
                              <div className="text-xs text-muted-foreground">
                                remaining
                              </div>
                            </div>
                            
                            <div className="flex items-center gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handlePauseResume(attempt.id)}
                              >
                                {attempt.isPaused ? (
                                  <>
                                    <Play className="h-4 w-4 mr-1" />
                                    Resume
                                  </>
                                ) : (
                                  <>
                                    <Pause className="h-4 w-4 mr-1" />
                                    Pause
                                  </>
                                )}
                              </Button>
                              
                              <Select onValueChange={(value) => handleExtendTime(attempt.id, parseInt(value))}>
                                <SelectTrigger className="w-32">
                                  <SelectValue placeholder="Extend" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="5">+5 min</SelectItem>
                                  <SelectItem value="10">+10 min</SelectItem>
                                  <SelectItem value="15">+15 min</SelectItem>
                                  <SelectItem value="30">+30 min</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                          </div>
                        </div>
                      </motion.div>
                    )
                  })}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="extensions" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <RotateCcw className="h-5 w-5" />
                Time Extensions
              </CardTitle>
              <CardDescription>
                Manage special time accommodations for individual students
              </CardDescription>
            </CardHeader>
            <CardContent>
              {settings.extendedTime && settings.extendedTime.length > 0 ? (
                <div className="space-y-4">
                  {settings.extendedTime.map((extension, index) => (
                    <div key={index} className="p-4 border rounded-lg">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-semibold">{extension.userName}</h4>
                          <p className="text-sm text-muted-foreground">
                            Reason: {extension.reason}
                          </p>
                        </div>
                        <div className="text-right">
                          <div className="text-lg font-bold text-green-600">
                            +{extension.additionalMinutes} min
                          </div>
                          <div className="text-xs text-muted-foreground">
                            extended time
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <RotateCcw className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No Time Extensions</h3>
                  <p className="text-muted-foreground">
                    No special time accommodations have been granted for this quiz.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
