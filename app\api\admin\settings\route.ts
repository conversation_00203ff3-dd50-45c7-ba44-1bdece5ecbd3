import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { emailService } from '@/lib/email-service'
import { z } from 'zod'

const systemSettingsSchema = z.object({
  siteName: z.string().min(1).max(100),
  siteDescription: z.string().max(500),
  siteUrl: z.string().url(),
  adminEmail: z.string().email(),
  allowRegistration: z.boolean(),
  requireEmailVerification: z.boolean(),
  defaultUserRole: z.enum(['STUDENT', 'ADMIN']),
  maxFileSize: z.number().min(1).max(100),
  allowedFileTypes: z.array(z.string()),
  enableNotifications: z.boolean(),
  enableAnalytics: z.boolean(),
  maintenanceMode: z.boolean(),
  theme: z.enum(['light', 'dark', 'system']),
  timezone: z.string(),
  language: z.string()
})

const securitySettingsSchema = z.object({
  sessionTimeout: z.number().min(1).max(168),
  maxLoginAttempts: z.number().min(3).max(20),
  passwordMinLength: z.number().min(6).max(32),
  requireStrongPassword: z.boolean(),
  enableTwoFactor: z.boolean()
})

const emailSettingsSchema = z.object({
  smtpHost: z.string(),
  smtpPort: z.number().min(1).max(65535),
  smtpUser: z.string(),
  smtpPassword: z.string(),
  smtpSecure: z.boolean(),
  fromEmail: z.string().email(),
  fromName: z.string(),
  enableEmailNotifications: z.boolean()
})

const settingsUpdateSchema = z.object({
  system: systemSettingsSchema.partial(),
  security: securitySettingsSchema.partial(),
  email: emailSettingsSchema.partial()
})

// GET /api/admin/settings - Get system settings
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    
  },
  async (request: NextRequest) => {
    try {
      console.log('Fetching system settings...')

      // Get settings from database or return defaults
      const settings = await prisma.systemSetting.findMany()
      console.log(`Found ${settings.length} settings in database`)

      // Convert array of settings to organized object
      const settingsMap = settings.reduce((acc, setting) => {
        const [category, key] = setting.key.split('.')
        if (!acc[category]) acc[category] = {}

        // Parse JSON values
        let value: any = setting.value
        try {
          // Try to parse as JSON for arrays and objects
          const parsed = JSON.parse(setting.value)
          value = parsed
        } catch {
          // Keep as string if not valid JSON
          // Convert string booleans and numbers
          if (setting.value === 'true') value = true
          else if (setting.value === 'false') value = false
          else if (!isNaN(Number(setting.value)) && setting.value !== '') value = Number(setting.value)
        }

        acc[category][key] = value
        return acc
      }, {} as any)

      // Provide defaults for missing settings
      const defaultSettings = {
        system: {
          siteName: 'QuizMaster',
          siteDescription: 'AI-Powered Learning Platform',
          siteUrl: process.env.NEXTAUTH_URL || 'http://localhost:3000',
          adminEmail: '<EMAIL>',
          allowRegistration: true,
          requireEmailVerification: false,
          defaultUserRole: 'STUDENT',
          maxFileSize: 10,
          allowedFileTypes: ['pdf', 'doc', 'docx', 'txt', 'jpg', 'png'],
          enableNotifications: true,
          enableAnalytics: true,
          maintenanceMode: false,
          theme: 'system',
          timezone: 'UTC',
          language: 'en'
        },
        security: {
          sessionTimeout: 24,
          maxLoginAttempts: 5,
          passwordMinLength: 8,
          requireStrongPassword: true,
          enableTwoFactor: false
        },
        email: {
          smtpHost: '',
          smtpPort: 587,
          smtpUser: '',
          smtpPassword: '',
          smtpSecure: true,
          fromEmail: '',
          fromName: 'QuizMaster',
          enableEmailNotifications: true
        }
      }

      // Merge defaults with stored settings
      const finalSettings = {
        system: { ...defaultSettings.system, ...settingsMap.system },
        security: { ...defaultSettings.security, ...settingsMap.security },
        email: { ...defaultSettings.email, ...settingsMap.email }
      }

      return APIResponse.success(finalSettings, 'Settings retrieved successfully')
    } catch (error) {
      console.error('Error fetching settings:', error)
      return APIResponse.error('Failed to fetch settings', 500)
    }
  }
)

// PUT /api/admin/settings - Update system settings
export const PUT = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    validateBody: settingsUpdateSchema
  },
  async (request: NextRequest, { validatedBody: body, user }: any) => {
    try {
      console.log('Updating system settings for user:', user.email)
      console.log('Request body:', JSON.stringify(body, null, 2))
      const { system, security, email } = body

      // Validate that at least one section is provided
      if (!system && !security && !email) {
        return APIResponse.error('No settings provided to update', 400)
      }

      // Prepare settings for database storage
      const settingsToUpdate: Array<{
        key: string
        value: string
        category: string
        description: string
      }> = []

      // Process system settings
      if (system) {
        console.log('Processing system settings:', Object.keys(system))
        for (const [key, value] of Object.entries(system)) {
          settingsToUpdate.push({
            key: `system.${key}`,
            value: typeof value === 'object' ? JSON.stringify(value) : String(value),
            category: 'system',
            description: `System setting: ${key}`
          })
        }
      }

      // Process security settings
      if (security) {
        console.log('Processing security settings:', Object.keys(security))
        for (const [key, value] of Object.entries(security)) {
          settingsToUpdate.push({
            key: `security.${key}`,
            value: typeof value === 'object' ? JSON.stringify(value) : String(value),
            category: 'security',
            description: `Security setting: ${key}`
          })
        }
      }

      // Process email settings
      if (email) {
        console.log('Processing email settings:', Object.keys(email))
        for (const [key, value] of Object.entries(email)) {
          // Don't store empty passwords
          if (key === 'smtpPassword' && !value) {
            console.log('Skipping empty SMTP password')
            continue
          }

          settingsToUpdate.push({
            key: `email.${key}`,
            value: typeof value === 'object' ? JSON.stringify(value) : String(value),
            category: 'email',
            description: `Email setting: ${key}`
          })
        }
      }

      console.log(`Updating ${settingsToUpdate.length} settings`)

      // Update settings in database using transaction for consistency
      const result = await prisma.$transaction(async (tx) => {
        const updatedSettings = []

        for (const setting of settingsToUpdate) {
          const updatedSetting = await tx.systemSetting.upsert({
            where: { key: setting.key },
            update: {
              value: setting.value,
              updatedAt: new Date()
            },
            create: {
              key: setting.key,
              value: setting.value,
              category: setting.category,
              description: setting.description
            }
          })
          updatedSettings.push(updatedSetting)
        }

        return updatedSettings
      })

      console.log(`Successfully updated ${result.length} settings`)

      // If email settings were updated, reinitialize email service
      if (email && Object.keys(email).length > 0) {
        try {
          console.log('Reinitializing email service after settings update...')
          await emailService.reinitialize()
          console.log('Email service reinitialized successfully')
        } catch (error) {
          console.error('Failed to reinitialize email service:', error)
          // Don't fail the entire request if email service fails
        }
      }

      return APIResponse.success(
        {
          updated: result.length,
          settings: result.map(s => ({ key: s.key, value: s.value }))
        },
        `Successfully updated ${result.length} setting(s)`
      )
    } catch (error: any) {
      console.error('Error updating settings:', error)

      // Provide more specific error messages
      let errorMessage = 'Failed to update settings'
      let statusCode = 500

      if (error.code === 'P2002') {
        // Prisma unique constraint violation
        errorMessage = 'Duplicate setting key detected'
        statusCode = 400
      } else if (error.code === 'P2025') {
        // Prisma record not found
        errorMessage = 'Setting not found'
        statusCode = 404
      } else if (error.message) {
        // Include the actual error message for debugging
        errorMessage = `Failed to update settings: ${error.message}`
      }

      return APIResponse.error(errorMessage, statusCode)
    }
  }
)
