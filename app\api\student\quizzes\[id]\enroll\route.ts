import { NextRequest } from 'next/server'
 import { createAP<PERSON>Handler, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { NotificationEvents } from '@/lib/notification-events'

// POST /api/student/quizzes/[id]/enroll - Enroll in a quiz
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT',
     
  },
  async (request: NextRequest, { user, params }) => {
    const resolvedParams = await params
    const quizId = resolvedParams?.id as string

    if (!quizId) {
      return APIResponse.error('Quiz ID is required', 400)
    }

    try {
      console.log(`Enrollment attempt for quiz ${quizId} by user ${user.id}`)

      // Check if quiz exists and is published
      const quiz = await prisma.quiz.findUnique({
        where: { id: quizId },
        select: {
          id: true,
          title: true,
          isPublished: true,
          startTime: true,
          endTime: true,
          maxAttempts: true
        }
      })

      if (!quiz) {
        return APIResponse.error('Quiz not found', 404)
      }

      if (!quiz.isPublished) {
        return APIResponse.error('Quiz is not available for enrollment', 400)
      }

      // Check if quiz is within time bounds
      const now = new Date()
      if (quiz.startTime && quiz.startTime > now) {
        return APIResponse.error('Quiz has not started yet', 400)
      }

      if (quiz.endTime && quiz.endTime < now) {
        return APIResponse.error('Quiz enrollment has ended', 400)
      }

      // Check if already enrolled
      const existingEnrollment = await prisma.quizEnrollment.findFirst({
        where: {
          quizId,
          userId: user.id
        }
      })

      if (existingEnrollment) {
        return APIResponse.error('Already enrolled in this quiz', 400)
      }

      // Check if user has exceeded max attempts
      if (quiz.maxAttempts > 0) {
        const attemptCount = await prisma.quizAttempt.count({
          where: {
            quizId,
            userId: user.id
          }
        })

        if (attemptCount >= quiz.maxAttempts) {
          return APIResponse.error('Maximum attempts exceeded', 400)
        }
      }

      // Create enrollment
      console.log(`Creating enrollment for quiz ${quizId} and user ${user.id}`)

      const enrollment = await prisma.quizEnrollment.create({
        data: {
          quizId,
          userId: user.id
        },
        include: {
          quiz: {
            select: {
              title: true,
              description: true,
              type: true,
              difficulty: true
            }
          }
        }
      })

      console.log(`Enrollment created successfully: ${enrollment.id}`)

      // Send enrollment confirmation notification
      try {
        await NotificationEvents.onQuizEnrolled(user.id, quiz.title, quizId)
      } catch (notificationError) {
        console.error('Failed to send enrollment notification:', notificationError)
        // Don't fail the enrollment if notification fails
      }

      return APIResponse.success(
        {
          enrollmentId: enrollment.id,
          quiz: enrollment.quiz,
          enrolledAt: enrollment.enrolledAt
        },
        'Successfully enrolled in quiz'
      )

    } catch (error) {
      console.error('Error enrolling in quiz:', error)
      console.error('Error details:', error instanceof Error ? error.message : String(error))
      console.error('Stack trace:', error instanceof Error ? error.stack : 'No stack trace')

      return APIResponse.error(
        error instanceof Error ? error.message : 'Failed to enroll in quiz',
        500
      )
    }
  }
)

// DELETE /api/student/quizzes/[id]/enroll - Unenroll from a quiz
export const DELETE = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT',
     
  },
  async (request: NextRequest, { user, params }) => {
    const resolvedParams = await params
    const quizId = resolvedParams?.id as string

    if (!quizId) {
      return APIResponse.error('Quiz ID is required', 400)
    }

    try {
      // Check if enrolled
      const enrollment = await prisma.quizEnrollment.findFirst({
        where: {
          quizId,
          userId: user.id
        }
      })

      if (!enrollment) {
        return APIResponse.error('Not enrolled in this quiz', 400)
      }

      // Check if user has already attempted the quiz
      const attemptCount = await prisma.quizAttempt.count({
        where: {
          quizId,
          userId: user.id
        }
      })

      if (attemptCount > 0) {
        return APIResponse.error('Cannot unenroll after attempting the quiz', 400)
      }

      // Delete enrollment
      await prisma.quizEnrollment.delete({
        where: { id: enrollment.id }
      })

      return APIResponse.success(
        null,
        'Successfully unenrolled from quiz'
      )

    } catch (error) {
      console.error('Error unenrolling from quiz:', error)
      return APIResponse.error('Failed to unenroll from quiz', 500)
    }
  }
)
