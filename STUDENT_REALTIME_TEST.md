# Student Real-time Features Test Guide

This guide helps you test all the real-time features for students to ensure they work correctly.

## 🚀 **Setup**

1. **Start Both Servers:**
   ```bash
   npm run dev:full
   ```
   
2. **Verify Servers Are Running:**
   - Next.js App: http://localhost:3000 ✅
   - Socket Server: http://localhost:3001 ✅

## 🧪 **Student-Side Testing**

### **1. Test Student Socket Connection**

1. **Login as a Student**
2. **Navigate to any student page** (e.g., `/student/dashboard`)
3. **Check Browser Console** for:
   ```
   🔌 Initializing socket for student: [Student Name]
   🔌 Socket connected: [socket-id]
   ✅ Connected to socket server: {...}
   🔐 Authenticating user: [Student Name]
   ✅ Authentication successful: {...}
   ```

### **2. Test Student Notifications Page**

1. **Navigate to:** `/student/notifications`
2. **Verify Connection Status:** Should show "Connected" badge
3. **Test Notification Reception:**
   - Go to **Test** tab
   - Click "Send Test Notification"
   - Should see toast notification appear
   - Should see notification in the list

### **3. Test Admin → Student Notifications**

1. **Open Two Browser Windows/Tabs:**
   - **Tab 1:** Login as Admin → `/admin/realtime`
   - **Tab 2:** Login as Student → `/student/notifications`

2. **Send Notification from Admin:**
   - In Admin tab, fill out notification form:
     - **Title:** "Test from Admin"
     - **Message:** "This is a real-time test!"
     - **Type:** Info
   - Click "Send Notification" (broadcast to all)

3. **Verify Student Receives:**
   - Student tab should show toast notification immediately
   - Notification should appear in the notifications list
   - Connection status should remain "Connected"

### **4. Test Live Quiz Progress**

1. **Start a Quiz as Student:**
   - Navigate to `/student/quiz/[quiz-id]`
   - Begin taking the quiz

2. **Monitor from Admin:**
   - In admin tab (`/admin/realtime`), watch for quiz progress updates
   - Should see real-time progress as student answers questions

3. **Verify Progress Updates:**
   - Answer questions in student tab
   - Navigate between questions
   - Check admin tab shows live updates

## 🔍 **What to Look For**

### **✅ Success Indicators:**

1. **Connection Status:**
   - Student shows "Connected" badge
   - No connection errors in console

2. **Notifications:**
   - Toast notifications appear instantly
   - Notifications saved to database
   - Real-time delivery without page refresh

3. **Quiz Progress:**
   - Live updates sent when answering questions
   - Real-time progress visible to admins
   - No delays or missed updates

4. **Console Logs:**
   ```
   🔌 Socket connected: [id]
   ✅ Student authenticated: {...}
   📨 Student received notification: {...}
   📊 Quiz progress update: {...}
   ```

### **❌ Failure Indicators:**

1. **Connection Issues:**
   - "Disconnected" status
   - Console errors about socket connection
   - No real-time updates

2. **Notification Problems:**
   - No toast notifications
   - Notifications not appearing in list
   - Delayed or missing notifications

3. **Quiz Progress Issues:**
   - No live updates sent
   - Admin not seeing student progress
   - Console errors during quiz

## 🛠️ **Troubleshooting**

### **Connection Issues:**
```bash
# Check if socket server is running
curl http://localhost:3001

# Restart both servers
npm run dev:full
```

### **Authentication Issues:**
- Clear browser localStorage
- Re-login as student
- Check user session data

### **Notification Issues:**
- Check browser console for errors
- Verify socket connection status
- Test with different notification types

## 📊 **Test Scenarios**

### **Scenario 1: Basic Notification**
1. Admin sends info notification
2. Student receives instantly
3. Toast appears with correct styling
4. Notification saved to list

### **Scenario 2: Multiple Students**
1. Open multiple student sessions
2. Admin broadcasts notification
3. All students receive simultaneously
4. Each student sees notification in their list

### **Scenario 3: Quiz Progress**
1. Student starts quiz
2. Admin monitors real-time progress
3. Student answers questions
4. Admin sees live updates immediately

### **Scenario 4: Connection Recovery**
1. Stop socket server
2. Student shows "Disconnected"
3. Restart socket server
4. Student reconnects automatically

## 🎯 **Expected Results**

After successful testing, you should have:

- ✅ **Real-time notifications** working for students
- ✅ **Live quiz progress** tracking
- ✅ **Instant message delivery** from admin to students
- ✅ **Connection status monitoring**
- ✅ **Automatic reconnection** on connection loss
- ✅ **Toast notifications** with proper styling
- ✅ **Notification history** saved and displayed

## 🚨 **Common Issues & Solutions**

### **Issue: Student not receiving notifications**
**Solution:** Check socket authentication and connection status

### **Issue: Quiz progress not updating**
**Solution:** Verify quiz ID is passed correctly to live progress hook

### **Issue: Connection keeps dropping**
**Solution:** Check firewall settings and CORS configuration

### **Issue: Notifications appear twice**
**Solution:** Check for duplicate event listeners in useEffect

## 📝 **Test Checklist**

- [ ] Socket server starts successfully
- [ ] Student connects to socket server
- [ ] Student authentication works
- [ ] Admin can send notifications to students
- [ ] Students receive notifications instantly
- [ ] Toast notifications appear with correct styling
- [ ] Notifications are saved to database
- [ ] Quiz progress updates in real-time
- [ ] Connection status is accurate
- [ ] Automatic reconnection works
- [ ] Multiple students can connect simultaneously
- [ ] No console errors during normal operation

## 🎉 **Success!**

If all tests pass, your student-side real-time features are working correctly! Students can now:

- Receive instant notifications from admins
- See their quiz progress tracked in real-time
- Monitor their connection status
- Get automatic reconnection on network issues

The real-time system is now fully functional for both admin and student users! 🚀
