"use client"

import { useState, useEffect } from "react"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { But<PERSON> } from "@/components/ui/button"
import { X } from "lucide-react"

interface Subject {
  id: string
  name: string
  chapters: Chapter[]
}

interface Chapter {
  id: string
  name: string
  subjectId: string
  topics: Topic[]
}

interface Topic {
  id: string
  name: string
  chapterId: string
}

interface CategoryFilterProps {
  selectedSubjectId?: string
  selectedChapterId?: string
  selectedTopicId?: string
  onFilterChange: (filters: {
    subjectId?: string
    chapterId?: string
    topicId?: string
  }) => void
  className?: string
  userRole?: 'ADMIN' | 'STUDENT' // Add role prop to determine API endpoint
}

export function CategoryFilter({
  selectedSubjectId,
  selectedChapterId,
  selectedTopicId,
  onFilterChange,
  className,
  userRole = 'ADMIN' // Default to admin for backward compatibility
}: CategoryFilterProps) {
  const [subjects, setSubjects] = useState<Subject[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchSubjects()
  }, [])

  const fetchSubjects = async () => {
    try {
      setLoading(true)
      setError(null)

      // Use appropriate API endpoint based on user role
      const apiEndpoint = userRole === 'STUDENT'
        ? '/api/student/categories/subjects'
        : '/api/admin/categories/subjects'

      const response = await fetch(apiEndpoint)

      if (!response.ok) {
        throw new Error(`Failed to fetch subjects: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()

      if (data.success) {
        setSubjects(data.subjects || [])
      } else {
        const errorMsg = data.error || 'Unknown error occurred'
        setError(errorMsg)
        console.error('API returned error:', errorMsg)
        setSubjects([])
      }
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Failed to fetch subjects'
      setError(errorMsg)
      console.error('Error fetching subjects:', error)
      setSubjects([])
    } finally {
      setLoading(false)
    }
  }

  const clearFilters = () => {
    onFilterChange({})
  }

  const hasActiveFilters = selectedSubjectId || selectedChapterId || selectedTopicId

  const selectedSubject = subjects.find(s => s.id === selectedSubjectId)
  const selectedChapter = selectedSubject?.chapters.find(c => c.id === selectedChapterId)

  if (loading) {
    return (
      <div className={`flex gap-2 ${className}`}>
        <div className="animate-pulse">
          <div className="h-10 bg-gray-200 rounded w-32"></div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className={`flex gap-2 items-center ${className}`}>
        <div className="text-red-600 text-sm">
          Error loading categories: {error}
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={fetchSubjects}
          className="ml-2"
        >
          Retry
        </Button>
      </div>
    )
  }

  return (
    <div className={`flex gap-2 items-center ${className}`}>
      {/* Subject Filter */}
      <Select
        value={selectedSubjectId || "all"}
        onValueChange={(value) => {
          onFilterChange({
            subjectId: value === "all" ? undefined : value,
            chapterId: undefined,
            topicId: undefined
          })
        }}
      >
        <SelectTrigger className="w-48">
          <SelectValue placeholder="All subjects" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">All subjects</SelectItem>
          {subjects.map((subject) => (
            <SelectItem key={subject.id} value={subject.id}>
              {subject.name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      {/* Chapter Filter */}
      {selectedSubjectId && selectedSubject && (
        <Select
          value={selectedChapterId || "all"}
          onValueChange={(value) => {
            onFilterChange({
              subjectId: selectedSubjectId,
              chapterId: value === "all" ? undefined : value,
              topicId: undefined
            })
          }}
        >
          <SelectTrigger className="w-48">
            <SelectValue placeholder="All chapters" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All chapters</SelectItem>
            {selectedSubject.chapters.map((chapter) => (
              <SelectItem key={chapter.id} value={chapter.id}>
                {chapter.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      )}

      {/* Topic Filter */}
      {selectedChapterId && selectedChapter && (
        <Select
          value={selectedTopicId || "all"}
          onValueChange={(value) => {
            onFilterChange({
              subjectId: selectedSubjectId,
              chapterId: selectedChapterId,
              topicId: value === "all" ? undefined : value
            })
          }}
        >
          <SelectTrigger className="w-48">
            <SelectValue placeholder="All topics" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All topics</SelectItem>
            {selectedChapter.topics.map((topic) => (
              <SelectItem key={topic.id} value={topic.id}>
                {topic.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      )}

      {/* Clear Filters Button */}
      {hasActiveFilters && (
        <Button variant="outline" size="sm" onClick={clearFilters}>
          <X className="h-4 w-4 mr-1" />
          Clear
        </Button>
      )}
    </div>
  )
}
