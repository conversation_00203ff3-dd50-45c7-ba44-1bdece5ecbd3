import { NextRequest, NextResponse } from 'next/server'
import { APIDocumentation, registerAPIEndpoints, generateMarkdownDocs } from '@/lib/api-docs'

// Initialize API documentation
registerAPIEndpoints()

// GET /api/docs - Get API documentation
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const format = searchParams.get('format') || 'json'

  try {
    switch (format) {
      case 'openapi':
      case 'swagger':
        const openApiSpec = APIDocumentation.generateOpenAPI()
        return NextResponse.json(openApiSpec)

      case 'markdown':
        const markdownDocs = generateMarkdownDocs()
        return new NextResponse(markdownDocs, {
          headers: {
            'Content-Type': 'text/markdown',
            'Content-Disposition': 'attachment; filename="api-docs.md"'
          }
        })

      case 'json':
      default:
        const endpoints = APIDocumentation.getAll()
        return NextResponse.json({
          success: true,
          data: {
            endpoints,
            totalEndpoints: endpoints.length,
            tags: [...new Set(endpoints.flatMap(e => e.tags))],
            version: '1.0.0',
            lastUpdated: new Date().toISOString()
          },
          message: 'API documentation retrieved successfully'
        })
    }
  } catch (error) {
    console.error('Error generating API documentation:', error)
    return NextResponse.json(
      {
        success: false,
        error: {
          message: 'Failed to generate API documentation',
          code: 'DOCS_GENERATION_ERROR',
          timestamp: new Date().toISOString()
        }
      },
      { status: 500 }
    )
  }
}
