"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  Shield,
  Users,
  FileText,
  BarChart3,
  Settings,
  TrendingUp,
  Clock,
  Award,
  Activity,
  Plus,
  Eye,
  Edit,
  Trash2,
  RefreshCw,
  AlertCircle,
  Bell,
  Calendar
} from "lucide-react"
import { toast } from "@/lib/toast-utils"

interface DashboardStats {
  totalUsers: number
  activeUsers: number
  totalQuizzes: number
  totalAttempts: number
  averageScore: number
  recentActivity: Array<{
    id: string
    type: 'quiz_created' | 'quiz_attempt' | 'user_registered'
    title: string
    description: string
    timestamp: string
    user?: {
      name: string
      email: string
    }
  }>
  trends: {
    usersGrowth: number
    quizzesGrowth: number
    attemptsGrowth: number
    scoreImprovement: number
  }
  performance: {
    quizCompletionRate: number
    userEngagementRate: number
  }
  systemStatus: {
    serverStatus: 'online' | 'offline' | 'maintenance'
    databaseStatus: 'healthy' | 'warning' | 'error'
    aiServiceStatus: 'active' | 'inactive' | 'error'
    storageUsed: number
    lastBackup: string
  }
}

export default function AdminDashboard() {
  const { data: session } = useSession()
  const [dashboardStats, setDashboardStats] = useState<DashboardStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)

  useEffect(() => {
    fetchDashboardStats()
  }, [])

  const fetchDashboardStats = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/admin/dashboard/stats')

      if (!response.ok) {
        throw new Error('Failed to fetch dashboard stats')
      }

      const data = await response.json()
      setDashboardStats(data.data)
    } catch (error) {
      console.error('Error fetching dashboard stats:', error)
      toast.error('Failed to load dashboard statistics')
    } finally {
      setLoading(false)
    }
  }

  const handleRefresh = async () => {
    setRefreshing(true)
    await fetchDashboardStats()
    setRefreshing(false)
    toast.success('Dashboard refreshed')
  }

  // Create stats array from API data
  const stats = dashboardStats ? [
    {
      title: "Total Users",
      value: dashboardStats.totalUsers.toLocaleString(),
      icon: Users,
      change: `${dashboardStats.trends.usersGrowth >= 0 ? '+' : ''}${dashboardStats.trends.usersGrowth}%`,
      trend: dashboardStats.trends.usersGrowth >= 0 ? "up" : "down"
    },
    {
      title: "Active Quizzes",
      value: dashboardStats.totalQuizzes.toLocaleString(),
      icon: FileText,
      change: `${dashboardStats.trends.quizzesGrowth >= 0 ? '+' : ''}${dashboardStats.trends.quizzesGrowth}%`,
      trend: dashboardStats.trends.quizzesGrowth >= 0 ? "up" : "down"
    },
    {
      title: "Quiz Attempts",
      value: dashboardStats.totalAttempts.toLocaleString(),
      icon: Activity,
      change: `${dashboardStats.trends.attemptsGrowth >= 0 ? '+' : ''}${dashboardStats.trends.attemptsGrowth}%`,
      trend: dashboardStats.trends.attemptsGrowth >= 0 ? "up" : "down"
    },
    {
      title: "Avg. Score",
      value: `${dashboardStats.averageScore}%`,
      icon: TrendingUp,
      change: `${dashboardStats.trends.scoreImprovement >= 0 ? '+' : ''}${dashboardStats.trends.scoreImprovement}%`,
      trend: dashboardStats.trends.scoreImprovement >= 0 ? "up" : "down"
    },
  ] : []

  const quickActions = [
    { title: "Create Quiz", description: "Generate a new quiz", icon: Plus, href: "/admin/quizzes/create", color: "bg-blue-500" },
    { title: "Manage Users", description: "View and edit users", icon: Users, href: "/admin/users", color: "bg-green-500" },
    { title: "View Analytics", description: "Check performance", icon: BarChart3, href: "/admin/analytics-dashboard", color: "bg-purple-500" },
    { title: "System Settings", description: "Configure platform", icon: Settings, href: "/admin/settings", color: "bg-orange-500" },
  ]

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-muted-foreground" />
            <p className="text-muted-foreground">Loading dashboard...</p>
          </div>
        </div>
      </div>
    )
  }

  if (!dashboardStats) {
    return (
      <div className="p-6 space-y-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <AlertCircle className="h-8 w-8 mx-auto mb-4 text-destructive" />
            <p className="text-muted-foreground mb-4">Failed to load dashboard data</p>
            <Button onClick={fetchDashboardStats}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Dashboard Overview</h1>
          <p className="text-muted-foreground mt-1">
            Welcome back, {session?.user?.name || 'Admin'}! Here's what's happening today.
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={refreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Badge variant="secondary" className="px-3 py-1">
            <Shield className="h-4 w-4 mr-1" />
            Admin Access
          </Badge>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <Card key={index} className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                {stat.title}
              </CardTitle>
              <div className="p-2 bg-primary/10 rounded-full">
                <stat.icon className="h-4 w-4 text-primary" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <p className="text-xs text-muted-foreground flex items-center gap-1">
                <TrendingUp className="h-3 w-3 text-green-600" />
                <span className="text-green-600">{stat.change}</span> from last month
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Quick Actions */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Common administrative tasks and shortcuts
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {quickActions.map((action, index) => (
                <Button
                  key={index}
                  variant="outline"
                  className="h-20 flex flex-col gap-2 hover:bg-accent"
                  asChild
                >
                  <a href={action.href}>
                    <div className={`p-2 rounded-full ${action.color} text-white`}>
                      <action.icon className="h-4 w-4" />
                    </div>
                    <div className="text-center">
                      <div className="font-semibold text-sm">{action.title}</div>
                      <div className="text-xs text-muted-foreground">{action.description}</div>
                    </div>
                  </a>
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>
              Latest system activities and updates
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {dashboardStats.recentActivity.length > 0 ? (
                dashboardStats.recentActivity.map((activity) => (
                  <div key={activity.id} className="flex items-start gap-3">
                    <div className="flex-shrink-0">
                      {activity.type === 'quiz_created' && (
                        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                          <FileText className="h-4 w-4 text-blue-600" />
                        </div>
                      )}
                      {activity.type === 'quiz_attempt' && (
                        <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                          <Activity className="h-4 w-4 text-green-600" />
                        </div>
                      )}
                      {activity.type === 'user_registered' && (
                        <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                          <Users className="h-4 w-4 text-purple-600" />
                        </div>
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium">{activity.title}</p>
                      <p className="text-xs text-muted-foreground truncate">{activity.description}</p>
                      <p className="text-xs text-muted-foreground">
                        {new Date(activity.timestamp).toLocaleString()}
                      </p>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-4">
                  <p className="text-sm text-muted-foreground">No recent activity</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Additional Insights */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Performance Overview */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Performance Overview
            </CardTitle>
            <CardDescription>
              Key metrics and performance indicators
            </CardDescription>
          </CardHeader>
          <CardContent>
            {dashboardStats ? (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Quiz Completion Rate</span>
                  <span className="text-sm text-muted-foreground">{dashboardStats.performance.quizCompletionRate}%</span>
                </div>
                <div className="w-full bg-muted rounded-full h-2">
                  <div className="bg-primary h-2 rounded-full" style={{ width: `${dashboardStats.performance.quizCompletionRate}%` }}></div>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Average Score</span>
                  <span className="text-sm text-muted-foreground">{dashboardStats.averageScore}%</span>
                </div>
                <div className="w-full bg-muted rounded-full h-2">
                  <div className="bg-green-500 h-2 rounded-full" style={{ width: `${dashboardStats.averageScore}%` }}></div>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">User Engagement</span>
                  <span className="text-sm text-muted-foreground">{dashboardStats.performance.userEngagementRate}%</span>
                </div>
                <div className="w-full bg-muted rounded-full h-2">
                  <div className="bg-blue-500 h-2 rounded-full" style={{ width: `${dashboardStats.performance.userEngagementRate}%` }}></div>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="animate-pulse">
                  <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
                  <div className="h-2 bg-muted rounded"></div>
                </div>
                <div className="animate-pulse">
                  <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
                  <div className="h-2 bg-muted rounded"></div>
                </div>
                <div className="animate-pulse">
                  <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
                  <div className="h-2 bg-muted rounded"></div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* System Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              System Status
            </CardTitle>
            <CardDescription>
              Current system health and status
            </CardDescription>
          </CardHeader>
          <CardContent>
            {dashboardStats ? (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Server Status</span>
                  <Badge className={
                    dashboardStats.systemStatus.serverStatus === 'online'
                      ? "bg-green-500 hover:bg-green-600"
                      : dashboardStats.systemStatus.serverStatus === 'maintenance'
                      ? "bg-yellow-500 hover:bg-yellow-600"
                      : "bg-red-500 hover:bg-red-600"
                  }>
                    {dashboardStats.systemStatus.serverStatus.charAt(0).toUpperCase() + dashboardStats.systemStatus.serverStatus.slice(1)}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Database</span>
                  <Badge className={
                    dashboardStats.systemStatus.databaseStatus === 'healthy'
                      ? "bg-green-500 hover:bg-green-600"
                      : dashboardStats.systemStatus.databaseStatus === 'warning'
                      ? "bg-yellow-500 hover:bg-yellow-600"
                      : "bg-red-500 hover:bg-red-600"
                  }>
                    {dashboardStats.systemStatus.databaseStatus.charAt(0).toUpperCase() + dashboardStats.systemStatus.databaseStatus.slice(1)}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">AI Service</span>
                  <Badge className={
                    dashboardStats.systemStatus.aiServiceStatus === 'active'
                      ? "bg-green-500 hover:bg-green-600"
                      : dashboardStats.systemStatus.aiServiceStatus === 'inactive'
                      ? "bg-yellow-500 hover:bg-yellow-600"
                      : "bg-red-500 hover:bg-red-600"
                  }>
                    {dashboardStats.systemStatus.aiServiceStatus.charAt(0).toUpperCase() + dashboardStats.systemStatus.aiServiceStatus.slice(1)}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Storage</span>
                  <Badge variant="outline">{dashboardStats.systemStatus.storageUsed}% Used</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Last Backup</span>
                  <span className="text-sm text-muted-foreground">
                    {new Date(dashboardStats.systemStatus.lastBackup).toLocaleString('en-US', {
                      month: 'short',
                      day: 'numeric',
                      hour: 'numeric',
                      minute: '2-digit',
                      hour12: true
                    })}
                  </span>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="flex items-center justify-between animate-pulse">
                    <div className="h-4 bg-muted rounded w-1/3"></div>
                    <div className="h-6 bg-muted rounded w-16"></div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
