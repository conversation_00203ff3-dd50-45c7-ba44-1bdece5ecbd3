'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { TypeAnimation } from 'react-type-animation';
import { ArrowRight, Play, Star, Users, BookOpen } from 'lucide-react';
import { AnimatedButton, MagneticButton } from '@/components/ui/animated-button';
import { FloatingBackground, AnimatedGrid, GradientOrb } from '@/components/ui/floating-shapes';
import { cn } from '@/lib/utils';

export function HeroSection() {
  const examNames = [
    'JEE',
    2000,
    'NEET',
    2000,
    'UPSC',
    2000,
    'SSC',
    2000,
    'CUET',
    2000,
    'Banking',
    2000,
    'NDA',
    2000,
    'CDS',
    2000,
    'CTET',
    2000,
  ];

  const stats = [
    { icon: Users, value: '500K+', label: 'Students' },
    { icon: Star, value: '98%', label: 'Success Rate' },
    { icon: BookOpen, value: '₹99', label: 'Courses' },
  ];

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-violet-50 via-white to-electric-50 dark:from-gray-900 dark:via-gray-800 dark:to-violet-900">
      {/* Animated Background Elements */}
      <FloatingBackground density="medium" colors={['violet', 'blue', 'cyan', 'pink']} />
      <AnimatedGrid size={60} opacity={0.05} />
      
      {/* Gradient Orbs */}
      <GradientOrb 
        className="top-20 left-20" 
        size={300} 
        colors={['#8b5cf6', '#3b82f6']} 
      />
      <GradientOrb 
        className="bottom-20 right-20" 
        size={250} 
        colors={['#06b6d4', '#f472b6']} 
      />

      {/* Main Content */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: 'easeOut' }}
          className="space-y-8"
        >
          {/* Badge */}
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="inline-flex items-center gap-2 px-4 py-2 rounded-full glass text-sm font-medium text-violet-700 dark:text-violet-300"
          >
            <Star className="w-4 h-4 text-yellow-500" />
            India's #1 Online Coaching Platform
          </motion.div>

          {/* Main Headline */}
          <div className="space-y-4">
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              className="text-4xl sm:text-6xl lg:text-7xl font-bold text-gray-900 dark:text-white leading-tight"
            >
              Crack Every{' '}
              <span className="text-gradient bg-gradient-primary">
                Indian Exam
              </span>
            </motion.h1>
            
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="text-xl sm:text-2xl lg:text-3xl font-semibold text-gray-700 dark:text-gray-300"
            >
              Smarter, Faster,{' '}
              <span className="text-gradient bg-gradient-secondary">
                Together
              </span>
            </motion.p>
          </div>

          {/* Typing Animation */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.5 }}
            className="text-2xl sm:text-3xl lg:text-4xl font-bold text-violet-600 dark:text-violet-400 h-16 flex items-center justify-center"
          >
            <TypeAnimation
              sequence={examNames}
              wrapper="span"
              speed={50}
              repeat={Infinity}
              className="text-gradient bg-gradient-accent"
            />
          </motion.div>

          {/* Description */}
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="text-lg sm:text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto leading-relaxed"
          >
            Join millions of students who trust our AI-powered platform for comprehensive exam preparation. 
            Expert teachers, personalized learning paths, and proven results.
          </motion.p>

          {/* CTA Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.7 }}
            className="flex flex-col sm:flex-row gap-4 justify-center items-center"
          >
            <MagneticButton
              variant="primary"
              size="xl"
              glow
              className="group"
            >
              Start Free Trial
              <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
            </MagneticButton>

            <AnimatedButton
              variant="outline"
              size="xl"
              className="group"
            >
              <Play className="w-5 h-5 group-hover:scale-110 transition-transform" />
              Watch Demo
            </AnimatedButton>
          </motion.div>

          {/* Stats */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="grid grid-cols-1 sm:grid-cols-3 gap-8 max-w-2xl mx-auto mt-16"
          >
            {stats.map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: 0.9 + index * 0.1 }}
                className="glass rounded-2xl p-6 text-center hover:shadow-glow transition-all duration-300 border border-white/30 dark:border-white/10"
              >
                <stat.icon className="w-8 h-8 mx-auto mb-3 text-violet-600 dark:text-violet-400" />
                <div className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white mb-1">
                  {stat.value}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  {stat.label}
                </div>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>
      </div>

      {/* Scroll Indicator */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1, delay: 1.5 }}
        className="absolute bottom-0 left-1/2 transform -translate-x-1/2 z-20"
      >
        <div className="flex flex-col items-center gap-2">
          <motion.div
            animate={{ y: [0, 8, 0] }}
            transition={{ duration: 2, repeat: Infinity, ease: 'easeInOut' }}
            className="w-6 h-10 border-2 border-violet-500 dark:border-violet-400 rounded-full flex justify-center bg-white/10 dark:bg-black/10 backdrop-blur-sm"
          >
            <motion.div
              animate={{ y: [0, 12, 0] }}
              transition={{ duration: 2, repeat: Infinity, ease: 'easeInOut' }}
              className="w-1 h-3 bg-violet-500 dark:bg-violet-400 rounded-full mt-2"
            />
          </motion.div>
          <motion.p
            animate={{ opacity: [0.5, 1, 0.5] }}
            transition={{ duration: 2, repeat: Infinity, ease: 'easeInOut' }}
            className="text-xs text-violet-600 dark:text-violet-400 font-medium"
          >
            Scroll to explore
          </motion.p>
        </div>
      </motion.div>
    </section>
  );
}
