import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { getBunnyStorage, ALLOWED_IMAGE_TYPES, ALLOWED_DOCUMENT_TYPES, MAX_FILE_SIZES } from '@/lib/bunny-storage'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Parse form data
    const formData = await request.formData()
    const files = formData.getAll('files') as File[]
    const uploadType = formData.get('type') as string || 'general'
    const folder = formData.get('folder') as string || 'uploads'

    if (!files || files.length === 0) {
      return NextResponse.json(
        { error: 'No files provided' },
        { status: 400 }
      )
    }

    // Validate files
    const validationErrors: string[] = []
    const bunnyStorage = getBunnyStorage()

    for (const file of files) {
      // Check file size
      const maxSize = uploadType === 'image' ? MAX_FILE_SIZES.image : 
                     uploadType === 'document' ? MAX_FILE_SIZES.document : 
                     MAX_FILE_SIZES.document

      if (file.size > maxSize) {
        validationErrors.push(`File ${file.name} exceeds maximum size of ${maxSize / (1024 * 1024)}MB`)
        continue
      }

      // Check file type
      const allowedTypes = uploadType === 'image' ? ALLOWED_IMAGE_TYPES : 
                          uploadType === 'document' ? ALLOWED_DOCUMENT_TYPES : 
                          [...ALLOWED_IMAGE_TYPES, ...ALLOWED_DOCUMENT_TYPES]

      if (!bunnyStorage.isValidFileType(file, allowedTypes)) {
        validationErrors.push(`File ${file.name} has invalid type. Allowed types: ${allowedTypes.join(', ')}`)
      }
    }

    if (validationErrors.length > 0) {
      return NextResponse.json(
        { error: 'Validation failed', details: validationErrors },
        { status: 400 }
      )
    }

    // Upload files to Bunny Storage
    const uploadResults = []
    const dbRecords = []

    for (const file of files) {
      const uploadResult = await bunnyStorage.uploadFile(file, {
        folder: `${folder}/${session.user.id}`,
        optimize: uploadType === 'image',
        maxSize: uploadType === 'image' ? MAX_FILE_SIZES.image : MAX_FILE_SIZES.document
      })

      if (uploadResult.success) {
        // Save file record to database
        const fileRecord = await prisma.file.create({
          data: {
            filename: uploadResult.filename!,
            originalName: file.name,
            url: uploadResult.url!,
            size: uploadResult.size!,
            mimeType: file.type,
            uploadType,
            folder,
            uploadedById: session.user.id
          }
        })

        dbRecords.push(fileRecord)
        uploadResults.push({
          success: true,
          file: fileRecord
        })
      } else {
        uploadResults.push({
          success: false,
          filename: file.name,
          error: uploadResult.error
        })
      }
    }

    return NextResponse.json({
      success: true,
      results: uploadResults,
      uploaded: dbRecords.length,
      total: files.length
    })

  } catch (error) {
    console.error('Upload error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type')
    const folder = searchParams.get('folder')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const skip = (page - 1) * limit

    // Build query filters
    const where: any = {
      uploadedById: session.user.id
    }

    if (type) {
      where.uploadType = type
    }

    if (folder) {
      where.folder = folder
    }

    // Get files with pagination
    const [files, total] = await Promise.all([
      prisma.file.findMany({
        where,
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit,
        include: {
          uploadedBy: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        }
      }),
      prisma.file.count({ where })
    ])

    return NextResponse.json({
      files,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })

  } catch (error) {
    console.error('Get files error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const fileId = searchParams.get('id')

    if (!fileId) {
      return NextResponse.json(
        { error: 'File ID is required' },
        { status: 400 }
      )
    }

    // Get file record
    const file = await prisma.file.findFirst({
      where: {
        id: fileId,
        uploadedById: session.user.id
      }
    })

    if (!file) {
      return NextResponse.json(
        { error: 'File not found' },
        { status: 404 }
      )
    }

    // Delete from Bunny Storage
    const bunnyStorage = getBunnyStorage()
    const filepath = file.url.replace(process.env.BUNNY_PULL_ZONE_URL + '/', '')
    const deleted = await bunnyStorage.deleteFile(filepath)

    if (deleted) {
      // Delete from database
      await prisma.file.delete({
        where: { id: fileId }
      })

      return NextResponse.json({
        success: true,
        message: 'File deleted successfully'
      })
    } else {
      return NextResponse.json(
        { error: 'Failed to delete file from storage' },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('Delete file error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
