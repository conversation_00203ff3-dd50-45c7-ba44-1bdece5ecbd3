import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { QuizCreationAgent } from '@/lib/ai-agents/quiz-creation-agent'
import { z } from 'zod'

const analyzeRequestSchema = z.object({
  content: z.string().optional(),
  files: z.array(z.object({
    name: z.string(),
    content: z.string(),
    type: z.string()
  })).optional(),
  analyzerModel: z.string().optional()
})

// POST /api/ai/quiz-creation/analyze - Analyze content before quiz creation
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    validateBody: analyzeRequestSchema
  },
  async (request: NextRequest, { validatedBody, user }) => {
    try {
      const { content, files, analyzerModel } = validatedBody

      if (!content && (!files || files.length === 0)) {
        return APIResponse.error('Either content or files must be provided', 400)
      }

      const agent = new QuizCreationAgent({
        contentAnalyzer: analyzerModel
      })

      // Create a public method for analysis
      const analysis = await agent.analyzeContentPublic({
        content,
        files,
        requirements: {}
      })

      return APIResponse.success({
        analysis,
        suggestions: {
          recommendedQuestionCount: analysis.suggestedQuestionCount,
          estimatedCreationTime: Math.ceil(analysis.suggestedQuestionCount * 0.5), // minutes
          complexityLevel: analysis.difficulty,
          keyFocusAreas: analysis.keyTopics.slice(0, 5)
        }
      }, 'Content analysis completed')

    } catch (error: any) {
      console.error('Content Analysis Error:', error)
      return APIResponse.error(
        error.message || 'Failed to analyze content',
        500
      )
    }
  }
)
