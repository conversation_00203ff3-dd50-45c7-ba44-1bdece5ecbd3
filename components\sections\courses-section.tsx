'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Calculator, 
  Stethoscope, 
  Building, 
  GraduationCap,
  Users,
  Clock,
  Star,
  ArrowRight,
  Filter
} from 'lucide-react';
import { useIntersectionObserver } from '@/hooks/use-intersection-observer';
import { AnimatedCard } from '@/components/ui/animated-card';
import { AnimatedButton } from '@/components/ui/animated-button';
import { cn } from '@/lib/utils';

interface Course {
  id: string;
  title: string;
  description: string;
  category: 'engineering' | 'medical' | 'government' | 'teaching' | 'all';
  price: string;
  originalPrice?: string;
  duration: string;
  students: string;
  rating: number;
  image: string;
  features: string[];
  exams: string[];
}

const courses: Course[] = [
  {
    id: '1',
    title: 'JEE Main & Advanced Complete Course',
    description: 'Comprehensive preparation for JEE with expert faculty and AI-powered practice',
    category: 'engineering',
    price: '₹99',
    originalPrice: '₹999',
    duration: '12 months',
    students: '50K+',
    rating: 4.9,
    image: '/api/placeholder/400/250',
    features: ['Live Classes', 'Mock Tests', 'Doubt Solving', 'Study Material'],
    exams: ['JEE Main', 'JEE Advanced']
  },
  {
    id: '2',
    title: 'NEET Complete Preparation',
    description: 'Master Biology, Chemistry, and Physics for NEET with proven strategies',
    category: 'medical',
    price: '₹99',
    originalPrice: '₹899',
    duration: '10 months',
    students: '75K+',
    rating: 4.8,
    image: '/api/placeholder/400/250',
    features: ['Expert Faculty', 'Previous Year Papers', 'Biology Focus', 'Chemistry Labs'],
    exams: ['NEET UG', 'AIIMS']
  },
  {
    id: '3',
    title: 'UPSC Civil Services Complete',
    description: 'Comprehensive UPSC preparation with current affairs and answer writing',
    category: 'government',
    price: '₹149',
    originalPrice: '₹1299',
    duration: '18 months',
    students: '30K+',
    rating: 4.9,
    image: '/api/placeholder/400/250',
    features: ['Current Affairs', 'Answer Writing', 'Interview Prep', 'Optional Subjects'],
    exams: ['UPSC CSE', 'State PSC']
  },
  {
    id: '4',
    title: 'SSC CGL & CHSL Preparation',
    description: 'Complete SSC preparation with quantitative aptitude and reasoning',
    category: 'government',
    price: '₹79',
    originalPrice: '₹699',
    duration: '8 months',
    students: '40K+',
    rating: 4.7,
    image: '/api/placeholder/400/250',
    features: ['Quantitative Aptitude', 'English', 'General Knowledge', 'Reasoning'],
    exams: ['SSC CGL', 'SSC CHSL', 'SSC MTS']
  },
  {
    id: '5',
    title: 'CTET & Teaching Exams',
    description: 'Comprehensive teacher eligibility test preparation with pedagogy focus',
    category: 'teaching',
    price: '₹69',
    originalPrice: '₹599',
    duration: '6 months',
    students: '25K+',
    rating: 4.6,
    image: '/api/placeholder/400/250',
    features: ['Child Development', 'Pedagogy', 'Subject Knowledge', 'Practice Tests'],
    exams: ['CTET', 'State TET', 'KVS', 'NVS']
  },
  {
    id: '6',
    title: 'Banking & Insurance Exams',
    description: 'Complete banking exam preparation with quantitative and reasoning skills',
    category: 'government',
    price: '₹89',
    originalPrice: '₹799',
    duration: '9 months',
    students: '35K+',
    rating: 4.8,
    image: '/api/placeholder/400/250',
    features: ['Banking Awareness', 'Quantitative Aptitude', 'English', 'Computer Knowledge'],
    exams: ['IBPS PO', 'SBI PO', 'RBI Grade B', 'LIC AAO']
  }
];

const categories = [
  { id: 'all', label: 'All Courses', icon: GraduationCap, color: 'violet' },
  { id: 'engineering', label: 'Engineering', icon: Calculator, color: 'blue' },
  { id: 'medical', label: 'Medical', icon: Stethoscope, color: 'pink' },
  { id: 'government', label: 'Government', icon: Building, color: 'green' },
  { id: 'teaching', label: 'Teaching', icon: Users, color: 'orange' },
];

export function CoursesSection() {
  const [activeCategory, setActiveCategory] = useState<string>('all');
  const { elementRef, isVisible } = useIntersectionObserver({
    threshold: 0.2,
    freezeOnceVisible: true,
  });

  const filteredCourses = activeCategory === 'all' 
    ? courses 
    : courses.filter(course => course.category === activeCategory);

  return (
    <section ref={elementRef} className="py-20 bg-gradient-to-b from-violet-50 to-white dark:from-gray-800 dark:to-gray-900 relative overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isVisible ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, ease: 'easeOut' }}
          className="text-center mb-16"
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={isVisible ? { opacity: 1, scale: 1 } : {}}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="inline-flex items-center gap-2 px-4 py-2 rounded-full glass text-sm font-medium text-violet-700 dark:text-violet-300 mb-6"
          >
            <Filter className="w-4 h-4" />
            Choose Your Path
          </motion.div>

          <h2 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 dark:text-white mb-6">
            Explore Our{' '}
            <span className="text-gradient bg-gradient-primary">Courses</span>
          </h2>
          
          <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
            Comprehensive courses designed by experts to help you crack any Indian competitive exam
          </p>
        </motion.div>

        {/* Category Filter */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={isVisible ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="flex flex-wrap justify-center gap-4 mb-12"
        >
          {categories.map((category, index) => (
            <motion.button
              key={category.id}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={isVisible ? { opacity: 1, scale: 1 } : {}}
              transition={{ duration: 0.4, delay: 0.4 + index * 0.1 }}
              onClick={() => setActiveCategory(category.id)}
              className={cn(
                'flex items-center gap-2 px-6 py-3 rounded-2xl font-medium transition-all duration-300',
                activeCategory === category.id
                  ? 'bg-gradient-primary text-white shadow-glow'
                  : 'glass hover:shadow-lg text-gray-700 dark:text-gray-300'
              )}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <category.icon className="w-5 h-5" />
              {category.label}
            </motion.button>
          ))}
        </motion.div>

        {/* Courses Grid */}
        <AnimatePresence mode="wait">
          <motion.div
            key={activeCategory}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.5 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            {filteredCourses.map((course, index) => (
              <motion.div
                key={course.id}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="group"
              >
                <AnimatedCard
                  variant="glass"
                  hover="lift"
                  className="h-full overflow-hidden p-0"
                >
                  {/* Course Image */}
                  <div className="relative h-48 bg-gradient-to-br from-violet-400 to-purple-600 overflow-hidden">
                    <div className="absolute inset-0 bg-black/20" />
                    <div className="absolute top-4 left-4 flex gap-2">
                      {course.exams.slice(0, 2).map((exam) => (
                        <span
                          key={exam}
                          className="px-2 py-1 text-xs font-medium bg-white/20 backdrop-blur-sm rounded-lg text-white"
                        >
                          {exam}
                        </span>
                      ))}
                    </div>
                    <div className="absolute top-4 right-4 flex items-center gap-1 px-2 py-1 bg-white/20 backdrop-blur-sm rounded-lg text-white text-sm">
                      <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                      {course.rating}
                    </div>
                  </div>

                  {/* Course Content */}
                  <div className="p-6">
                    <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2 group-hover:text-violet-600 transition-colors">
                      {course.title}
                    </h3>
                    
                    <p className="text-gray-600 dark:text-gray-400 mb-4 line-clamp-2">
                      {course.description}
                    </p>

                    {/* Features */}
                    <div className="flex flex-wrap gap-2 mb-4">
                      {course.features.slice(0, 3).map((feature) => (
                        <span
                          key={feature}
                          className="px-2 py-1 text-xs bg-violet-100 dark:bg-violet-900 text-violet-700 dark:text-violet-300 rounded-lg"
                        >
                          {feature}
                        </span>
                      ))}
                    </div>

                    {/* Course Stats */}
                    <div className="flex items-center gap-4 mb-4 text-sm text-gray-600 dark:text-gray-400">
                      <div className="flex items-center gap-1">
                        <Clock className="w-4 h-4" />
                        {course.duration}
                      </div>
                      <div className="flex items-center gap-1">
                        <Users className="w-4 h-4" />
                        {course.students}
                      </div>
                    </div>

                    {/* Price and CTA */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span className="text-2xl font-bold text-gray-900 dark:text-white">
                          {course.price}
                        </span>
                        {course.originalPrice && (
                          <span className="text-sm text-gray-500 line-through">
                            {course.originalPrice}
                          </span>
                        )}
                      </div>
                      
                      <AnimatedButton
                        variant="primary"
                        size="sm"
                        className="group"
                      >
                        Enroll Now
                        <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
                      </AnimatedButton>
                    </div>
                  </div>
                </AnimatedCard>
              </motion.div>
            ))}
          </motion.div>
        </AnimatePresence>

        {/* View All Button */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={isVisible ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="text-center mt-12"
        >
          <AnimatedButton
            variant="outline"
            size="lg"
            className="group"
          >
            View All Courses
            <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
          </AnimatedButton>
        </motion.div>
      </div>
    </section>
  );
}
