"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { 
  Search, 
  Filter, 
  Calendar, 
  Clock, 
  Trophy,
  Target,
  TrendingUp,
  TrendingDown,
  Eye,
  RotateCcw,
  Download,
  Star,
  BookOpen,
  Award,
  CheckCircle,
  XCircle,
  AlertCircle,
  BarChart3
} from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { motion, AnimatePresence } from "framer-motion"
import { toast } from "@/lib/toast-utils"
import Link from "next/link"

interface QuizAttempt {
  id: string
  quiz: {
    id: string
    title: string
    description: string
    type: string
    difficulty: string
    category: string
    tags: string[]
    thumbnail?: string
    timeLimit: number
    questionCount: number
  }
  score: number
  percentage: number
  timeSpent: number | null
  status: string
  startedAt: string
  completedAt: string | null
  isCompleted: boolean
  correctAnswers: number
  totalQuestions: number
  passed: boolean
  rank?: number
}

interface PerformanceStats {
  totalAttempts: number
  completedQuizzes: number
  averageScore: number
  totalTimeSpent: number
  bestScore: number
  improvementRate: number
  currentStreak: number
  longestStreak: number
  rank: number
  totalStudents: number
}

export default function StudentHistory() {
  const [attempts, setAttempts] = useState<QuizAttempt[]>([])
  const [stats, setStats] = useState<PerformanceStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [selectedDifficulty, setSelectedDifficulty] = useState("all")
  const [selectedType, setSelectedType] = useState("all")
  const [sortBy, setSortBy] = useState("recent")
  const [showFilters, setShowFilters] = useState(false)

  // Categories will be fetched dynamically from the API response
  const [categories, setCategories] = useState<string[]>([])

  useEffect(() => {
    fetchStudentHistory()
  }, [selectedType, selectedDifficulty, sortBy])

  const fetchStudentHistory = async () => {
    setLoading(true)
    try {
      // Build query parameters
      const params = new URLSearchParams({
        page: '1',
        limit: '20',
        sortBy: sortBy === 'recent' ? 'date' : sortBy === 'score' ? 'score' : 'title',
        sortOrder: 'desc'
      })

      if (selectedType !== 'all') {
        params.append('type', selectedType)
      }
      if (selectedDifficulty !== 'all') {
        params.append('difficulty', selectedDifficulty)
      }

      const response = await fetch(`/api/student/history?${params}`)

      if (!response.ok) {
        throw new Error('Failed to fetch history')
      }

      const data = await response.json()

      if (data.success) {
        setAttempts(data.data.attempts)
        setCategories(data.data.categories || [])
        setStats({
          totalAttempts: data.data.summary.totalAttempts,
          completedQuizzes: data.data.summary.completedAttempts,
          averageScore: data.data.summary.averageScore,
          totalTimeSpent: data.data.summary.totalTimeSpent,
          bestScore: data.data.summary.bestScore,
          improvementRate: 0, // Not implemented yet
          currentStreak: 0, // Not implemented yet
          longestStreak: 0, // Not implemented yet
          rank: data.data.summary.rank,
          totalStudents: data.data.summary.totalStudents
        })
      } else {
        throw new Error(data.message || 'Failed to fetch history')
      }

    } catch (error) {
      console.error('Error fetching student history:', error)
      toast.error('Failed to load history')
    } finally {
      setLoading(false)
    }
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'EASY':
        return 'text-green-600 bg-green-50 border-green-200 dark:text-green-400 dark:bg-green-900/20 dark:border-green-800'
      case 'MEDIUM':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200 dark:text-yellow-400 dark:bg-yellow-900/20 dark:border-yellow-800'
      case 'HARD':
        return 'text-red-600 bg-red-50 border-red-200 dark:text-red-400 dark:bg-red-900/20 dark:border-red-800'
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200 dark:text-gray-400 dark:bg-gray-900/20 dark:border-gray-800'
    }
  }

  const getScoreColor = (percentage: number) => {
    if (percentage >= 90) return 'text-green-600'
    if (percentage >= 70) return 'text-blue-600'
    if (percentage >= 50) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getPerformanceIcon = (percentage: number) => {
    if (percentage >= 90) return <Trophy className="h-4 w-4 text-green-600" />
    if (percentage >= 70) return <Target className="h-4 w-4 text-blue-600" />
    if (percentage >= 50) return <AlertCircle className="h-4 w-4 text-yellow-600" />
    return <XCircle className="h-4 w-4 text-red-600" />
  }

  const filteredAttempts = attempts.filter(attempt => {
    const matchesSearch = attempt.quiz.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         attempt.quiz.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
    const matchesCategory = selectedCategory === 'all' || attempt.quiz.tags.includes(selectedCategory)
    const matchesDifficulty = selectedDifficulty === 'all' || attempt.quiz.difficulty.toLowerCase() === selectedDifficulty
    const matchesType = selectedType === 'all' || attempt.quiz.type.toLowerCase() === selectedType

    return matchesSearch && matchesCategory && matchesDifficulty && matchesType
  })

  const sortedAttempts = [...filteredAttempts].sort((a, b) => {
    switch (sortBy) {
      case 'recent':
        return new Date(b.startedAt).getTime() - new Date(a.startedAt).getTime()
      case 'score':
        return b.percentage - a.percentage
      case 'title':
        return a.quiz.title.localeCompare(b.quiz.title)
      case 'time':
        return (b.timeSpent || 0) - (a.timeSpent || 0)
      default:
        return 0
    }
  })

  const exportHistory = () => {
    const csvContent = [
      ['Quiz Title', 'Category', 'Difficulty', 'Score', 'Percentage', 'Time Spent', 'Completed At', 'Passed'].join(','),
      ...sortedAttempts.map(attempt => [
        attempt.quiz.title,
        attempt.quiz.category,
        attempt.quiz.difficulty,
        attempt.score,
        attempt.percentage,
        attempt.timeSpent,
        attempt.completedAt ? new Date(attempt.completedAt).toLocaleDateString() : 'N/A',
        attempt.passed ? 'Yes' : 'No'
      ].join(','))
    ].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'quiz-history.csv'
    a.click()
    URL.revokeObjectURL(url)
    
    toast.success('History exported successfully!')
  }

  return (
    <div className="p-6 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Quiz History</h1>
          <p className="text-muted-foreground mt-1">
            Track your learning progress and performance over time
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={exportHistory}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
          >
            <Filter className="h-4 w-4 mr-2" />
            {showFilters ? 'Hide Filters' : 'Show Filters'}
          </Button>
        </div>
      </div>

      {/* Performance Overview */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Card className="hover:shadow-lg transition-shadow">
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-3xl font-bold">{stats.totalAttempts}</div>
                    <p className="text-sm text-muted-foreground">Total Attempts</p>
                  </div>
                  <BookOpen className="h-8 w-8 text-blue-600" />
                </div>
                <div className="mt-4 flex items-center gap-2">
                  <TrendingUp className="h-4 w-4 text-green-600" />
                  <p className="text-xs text-green-600">
                    +{stats.improvementRate}% improvement
                  </p>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            <Card className="hover:shadow-lg transition-shadow">
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-3xl font-bold">{stats.averageScore}%</div>
                    <p className="text-sm text-muted-foreground">Average Score</p>
                  </div>
                  <Target className="h-8 w-8 text-green-600" />
                </div>
                <div className="mt-4">
                  <Progress value={stats.averageScore} className="h-2" />
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <Card className="hover:shadow-lg transition-shadow">
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-3xl font-bold">#{stats.rank}</div>
                    <p className="text-sm text-muted-foreground">Global Rank</p>
                  </div>
                  <Trophy className="h-8 w-8 text-yellow-600" />
                </div>
                <div className="mt-4">
                  <p className="text-xs text-muted-foreground">
                    Out of {stats.totalStudents.toLocaleString()} students
                  </p>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <Card className="hover:shadow-lg transition-shadow">
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-3xl font-bold">{Math.round(stats.totalTimeSpent / 60)}h</div>
                    <p className="text-sm text-muted-foreground">Time Spent</p>
                  </div>
                  <Clock className="h-8 w-8 text-purple-600" />
                </div>
                <div className="mt-4">
                  <p className="text-xs text-muted-foreground">
                    {stats.totalTimeSpent} minutes total
                  </p>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      )}

      {/* Search and Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="space-y-4">
            {/* Search Bar */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder="Search quiz history..."
                className="pl-10"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>

            {/* Advanced Filters */}
            <AnimatePresence>
              {showFilters && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="grid grid-cols-1 md:grid-cols-4 gap-4 pt-4 border-t"
                >
                  <div>
                    <label className="text-sm font-medium mb-2 block">Category</label>
                    <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Categories</SelectItem>
                        {categories.map(category => (
                          <SelectItem key={category} value={category}>
                            {category}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium mb-2 block">Difficulty</label>
                    <Select value={selectedDifficulty} onValueChange={setSelectedDifficulty}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Levels</SelectItem>
                        <SelectItem value="easy">Easy</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="hard">Hard</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium mb-2 block">Type</label>
                    <Select value={selectedType} onValueChange={setSelectedType}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Types</SelectItem>
                        <SelectItem value="quiz">Quiz</SelectItem>
                        <SelectItem value="test_series">Test Series</SelectItem>
                        <SelectItem value="daily_practice">Daily Practice</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium mb-2 block">Sort By</label>
                    <Select value={sortBy} onValueChange={setSortBy}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="recent">Most Recent</SelectItem>
                        <SelectItem value="score">Highest Score</SelectItem>
                        <SelectItem value="title">Quiz Title</SelectItem>
                        <SelectItem value="time">Time Spent</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </CardContent>
      </Card>

      {/* Results Summary */}
      <div className="flex items-center justify-between">
        <p className="text-sm text-muted-foreground">
          Showing {sortedAttempts.length} of {attempts.length} attempts
        </p>
      </div>

      {/* Quiz History List */}
      {loading ? (
        <div className="space-y-4">
          {[...Array(5)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="pt-6">
                <div className="space-y-3">
                  <div className="h-4 bg-muted rounded w-3/4"></div>
                  <div className="h-3 bg-muted rounded w-1/2"></div>
                  <div className="h-3 bg-muted rounded w-full"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : sortedAttempts.length === 0 ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-12">
              <BookOpen className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">No quiz history found</h3>
              <p className="text-muted-foreground mb-6">
                {searchQuery || selectedCategory !== 'all' || selectedDifficulty !== 'all' || selectedType !== 'all'
                  ? 'Try adjusting your search criteria or filters.'
                  : 'Start taking quizzes to build your learning history.'}
              </p>
              <div className="flex items-center gap-2 justify-center">
                {(searchQuery || selectedCategory !== 'all' || selectedDifficulty !== 'all' || selectedType !== 'all') && (
                  <Button variant="outline" onClick={() => {
                    setSearchQuery("")
                    setSelectedCategory("all")
                    setSelectedDifficulty("all")
                    setSelectedType("all")
                  }}>
                    Clear Filters
                  </Button>
                )}
                <Button asChild>
                  <Link href="/student/browse">
                    Browse Quizzes
                  </Link>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {sortedAttempts.map((attempt, index) => (
            <motion.div
              key={attempt.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.1 * index }}
            >
              <Card className="hover:shadow-lg transition-shadow group">
                <CardContent className="pt-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        {getPerformanceIcon(attempt.percentage)}
                        <h3 className="text-lg font-semibold group-hover:text-primary transition-colors">
                          {attempt.quiz.title}
                        </h3>
                        {attempt.passed ? (
                          <Badge className="bg-green-500">
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Passed
                          </Badge>
                        ) : (
                          <Badge variant="destructive">
                            <XCircle className="h-3 w-3 mr-1" />
                            Failed
                          </Badge>
                        )}
                      </div>
                      
                      <div className="flex items-center gap-4 mb-3 text-sm text-muted-foreground">
                        <span>{attempt.quiz.category}</span>
                        <Badge variant="outline" className={getDifficultyColor(attempt.quiz.difficulty)}>
                          {attempt.quiz.difficulty}
                        </Badge>
                        <Badge variant="outline">
                          {attempt.quiz.type.replace('_', ' ')}
                        </Badge>
                        <span>{attempt.completedAt ? new Date(attempt.completedAt).toLocaleDateString() : 'In Progress'}</span>
                      </div>

                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <div className={`text-2xl font-bold ${getScoreColor(attempt.percentage)}`}>
                            {attempt.percentage}%
                          </div>
                          <div className="text-muted-foreground">Score</div>
                        </div>
                        <div>
                          <div className="text-2xl font-bold">{attempt.timeSpent}m</div>
                          <div className="text-muted-foreground">Time</div>
                        </div>
                        <div>
                          <div className="text-2xl font-bold text-green-600">{attempt.correctAnswers}</div>
                          <div className="text-muted-foreground">Correct</div>
                        </div>
                        {attempt.rank && (
                          <div>
                            <div className="text-2xl font-bold text-yellow-600">#{attempt.rank}</div>
                            <div className="text-muted-foreground">Rank</div>
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center gap-2 ml-4">
                      <Button variant="outline" size="sm" asChild>
                        <Link href={`/student/quiz/${attempt.quiz.id}/result/${attempt.id}`}>
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </Link>
                      </Button>
                      <Button variant="outline" size="sm" asChild>
                        <Link href={`/student/quiz/${attempt.quiz.id}`}>
                          <RotateCcw className="h-4 w-4 mr-1" />
                          Retake
                        </Link>
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      )}
    </div>
  )
}
