/**
 * Image optimization utilities for client-side processing
 */

interface ImageOptimizationOptions {
  maxWidth?: number
  maxHeight?: number
  quality?: number
  format?: 'jpeg' | 'png' | 'webp'
}

interface OptimizedImage {
  file: File
  originalSize: number
  optimizedSize: number
  compressionRatio: number
}

/**
 * Optimize an image file by resizing and compressing
 */
export async function optimizeImage(
  file: File,
  options: ImageOptimizationOptions = {}
): Promise<OptimizedImage> {
  const {
    maxWidth = 1920,
    maxHeight = 1080,
    quality = 0.8,
    format = 'jpeg'
  } = options

  return new Promise((resolve, reject) => {
    const img = new Image()
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')

    if (!ctx) {
      reject(new Error('Canvas context not available'))
      return
    }

    img.onload = () => {
      // Calculate new dimensions
      let { width, height } = img
      
      if (width > maxWidth || height > maxHeight) {
        const aspectRatio = width / height
        
        if (width > height) {
          width = Math.min(width, maxWidth)
          height = width / aspectRatio
        } else {
          height = Math.min(height, maxHeight)
          width = height * aspectRatio
        }
      }

      // Set canvas dimensions
      canvas.width = width
      canvas.height = height

      // Draw and compress image
      ctx.drawImage(img, 0, 0, width, height)

      canvas.toBlob(
        (blob) => {
          if (!blob) {
            reject(new Error('Failed to optimize image'))
            return
          }

          const optimizedFile = new File([blob], file.name, {
            type: `image/${format}`,
            lastModified: Date.now()
          })

          const compressionRatio = ((file.size - optimizedFile.size) / file.size) * 100

          resolve({
            file: optimizedFile,
            originalSize: file.size,
            optimizedSize: optimizedFile.size,
            compressionRatio: Math.max(0, compressionRatio)
          })
        },
        `image/${format}`,
        quality
      )
    }

    img.onerror = () => {
      reject(new Error('Failed to load image'))
    }

    img.src = URL.createObjectURL(file)
  })
}

/**
 * Generate thumbnail from image file
 */
export async function generateThumbnail(
  file: File,
  size: number = 200
): Promise<File> {
  return new Promise((resolve, reject) => {
    const img = new Image()
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')

    if (!ctx) {
      reject(new Error('Canvas context not available'))
      return
    }

    img.onload = () => {
      // Calculate square thumbnail dimensions
      const { width, height } = img
      const minDimension = Math.min(width, height)
      
      // Calculate crop area (center crop)
      const cropX = (width - minDimension) / 2
      const cropY = (height - minDimension) / 2

      // Set canvas to thumbnail size
      canvas.width = size
      canvas.height = size

      // Draw cropped and resized image
      ctx.drawImage(
        img,
        cropX, cropY, minDimension, minDimension,
        0, 0, size, size
      )

      canvas.toBlob(
        (blob) => {
          if (!blob) {
            reject(new Error('Failed to generate thumbnail'))
            return
          }

          const thumbnailFile = new File([blob], `thumb_${file.name}`, {
            type: 'image/jpeg',
            lastModified: Date.now()
          })

          resolve(thumbnailFile)
        },
        'image/jpeg',
        0.8
      )
    }

    img.onerror = () => {
      reject(new Error('Failed to load image for thumbnail'))
    }

    img.src = URL.createObjectURL(file)
  })
}

/**
 * Get image dimensions without loading the full image
 */
export function getImageDimensions(file: File): Promise<{ width: number; height: number }> {
  return new Promise((resolve, reject) => {
    const img = new Image()
    
    img.onload = () => {
      resolve({
        width: img.naturalWidth,
        height: img.naturalHeight
      })
      URL.revokeObjectURL(img.src)
    }

    img.onerror = () => {
      reject(new Error('Failed to load image'))
      URL.revokeObjectURL(img.src)
    }

    img.src = URL.createObjectURL(file)
  })
}

/**
 * Convert image to different format
 */
export async function convertImageFormat(
  file: File,
  targetFormat: 'jpeg' | 'png' | 'webp',
  quality: number = 0.8
): Promise<File> {
  return new Promise((resolve, reject) => {
    const img = new Image()
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')

    if (!ctx) {
      reject(new Error('Canvas context not available'))
      return
    }

    img.onload = () => {
      canvas.width = img.width
      canvas.height = img.height

      // Handle transparency for JPEG
      if (targetFormat === 'jpeg') {
        ctx.fillStyle = '#FFFFFF'
        ctx.fillRect(0, 0, canvas.width, canvas.height)
      }

      ctx.drawImage(img, 0, 0)

      canvas.toBlob(
        (blob) => {
          if (!blob) {
            reject(new Error('Failed to convert image'))
            return
          }

          const convertedFile = new File([blob], file.name.replace(/\.[^/.]+$/, `.${targetFormat}`), {
            type: `image/${targetFormat}`,
            lastModified: Date.now()
          })

          resolve(convertedFile)
        },
        `image/${targetFormat}`,
        quality
      )
    }

    img.onerror = () => {
      reject(new Error('Failed to load image for conversion'))
    }

    img.src = URL.createObjectURL(file)
  })
}

/**
 * Validate image file
 */
export function validateImageFile(file: File): {
  isValid: boolean
  errors: string[]
} {
  const errors: string[] = []
  
  // Check if it's an image
  if (!file.type.startsWith('image/')) {
    errors.push('File is not an image')
  }

  // Check supported formats
  const supportedFormats = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml']
  if (!supportedFormats.includes(file.type)) {
    errors.push(`Unsupported image format: ${file.type}`)
  }

  // Check file size (max 10MB)
  const maxSize = 10 * 1024 * 1024
  if (file.size > maxSize) {
    errors.push(`File size exceeds ${maxSize / (1024 * 1024)}MB limit`)
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * Create image preview URL
 */
export function createImagePreview(file: File): string {
  return URL.createObjectURL(file)
}

/**
 * Cleanup image preview URL
 */
export function cleanupImagePreview(url: string): void {
  URL.revokeObjectURL(url)
}

/**
 * Batch optimize multiple images
 */
export async function batchOptimizeImages(
  files: File[],
  options: ImageOptimizationOptions = {},
  onProgress?: (completed: number, total: number) => void
): Promise<OptimizedImage[]> {
  const results: OptimizedImage[] = []
  
  for (let i = 0; i < files.length; i++) {
    try {
      const optimized = await optimizeImage(files[i], options)
      results.push(optimized)
      
      if (onProgress) {
        onProgress(i + 1, files.length)
      }
    } catch (error) {
      console.error(`Failed to optimize image ${files[i].name}:`, error)
      // Add original file as fallback
      results.push({
        file: files[i],
        originalSize: files[i].size,
        optimizedSize: files[i].size,
        compressionRatio: 0
      })
    }
  }
  
  return results
}

/**
 * Format file size for display
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * Calculate compression savings
 */
export function calculateCompressionSavings(originalSize: number, optimizedSize: number): {
  savedBytes: number
  savedPercentage: number
  ratio: string
} {
  const savedBytes = originalSize - optimizedSize
  const savedPercentage = (savedBytes / originalSize) * 100
  const ratio = `${(originalSize / optimizedSize).toFixed(1)}:1`
  
  return {
    savedBytes,
    savedPercentage,
    ratio
  }
}
