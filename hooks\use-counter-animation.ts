'use client';

import { useEffect, useState } from 'react';

interface UseCounterAnimationProps {
  end: number;
  start?: number;
  duration?: number;
  isVisible?: boolean;
  decimals?: number;
  suffix?: string;
  prefix?: string;
}

export function useCounterAnimation({
  end,
  start = 0,
  duration = 2000,
  isVisible = true,
  decimals = 0,
  suffix = '',
  prefix = '',
}: UseCounterAnimationProps) {
  const [count, setCount] = useState(start);
  const [hasAnimated, setHasAnimated] = useState(false);

  useEffect(() => {
    if (!isVisible || hasAnimated) return;

    setHasAnimated(true);
    let startTime: number;
    const startValue = start;
    const endValue = end;
    const difference = endValue - startValue;

    const animate = (currentTime: number) => {
      if (!startTime) startTime = currentTime;
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // Easing function (ease-out)
      const easeOut = 1 - Math.pow(1 - progress, 3);
      const currentCount = startValue + difference * easeOut;

      setCount(currentCount);

      if (progress < 1) {
        requestAnimationFrame(animate);
      } else {
        setCount(endValue);
      }
    };

    requestAnimationFrame(animate);
  }, [isVisible, end, start, duration, hasAnimated]);

  const formatNumber = (num: number) => {
    const formatted = num.toFixed(decimals);
    return `${prefix}${formatted}${suffix}`;
  };

  return formatNumber(count);
}

// Hook for multiple counters with staggered animations
export function useStaggeredCounters(
  counters: Array<{
    end: number;
    start?: number;
    duration?: number;
    decimals?: number;
    suffix?: string;
    prefix?: string;
  }>,
  isVisible = true,
  staggerDelay = 200
) {
  const [animatedCounters, setAnimatedCounters] = useState<string[]>(
    counters.map((counter) => 
      `${counter.prefix || ''}${(counter.start || 0).toFixed(counter.decimals || 0)}${counter.suffix || ''}`
    )
  );

  useEffect(() => {
    if (!isVisible) return;

    counters.forEach((counter, index) => {
      const delay = index * staggerDelay;
      
      setTimeout(() => {
        let startTime: number;
        const startValue = counter.start || 0;
        const endValue = counter.end;
        const difference = endValue - startValue;
        const duration = counter.duration || 2000;

        const animate = (currentTime: number) => {
          if (!startTime) startTime = currentTime;
          const elapsed = currentTime - startTime;
          const progress = Math.min(elapsed / duration, 1);

          // Easing function (ease-out)
          const easeOut = 1 - Math.pow(1 - progress, 3);
          const currentCount = startValue + difference * easeOut;

          const formatted = `${counter.prefix || ''}${currentCount.toFixed(counter.decimals || 0)}${counter.suffix || ''}`;
          
          setAnimatedCounters((prev) => {
            const newCounters = [...prev];
            newCounters[index] = formatted;
            return newCounters;
          });

          if (progress < 1) {
            requestAnimationFrame(animate);
          } else {
            const finalFormatted = `${counter.prefix || ''}${endValue.toFixed(counter.decimals || 0)}${counter.suffix || ''}`;
            setAnimatedCounters((prev) => {
              const newCounters = [...prev];
              newCounters[index] = finalFormatted;
              return newCounters;
            });
          }
        };

        requestAnimationFrame(animate);
      }, delay);
    });
  }, [isVisible, staggerDelay]);

  return animatedCounters;
}
