import { prisma } from '../lib/prisma'

async function seedPracticeSessions() {
  console.log('Seeding practice sessions...')

  // Get the first admin user to be the creator
  const adminUser = await prisma.user.findFirst({
    where: { role: 'ADMIN' }
  })

  if (!adminUser) {
    console.error('No admin user found. Please create an admin user first.')
    return
  }

  const practiceSessions = [
    {
      title: "JavaScript Basics",
      description: "Quick practice with variables, functions, and basic syntax",
      category: "programming",
      difficulty: "EASY" as const,
      estimatedTime: 15,
      points: 50,
      questions: [
        {
          text: "What is the correct way to declare a variable in JavaScript?",
          options: ["var x = 5;", "variable x = 5;", "v x = 5;", "declare x = 5;"],
          correctAnswer: "var x = 5;",
          explanation: "In JavaScript, variables can be declared using var, let, or const keywords.",
          points: 5
        },
        {
          text: "Which method is used to add an element to the end of an array?",
          options: ["push()", "add()", "append()", "insert()"],
          correctAnswer: "push()",
          explanation: "The push() method adds one or more elements to the end of an array.",
          points: 5
        }
      ]
    },
    {
      title: "React Hooks Challenge",
      description: "Test your knowledge of useState, useEffect, and custom hooks",
      category: "web-dev",
      difficulty: "MEDIUM" as const,
      estimatedTime: 12,
      points: 80,
      questions: [
        {
          text: "What does the useState hook return?",
          options: ["A state value", "A setter function", "An array with state value and setter", "An object with state and setState"],
          correctAnswer: "An array with state value and setter",
          explanation: "useState returns an array with two elements: the current state value and a function to update it.",
          points: 10
        },
        {
          text: "When does useEffect run by default?",
          options: ["Only on mount", "Only on unmount", "After every render", "Only when dependencies change"],
          correctAnswer: "After every render",
          explanation: "By default, useEffect runs after every completed render, both after the first render and after every update.",
          points: 10
        }
      ]
    },
    {
      title: "Algorithm Complexity",
      description: "Big O notation and time complexity analysis",
      category: "algorithms",
      difficulty: "HARD" as const,
      estimatedTime: 20,
      points: 120,
      questions: [
        {
          text: "What is the time complexity of binary search?",
          options: ["O(1)", "O(log n)", "O(n)", "O(n log n)"],
          correctAnswer: "O(log n)",
          explanation: "Binary search has O(log n) time complexity because it eliminates half of the search space in each iteration.",
          points: 15
        },
        {
          text: "What is the space complexity of merge sort?",
          options: ["O(1)", "O(log n)", "O(n)", "O(n²)"],
          correctAnswer: "O(n)",
          explanation: "Merge sort requires O(n) additional space for the temporary arrays used during the merge process.",
          points: 15
        }
      ]
    },
    {
      title: "SQL Queries",
      description: "Practice with SELECT, JOIN, and aggregate functions",
      category: "databases",
      difficulty: "MEDIUM" as const,
      estimatedTime: 18,
      points: 90,
      questions: [
        {
          text: "Which SQL clause is used to filter rows?",
          options: ["SELECT", "WHERE", "GROUP BY", "ORDER BY"],
          correctAnswer: "WHERE",
          explanation: "The WHERE clause is used to filter records and extract only those that fulfill a specified condition.",
          points: 10
        },
        {
          text: "What does INNER JOIN return?",
          options: ["All rows from left table", "All rows from right table", "Only matching rows from both tables", "All rows from both tables"],
          correctAnswer: "Only matching rows from both tables",
          explanation: "INNER JOIN returns only the rows that have matching values in both tables.",
          points: 10
        }
      ]
    },
    {
      title: "CSS Flexbox Fundamentals",
      description: "Master CSS Flexbox layout properties and alignment",
      category: "web-dev",
      difficulty: "EASY" as const,
      estimatedTime: 10,
      points: 60,
      questions: [
        {
          text: "Which property makes an element a flex container?",
          options: ["display: flex;", "flex: 1;", "flex-direction: row;", "justify-content: center;"],
          correctAnswer: "display: flex;",
          explanation: "The display: flex property makes an element a flex container, enabling flexbox layout for its children.",
          points: 8
        },
        {
          text: "What does justify-content control?",
          options: ["Vertical alignment", "Horizontal alignment", "Item order", "Item size"],
          correctAnswer: "Horizontal alignment",
          explanation: "justify-content controls the alignment of flex items along the main axis (horizontal by default).",
          points: 8
        }
      ]
    },
    {
      title: "Python Data Structures",
      description: "Lists, dictionaries, sets, and tuples in Python",
      category: "programming",
      difficulty: "MEDIUM" as const,
      estimatedTime: 16,
      points: 75,
      questions: [
        {
          text: "Which data structure is ordered and mutable in Python?",
          options: ["tuple", "set", "list", "frozenset"],
          correctAnswer: "list",
          explanation: "Lists in Python are ordered collections that are mutable (can be changed after creation).",
          points: 10
        },
        {
          text: "How do you create an empty dictionary in Python?",
          options: ["{}", "[]", "()", "set()"],
          correctAnswer: "{}",
          explanation: "An empty dictionary is created using curly braces {} or the dict() constructor.",
          points: 10
        }
      ]
    }
  ]

  for (const sessionData of practiceSessions) {
    try {
      // Create the quiz
      const quiz = await prisma.quiz.create({
        data: {
          title: sessionData.title,
          description: sessionData.description,
          type: 'DAILY_PRACTICE',
          difficulty: sessionData.difficulty,
          category: sessionData.category,
          estimatedTime: sessionData.estimatedTime,
          points: sessionData.points,
          timeLimit: sessionData.estimatedTime,
          isPublished: true,
          createdBy: adminUser.id
        }
      })

      // Create questions for the quiz
      for (let i = 0; i < sessionData.questions.length; i++) {
        const questionData = sessionData.questions[i]
        await prisma.question.create({
          data: {
            quizId: quiz.id,
            type: 'MCQ',
            text: questionData.text,
            options: questionData.options,
            correctAnswer: questionData.correctAnswer,
            explanation: questionData.explanation,
            points: questionData.points,
            order: i + 1
          }
        })
      }

      console.log(`Created practice session: ${sessionData.title}`)
    } catch (error) {
      console.error(`Error creating practice session ${sessionData.title}:`, error)
    }
  }

  console.log('Practice sessions seeding completed!')
}

// Also create some scheduled quizzes for the schedule page
async function seedScheduledQuizzes() {
  console.log('Seeding scheduled quizzes...')

  const adminUser = await prisma.user.findFirst({
    where: { role: 'ADMIN' }
  })

  if (!adminUser) {
    console.error('No admin user found.')
    return
  }

  const now = new Date()
  const scheduledQuizzes = [
    {
      title: "JavaScript Fundamentals Test",
      description: "Comprehensive test covering JavaScript basics, functions, and DOM manipulation",
      type: "TEST_SERIES" as const,
      difficulty: "MEDIUM" as const,
      startTime: new Date(now.getTime() + 2 * 24 * 60 * 60 * 1000), // 2 days from now
      endTime: new Date(now.getTime() + 2 * 24 * 60 * 60 * 1000 + 2 * 60 * 60 * 1000), // 2 hours duration
      timeLimit: 90,
      maxAttempts: 1
    },
    {
      title: "React Components Quiz",
      description: "Test your knowledge of React components, props, and state management",
      type: "QUIZ" as const,
      difficulty: "HARD" as const,
      startTime: new Date(now.getTime() + 60 * 60 * 1000), // 1 hour from now
      endTime: new Date(now.getTime() + 3 * 60 * 60 * 1000), // 2 hours duration
      timeLimit: 45,
      maxAttempts: 2
    }
  ]

  for (const quizData of scheduledQuizzes) {
    try {
      const quiz = await prisma.quiz.create({
        data: {
          title: quizData.title,
          description: quizData.description,
          type: quizData.type,
          difficulty: quizData.difficulty,
          startTime: quizData.startTime,
          endTime: quizData.endTime,
          timeLimit: quizData.timeLimit,
          maxAttempts: quizData.maxAttempts,
          isPublished: true,
          createdBy: adminUser.id
        }
      })

      // Create sample questions
      await prisma.question.create({
        data: {
          quizId: quiz.id,
          type: 'MCQ',
          text: 'Sample question for ' + quiz.title,
          options: ['Option A', 'Option B', 'Option C', 'Option D'],
          correctAnswer: 'Option A',
          explanation: 'This is a sample question.',
          points: 10,
          order: 1
        }
      })

      console.log(`Created scheduled quiz: ${quizData.title}`)
    } catch (error) {
      console.error(`Error creating scheduled quiz ${quizData.title}:`, error)
    }
  }

  console.log('Scheduled quizzes seeding completed!')
}

async function main() {
  await seedPracticeSessions()
  await seedScheduledQuizzes()
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
