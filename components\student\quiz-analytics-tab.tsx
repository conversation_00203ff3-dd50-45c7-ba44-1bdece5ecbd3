"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { 
  Bar<PERSON>hart3, 
  <PERSON><PERSON>dingUp, 
  Users, 
  Clock,
  Target,
  Award,
  Activity,
  CheckCircle
} from "lucide-react"

interface QuizAnalytics {
  quiz: {
    id: string
    title: string
    questionCount: number
  }
  overview: {
    totalAttempts: number
    uniqueUsers: number
    averageScore: number
    averageTime: number
    completionRate: number
    passRate: number
  }
  scoreDistribution: Array<{
    range: string
    count: number
  }>
  topPerformers: Array<{
    user: {
      name: string
      email: string
    }
    score: number
    timeSpent: number
    completedAt: string
  }>
  insights: Array<{
    type: string
    title: string
    value: string
    trend: 'positive' | 'neutral' | 'negative'
    description: string
  }>
}

interface QuizAnalyticsTabProps {
  quizId: string
}

export function QuizAnalyticsTab({ quizId }: QuizAnalyticsTabProps) {
  const [analytics, setAnalytics] = useState<QuizAnalytics | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchAnalytics()
  }, [quizId])

  const fetchAnalytics = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const response = await fetch(`/api/student/quizzes/${quizId}/analytics`)
      
      if (!response.ok) {
        throw new Error('Failed to fetch analytics')
      }
      
      const response_data = await response.json()
      const data = response_data.data || response_data // Handle both wrapped and unwrapped responses

      // Transform the student analytics data to match the expected interface
      const transformedData: QuizAnalytics = {
        quiz: {
          id: data.quiz.id,
          title: data.quiz.title,
          questionCount: data.quiz.questionCount
        },
        overview: {
          totalAttempts: data.overview.totalAttempts,
          uniqueUsers: data.overview.uniqueUsers,
          averageScore: data.overview.averageScore,
          averageTime: data.overview.averageTime,
          completionRate: data.overview.completionRate,
          passRate: data.overview.completionRate // Use completion rate as pass rate for students
        },
        scoreDistribution: data.scoreDistribution || [],
        topPerformers: data.topPerformers || [],
        insights: data.insights || []
      }

      setAnalytics(transformedData)
    } catch (error) {
      console.error('Error fetching quiz analytics:', error)
      setError('Failed to load analytics data')
    } finally {
      setLoading(false)
    }
  }

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case 'positive':
        return 'text-green-600'
      case 'negative':
        return 'text-red-600'
      default:
        return 'text-yellow-600'
    }
  }

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'positive':
        return <TrendingUp className="h-4 w-4 text-green-600" />
      case 'negative':
        return <TrendingUp className="h-4 w-4 text-red-600 rotate-180" />
      default:
        return <Activity className="h-4 w-4 text-yellow-600" />
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className="pt-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center text-muted-foreground">
            <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>{error}</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!analytics) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center text-muted-foreground">
            <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No analytics data available</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-full">
                <Users className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <div className="text-2xl font-bold">{analytics.overview.totalAttempts}</div>
                <p className="text-xs text-muted-foreground">Total Attempts</p>
                <p className="text-xs text-blue-600">{analytics.overview.uniqueUsers} unique users</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-full">
                <Target className="h-5 w-5 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <div className="text-2xl font-bold">{Math.round(analytics.overview.averageScore)}%</div>
                <p className="text-xs text-muted-foreground">Average Score</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-full">
                <CheckCircle className="h-5 w-5 text-purple-600 dark:text-purple-400" />
              </div>
              <div>
                <div className="text-2xl font-bold">{Math.round(analytics.overview.completionRate)}%</div>
                <p className="text-xs text-muted-foreground">Completion Rate</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-orange-100 dark:bg-orange-900/30 rounded-full">
                <Clock className="h-5 w-5 text-orange-600 dark:text-orange-400" />
              </div>
              <div>
                <div className="text-2xl font-bold">{Math.round(analytics.overview.averageTime)}m</div>
                <p className="text-xs text-muted-foreground">Avg Time</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Score Distribution */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Score Distribution
          </CardTitle>
          <CardDescription>
            How students performed on this quiz
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {analytics.scoreDistribution.map((range, index) => {
              const maxCount = Math.max(...analytics.scoreDistribution.map(r => r.count))
              const percentage = maxCount > 0 ? (range.count / maxCount) * 100 : 0
              
              return (
                <div key={index} className="flex items-center gap-4">
                  <div className="w-16 text-sm font-medium">{range.range}</div>
                  <div className="flex-1">
                    <Progress value={percentage} className="h-2" />
                  </div>
                  <div className="w-12 text-sm text-muted-foreground text-right">
                    {range.count}
                  </div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Insights */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {analytics.insights.map((insight, index) => (
          <Card key={index}>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-medium">{insight.title}</CardTitle>
                {getTrendIcon(insight.trend)}
              </div>
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold mb-1 ${getTrendColor(insight.trend)}`}>
                {insight.value}
              </div>
              <p className="text-xs text-muted-foreground">
                {insight.description}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Top Performers */}
      {analytics.topPerformers.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Award className="h-5 w-5" />
              Top Performers
            </CardTitle>
            <CardDescription>
              Students with the highest scores
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {analytics.topPerformers.map((performer, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <Badge variant="outline" className="w-8 h-8 rounded-full p-0 flex items-center justify-center">
                      {index + 1}
                    </Badge>
                    <div>
                      <div className="font-medium">{performer.user.name}</div>
                      <div className="text-sm text-muted-foreground">
                        {Math.round(performer.timeSpent / 60)} minutes
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-bold text-lg">{Math.round(performer.score)}%</div>
                    <div className="text-xs text-muted-foreground">
                      {new Date(performer.completedAt).toLocaleDateString()}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
