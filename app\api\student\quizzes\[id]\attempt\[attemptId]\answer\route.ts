import { NextRequest } from 'next/server'
 import { createAPIHandler, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const answerSchema = z.object({
  questionId: z.string(),
  answer: z.union([
    z.string(),
    z.array(z.string()),
    z.boolean()
  ]),
  timeSpent: z.number().optional()
})

// POST /api/student/quizzes/[id]/attempt/[attemptId]/answer - Submit answer for a question
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT',
     
    validateBody: answerSchema
  },
  async (request: NextRequest, { user, params, validatedBody }) => {
    const resolvedParams = await params
    const quizId = resolvedParams?.id as string
    const attemptId = resolvedParams?.attemptId as string
    const { questionId, answer, timeSpent } = validatedBody

    if (!quizId || !attemptId) {
      return APIResponse.error('Quiz ID and Attempt ID are required', 400)
    }

    try {
      // Verify attempt belongs to user and is active
      const attempt = await prisma.quizAttempt.findUnique({
        where: {
          id: attemptId,
          userId: user.id,
          quizId,
          completedAt: null
        },
        include: {
          quiz: {
            select: {
              timeLimit: true,
              endTime: true
            }
          }
        }
      })

      if (!attempt) {
        return APIResponse.error('Invalid or completed attempt', 400)
      }

      // Check if quiz has timed out
      if (attempt.quiz.timeLimit) {
        const timeElapsed = Math.floor((Date.now() - attempt.startedAt.getTime()) / 1000)
        if (timeElapsed > attempt.quiz.timeLimit * 60) {
          return APIResponse.error('Quiz time has expired', 400)
        }
      }

      // Check if quiz end time has passed
      if (attempt.quiz.endTime && attempt.quiz.endTime < new Date()) {
        return APIResponse.error('Quiz has ended', 400)
      }

      // Verify question belongs to this quiz
      const question = await prisma.question.findUnique({
        where: {
          id: questionId,
          quizId
        },
        select: {
          id: true,
          type: true,
          correctAnswer: true,
          points: true
        }
      })

      if (!question) {
        return APIResponse.error('Question not found in this quiz', 400)
      }

      // Calculate if answer is correct
      let isCorrect = false
      let points = 0

      switch (question.type) {
        case 'MCQ':
          isCorrect = answer === question.correctAnswer
          break
        case 'TRUE_FALSE':
          isCorrect = answer === (question.correctAnswer === 'true')
          break
        case 'SHORT_ANSWER':
          // Simple string comparison (case-insensitive)
          isCorrect = typeof answer === 'string' && 
            answer.toLowerCase().trim() === question.correctAnswer?.toLowerCase().trim()
          break
        case 'MATCHING':
          // For matching questions, answer should be an array of selected right items
          // correctAnswer is stored as JSON string of pairs: [{"left":"A","right":"B"}]
          if (Array.isArray(answer) && typeof question.correctAnswer === 'string') {
            try {
              const correctPairs = JSON.parse(question.correctAnswer)
              if (Array.isArray(correctPairs)) {
                // Extract the correct right items in order
                const correctRightItems = correctPairs.map((pair: any) => pair.right)
                // Compare the student's answers with correct answers
                isCorrect = JSON.stringify(answer) === JSON.stringify(correctRightItems)
              }
            } catch (e) {
              console.error('Error parsing MATCHING question correctAnswer:', e)
              isCorrect = false
            }
          }
          break
      }

      if (isCorrect) {
        points = question.points || 1
      }

      // Update the attempt's answers JSON field
      const currentAttempt = await prisma.quizAttempt.findUnique({
        where: { id: attemptId },
        select: { answers: true }
      })

      const currentAnswers = (currentAttempt?.answers as Record<string, any>) || {}
      const updatedAnswers = {
        ...currentAnswers,
        [questionId]: answer
      }

      await prisma.quizAttempt.update({
        where: { id: attemptId },
        data: {
          answers: updatedAnswers
        }
      })

      return APIResponse.success(
        {
          questionId,
          isCorrect,
          points,
          answeredAt: new Date().toISOString()
        },
        'Answer submitted successfully'
      )

    } catch (error) {
      console.error('Error submitting answer:', error)
      return APIResponse.error('Failed to submit answer', 500)
    }
  }
)
