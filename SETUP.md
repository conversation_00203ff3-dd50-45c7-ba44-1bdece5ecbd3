# Quiz App Setup Guide

This guide will help you set up the Quiz App with <PERSON>ustand for state management and Auth.js for authentication.

## Features Added

### 🔐 Authentication & Authorization
- **Auth.js (NextAuth.js v5)** integration with Prisma adapter
- **Role-based access control** (Student/Admin)
- **OAuth providers**: Google and GitHub
- **Protected routes** with middleware
- **Session management** with JWT strategy

### 🏪 State Management
- **Zustand store** for global state management
- **Persistent state** for user preferences
- **Separate stores** for auth, quiz, and UI state
- **TypeScript support** with proper typing

### 🎯 Enhanced Features
- **User menu** with role-based navigation
- **Header component** with authentication status
- **Error pages** for authentication failures
- **Unauthorized access handling**

## Setup Instructions

### 1. Install Dependencies
All required dependencies have been installed:
```bash
npm install zustand next-auth@beta @auth/prisma-adapter @radix-ui/react-dropdown-menu @radix-ui/react-avatar
```

### 2. Environment Variables
Copy `.env.example` to `.env.local` and fill in your values:

```bash
cp .env.example .env.local
```

Required environment variables:
- `AUTH_SECRET` - Generated automatically
- `DATABASE_URL` - Your PostgreSQL database URL
- `GOOGLE_CLIENT_ID` & `GOOGLE_CLIENT_SECRET` - Google OAuth credentials
- `GITHUB_CLIENT_ID` & `GITHUB_CLIENT_SECRET` - GitHub OAuth credentials

### 3. Database Setup
The Prisma schema has been updated with Auth.js models and role management:

```bash
# Generate Prisma client (already done)
npx prisma generate

# Run database migrations (when you have a database)
npx prisma migrate dev --name init

# Optional: Seed the database
npx prisma db seed
```

### 4. OAuth Provider Setup

#### Google OAuth
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing
3. Enable Google+ API
4. Create OAuth 2.0 credentials
5. Add authorized redirect URI: `http://localhost:3000/api/auth/callback/google`

#### GitHub OAuth
1. Go to GitHub Settings > Developer settings > OAuth Apps
2. Create a new OAuth App
3. Set Authorization callback URL: `http://localhost:3000/api/auth/callback/github`

### 5. Run the Application
```bash
npm run dev
```

## File Structure

### New Files Added
```
├── auth.ts                           # Auth.js configuration
├── middleware.ts                     # Route protection middleware
├── lib/
│   ├── store.ts                     # Zustand store
│   └── prisma.ts                    # Prisma client instance
├── components/
│   ├── header.tsx                   # App header with user menu
│   ├── user-menu.tsx               # User dropdown menu
│   ├── providers/
│   │   └── session-provider.tsx    # Session provider wrapper
│   └── ui/
│       ├── dropdown-menu.tsx       # Dropdown menu component
│       └── avatar.tsx              # Avatar component
├── app/
│   ├── api/auth/[...nextauth]/     # Auth.js API routes
│   ├── auth/
│   │   ├── signin/                 # Sign-in page
│   │   └── error/                  # Auth error page
│   └── unauthorized/               # Unauthorized access page
└── prisma/
    └── schema.prisma               # Updated with Auth.js models
```

## Usage

### Authentication
- Users can sign in with Google or GitHub
- New users are assigned the "STUDENT" role by default
- Admin users can access protected admin routes

### State Management
The Zustand store provides:
- **Auth state**: User info, authentication status
- **Quiz state**: Questions, answers, progress
- **UI state**: Theme, notifications, loading states

### Role-Based Access
- **Students**: Can take quizzes and view results
- **Admins**: Can access admin panel and manage quizzes
- **Protected routes**: `/admin/*` requires admin role
- **Middleware**: Automatically redirects unauthorized users

### API Integration
The existing AI SDK integration remains unchanged:
- PDF upload and processing
- Quiz generation with Google Gemini
- Real-time quiz creation progress

## Development Notes

### Zustand Store Usage
```typescript
import { useAuth, useQuiz, useUI } from '@/lib/store'

// In components
const { user, isAuthenticated } = useAuth()
const { questions, setQuestions } = useQuiz()
const { theme, setTheme } = useUI()
```

### Authentication Checks
```typescript
import { useSession } from 'next-auth/react'

const { data: session, status } = useSession()
const isAdmin = session?.user?.role === 'ADMIN'
```

### Protected Routes
Routes are automatically protected by middleware:
- `/admin/*` - Admin only
- Add more protected routes in `middleware.ts`

## Next Steps

1. **Set up your database** and run migrations
2. **Configure OAuth providers** with your credentials
3. **Customize user roles** and permissions as needed
4. **Add more protected routes** for different user types
5. **Implement quiz management** features for admins

## Troubleshooting

### Common Issues
1. **Database connection**: Ensure DATABASE_URL is correct
2. **OAuth errors**: Check client IDs and secrets
3. **Session issues**: Verify AUTH_SECRET is set
4. **Type errors**: Run `npx prisma generate` after schema changes

### Support
- Check the [Auth.js documentation](https://authjs.dev)
- Review [Zustand documentation](https://zustand-demo.pmnd.rs)
- Ensure all environment variables are properly set
