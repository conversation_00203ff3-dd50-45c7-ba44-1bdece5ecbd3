"use client"

import { signIn, getProviders } from "next-auth/react"
import { useEffect, useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { GitIcon } from "@/components/icons"
import { Chrome, GraduationCap, Shield, Users, BookOpen } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { motion } from "framer-motion"

interface Provider {
  id: string
  name: string
  type: string
  signinUrl: string
  callbackUrl: string
}

export default function SignUp() {
  const [providers, setProviders] = useState<Record<string, Provider> | null>(null)
  const [selectedRole, setSelectedRole] = useState<'STUDENT' | 'ADMIN'>('STUDENT')

  useEffect(() => {
    const fetchProviders = async () => {
      const res = await getProviders()
      setProviders(res)
    }
    fetchProviders()
  }, [])

  const getProviderIcon = (providerId: string) => {
    switch (providerId) {
      case 'google':
        return <Chrome className="w-5 h-5" />
      case 'github':
        return <GitIcon className="w-5 h-5" />
      default:
        return null
    }
  }

  const getProviderColor = (providerId: string) => {
    switch (providerId) {
      case 'google':
        return 'bg-red-600 hover:bg-red-700'
      case 'github':
        return 'bg-gray-800 hover:bg-gray-900'
      default:
        return 'bg-primary hover:bg-primary/90'
    }
  }

  const handleSignUp = (providerId: string) => {
    // Store the selected role in localStorage to be used after OAuth callback
    localStorage.setItem('selectedRole', selectedRole)
    signIn(providerId, { callbackUrl: '/' })
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-md"
      >
        <Card className="shadow-2xl border-0 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm">
          <CardHeader className="text-center space-y-4">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
              className="mx-auto w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center"
            >
              <BookOpen className="w-8 h-8 text-white" />
            </motion.div>
            <div>
              <CardTitle className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Join QuizMaster
              </CardTitle>
              <CardDescription className="text-lg mt-2">
                Create your account and start your learning journey
              </CardDescription>
            </div>
          </CardHeader>
          
          <CardContent className="space-y-6">
            {/* Role Selection */}
            <div className="space-y-4">
              <Label className="text-base font-semibold">Choose your role:</Label>
              <RadioGroup
                value={selectedRole}
                onValueChange={(value) => setSelectedRole(value as 'STUDENT' | 'ADMIN')}
                className="space-y-3"
              >
                <motion.div
                  whileHover={{ scale: 1.02 }}
                  className={`flex items-center space-x-3 p-4 rounded-lg border-2 transition-all cursor-pointer ${
                    selectedRole === 'STUDENT' 
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
                      : 'border-gray-200 dark:border-gray-700 hover:border-gray-300'
                  }`}
                  onClick={() => setSelectedRole('STUDENT')}
                >
                  <RadioGroupItem value="STUDENT" id="student" />
                  <div className="flex items-center space-x-3 flex-1">
                    <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-full">
                      <GraduationCap className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                      <Label htmlFor="student" className="font-semibold cursor-pointer">
                        Student
                      </Label>
                      <p className="text-sm text-muted-foreground">
                        Take quizzes, track progress, and learn
                      </p>
                    </div>
                  </div>
                  {selectedRole === 'STUDENT' && (
                    <Badge variant="default" className="bg-blue-500">
                      Selected
                    </Badge>
                  )}
                </motion.div>

                <motion.div
                  whileHover={{ scale: 1.02 }}
                  className={`flex items-center space-x-3 p-4 rounded-lg border-2 transition-all cursor-pointer ${
                    selectedRole === 'ADMIN' 
                      ? 'border-purple-500 bg-purple-50 dark:bg-purple-900/20' 
                      : 'border-gray-200 dark:border-gray-700 hover:border-gray-300'
                  }`}
                  onClick={() => setSelectedRole('ADMIN')}
                >
                  <RadioGroupItem value="ADMIN" id="admin" />
                  <div className="flex items-center space-x-3 flex-1">
                    <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-full">
                      <Shield className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                    </div>
                    <div>
                      <Label htmlFor="admin" className="font-semibold cursor-pointer">
                        Admin
                      </Label>
                      <p className="text-sm text-muted-foreground">
                        Create quizzes, manage users, and analyze data
                      </p>
                    </div>
                  </div>
                  {selectedRole === 'ADMIN' && (
                    <Badge variant="default" className="bg-purple-500">
                      Selected
                    </Badge>
                  )}
                </motion.div>
              </RadioGroup>
            </div>

            {/* Provider Buttons */}
            <div className="space-y-3">
              <Label className="text-base font-semibold">Sign up with:</Label>
              {providers &&
                Object.values(providers).map((provider) => (
                  <motion.div
                    key={provider.name}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Button
                      onClick={() => handleSignUp(provider.id)}
                      className={`w-full h-12 ${getProviderColor(provider.id)} text-white shadow-lg hover:shadow-xl transition-all duration-200`}
                      size="lg"
                    >
                      <div className="flex items-center justify-center space-x-3">
                        {getProviderIcon(provider.id)}
                        <span className="font-semibold">Continue with {provider.name}</span>
                      </div>
                    </Button>
                  </motion.div>
                ))}
              
              {!providers && (
                <div className="text-center text-muted-foreground py-4">
                  <div className="animate-spin w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-2"></div>
                  Loading sign-up options...
                </div>
              )}
            </div>
            
            <div className="text-center text-sm text-muted-foreground pt-4 border-t">
              <p>Already have an account?{' '}
                <a href="/auth/signin" className="text-blue-600 hover:text-blue-700 font-semibold">
                  Sign in here
                </a>
              </p>
              <p className="mt-2">
                By signing up, you agree to our{' '}
                <a href="/terms" className="text-blue-600 hover:underline">Terms of Service</a>
                {' '}and{' '}
                <a href="/privacy" className="text-blue-600 hover:underline">Privacy Policy</a>.
              </p>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}
