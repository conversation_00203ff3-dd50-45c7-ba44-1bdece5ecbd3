const { io } = require('socket.io-client');

// Test room isolation - messages should only appear in the correct room
async function testRoomIsolation() {
  console.log('🧪 Testing room isolation...');

  // Create three socket connections
  const user1 = io('http://localhost:3001', { transports: ['websocket', 'polling'] });
  const user2 = io('http://localhost:3001', { transports: ['websocket', 'polling'] });
  const user3 = io('http://localhost:3001', { transports: ['websocket', 'polling'] });

  // Wait for connections
  await new Promise((resolve) => {
    let connected = 0;
    
    [user1, user2, user3].forEach((socket, index) => {
      socket.on('connect', () => {
        console.log(`👤 User ${index + 1} connected:`, socket.id);
        connected++;
        if (connected === 3) resolve();
      });
    });
  });

  // Authenticate all users
  let authenticated = 0;
  
  [user1, user2, user3].forEach((socket, index) => {
    socket.on('authenticated', () => {
      console.log(`✅ User ${index + 1} authenticated`);
      authenticated++;
    });
  });

  user1.emit('authenticate', { userId: 'user1', name: 'User 1', email: '<EMAIL>', role: 'STUDENT' });
  user2.emit('authenticate', { userId: 'user2', name: 'User 2', email: '<EMAIL>', role: 'STUDENT' });
  user3.emit('authenticate', { userId: 'user3', name: 'User 3', email: '<EMAIL>', role: 'ADMIN' });

  // Wait for authentication
  while (authenticated < 3) {
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  // Set up message tracking
  const receivedMessages = {
    user1: [],
    user2: [],
    user3: []
  };

  user1.on('chat:message_received', (msg) => {
    receivedMessages.user1.push(msg);
    console.log('👤 User 1 received:', msg.message, 'in room', msg.roomId);
  });

  user2.on('chat:message_received', (msg) => {
    receivedMessages.user2.push(msg);
    console.log('👤 User 2 received:', msg.message, 'in room', msg.roomId);
  });

  user3.on('chat:message_received', (msg) => {
    receivedMessages.user3.push(msg);
    console.log('👤 User 3 received:', msg.message, 'in room', msg.roomId);
  });

  // Join different rooms
  console.log('🏠 Joining rooms...');
  user1.emit('chat:join', { roomId: 'student-general' });      // User 1 in general
  user2.emit('chat:join', { roomId: 'student-study-help' });   // User 2 in study help
  user3.emit('chat:join', { roomId: 'student-general' });      // User 3 in general (same as User 1)

  await new Promise(resolve => setTimeout(resolve, 1000));

  // Send messages to different rooms
  console.log('💬 Sending messages to different rooms...');
  
  user1.emit('chat:message', {
    roomId: 'student-general',
    message: 'Message from User 1 in GENERAL room',
    type: 'text'
  });

  await new Promise(resolve => setTimeout(resolve, 500));

  user2.emit('chat:message', {
    roomId: 'student-study-help',
    message: 'Message from User 2 in STUDY HELP room',
    type: 'text'
  });

  await new Promise(resolve => setTimeout(resolve, 500));

  user3.emit('chat:message', {
    roomId: 'student-general',
    message: 'Message from User 3 in GENERAL room',
    type: 'text'
  });

  // Wait for messages to be processed
  await new Promise(resolve => setTimeout(resolve, 2000));

  // Analyze results
  console.log('\n📊 Results Analysis:');
  console.log('User 1 (in general room) received:', receivedMessages.user1.length, 'messages');
  console.log('User 2 (in study help room) received:', receivedMessages.user2.length, 'messages');
  console.log('User 3 (in general room) received:', receivedMessages.user3.length, 'messages');

  // Expected results:
  // User 1 should receive 2 messages (from User 1 and User 3, both in general room)
  // User 2 should receive 1 message (from User 2 in study help room)
  // User 3 should receive 2 messages (from User 1 and User 3, both in general room)

  const success = 
    receivedMessages.user1.length === 2 &&
    receivedMessages.user2.length === 1 &&
    receivedMessages.user3.length === 2;

  if (success) {
    console.log('✅ Room isolation test PASSED - messages are properly isolated by room');
  } else {
    console.log('❌ Room isolation test FAILED - messages are bleeding across rooms');
  }

  // Close connections
  user1.disconnect();
  user2.disconnect();
  user3.disconnect();
}

// Run the test
testRoomIsolation().catch(console.error);
