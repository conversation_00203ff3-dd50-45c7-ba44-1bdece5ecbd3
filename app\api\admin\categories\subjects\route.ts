import { NextRequest, NextResponse } from 'next/server'

import { prisma } from '@/lib/prisma'
import { z } from 'zod'
import { auth } from '@/auth'

const createSubjectSchema = z.object({
  name: z.string().min(1, "Subject name is required"),
  description: z.string().optional(),
})

// GET /api/admin/categories/subjects - Get all subjects
export async function GET() {
  try {
    const session = await auth()
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const subjects = await prisma.subject.findMany({
      where: { isActive: true },
      include: {
        chapters: {
          where: { isActive: true },
          include: {
            topics: {
              where: { isActive: true },
              orderBy: { order: 'asc' }
            }
          },
          orderBy: { order: 'asc' }
        },
        _count: {
          select: {
            quizzes: true,
            chapters: true
          }
        }
      },
      orderBy: { name: 'asc' }
    })

    return NextResponse.json({
      subjects,
      success: true
    })
  } catch (error) {
    console.error('Error fetching subjects:', error)
    return NextResponse.json(
      { error: 'Failed to fetch subjects' },
      { status: 500 }
    )
  }
}

// POST /api/admin/categories/subjects - Create new subject
export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const validatedData = createSubjectSchema.parse(body)

    // Check if subject already exists
    const existingSubject = await prisma.subject.findUnique({
      where: { name: validatedData.name }
    })

    if (existingSubject) {
      return NextResponse.json(
        { error: 'Subject with this name already exists' },
        { status: 400 }
      )
    }

    const subject = await prisma.subject.create({
      data: validatedData,
      include: {
        chapters: {
          include: {
            topics: true
          }
        },
        _count: {
          select: {
            quizzes: true,
            chapters: true
          }
        }
      }
    })

    return NextResponse.json({
      subject,
      success: true
    })
  } catch (error) {
    console.error('Error creating subject:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Failed to create subject' },
      { status: 500 }
    )
  }
}
