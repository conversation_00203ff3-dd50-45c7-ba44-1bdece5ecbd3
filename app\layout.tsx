import "./globals.css";
import { <PERSON>ada<PERSON> } from "next";
import { Toaster } from "sonner";
import { ThemeProvider } from "next-themes";
import { <PERSON><PERSON><PERSON> } from "next/font/google";
import { AuthProvider } from "@/components/providers/session-provider";
import { SettingsProvider } from "@/lib/settings-context";
import { MaintenanceMode } from "@/components/maintenance-mode";

import { RoleUpdateHandler } from "@/components/auth/role-update-handler";
import { SkipNav, SkipNavTarget } from "@/components/accessibility/skip-nav";
import { GlobalKeyboardShortcuts } from "@/components/accessibility/keyboard-shortcuts";

const geist = Geist({ subsets: ["latin"] });

export const metadata: Metadata = {
  metadataBase: new URL("https://examace.vercel.app"),
  title: "ExamAce - India's #1 Online Exam Coaching Platform",
  description: "Crack every Indian exam with ExamAce. Expert teachers, AI-powered learning, and proven results for JEE, NEET, UPSC, SSC, Banking, and more. Start your free trial today!",
  keywords: ["JEE", "NEET", "UPSC", "SSC", "Banking", "online coaching", "exam preparation", "India", "competitive exams", "AI learning"],
  authors: [{ name: "ExamAce Team" }],
  openGraph: {
    title: "ExamAce - India's #1 Online Exam Coaching Platform",
    description: "Crack every Indian exam with ExamAce. Expert teachers, AI-powered learning, and proven results for JEE, NEET, UPSC, SSC, Banking, and more.",
    type: "website",
    locale: "en_IN",
  },
  twitter: {
    card: "summary_large_image",
    title: "ExamAce - India's #1 Online Exam Coaching Platform",
    description: "Crack every Indian exam with ExamAce. Expert teachers, AI-powered learning, and proven results for JEE, NEET, UPSC, SSC, Banking, and more.",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning className={`${geist.className}`}>
      <body suppressHydrationWarning>
        <AuthProvider>
          <SettingsProvider>
            <ThemeProvider attribute="class" enableSystem defaultTheme="light">
              <SkipNav />
              <RoleUpdateHandler />
              <GlobalKeyboardShortcuts />
              <MaintenanceMode>
                <div className="relative flex min-h-screen flex-col">
                  <SkipNavTarget>
                    {children}
                  </SkipNavTarget>
                </div>
              </MaintenanceMode>
              <Toaster position="top-center" richColors />
            </ThemeProvider>
          </SettingsProvider>
        </AuthProvider>
      </body>
    </html>
  );
}
