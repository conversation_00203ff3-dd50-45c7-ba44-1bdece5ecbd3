'use client'

import { useState } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Printer, Download, Eye } from 'lucide-react'
import { Badge } from '@/components/ui/badge'

interface PrintQuizFormatProps {
  quiz: {
    title: string
    description?: string
    instructions?: string
    questions: Array<{
      id: string
      type: string
      text: string
      options?: string[]
      correctAnswer: string
      explanation?: string
      points: number
      order: number
    }>
    metadata?: {
      totalPoints: number
      estimatedDuration: number
      tags?: string[]
    }
  }
  institutionName?: string
  testDate?: string
  testType?: string
  includeAnswerKey?: boolean
  includeInstructions?: boolean
}

export function PrintQuizFormat({ 
  quiz, 
  institutionName, 
  testDate, 
  testType,
  includeAnswerKey = true,
  includeInstructions = true 
}: PrintQuizFormatProps) {
  const [showPreview, setShowPreview] = useState(false)

  const handlePrint = () => {
    const printWindow = window.open('', '_blank')
    if (printWindow) {
      printWindow.document.write(generatePrintHTML())
      printWindow.document.close()

      // Wait for content to load, then print
      printWindow.onload = () => {
        // Remove any browser-generated headers/footers
        printWindow.focus()
        setTimeout(() => {
          printWindow.print()
          printWindow.close()
        }, 500)
      }
    }
  }

  const handleDownload = () => {
    const element = document.createElement('a')
    const file = new Blob([generatePrintHTML()], { type: 'text/html' })
    element.href = URL.createObjectURL(file)
    element.download = `${quiz.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.html`
    document.body.appendChild(element)
    element.click()
    document.body.removeChild(element)
  }

  const generatePrintHTML = () => {
    return `
<!DOCTYPE html>
<html>
<head>
    <title>${quiz.title}</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body { 
            font-family: 'Times New Roman', serif; 
            margin: 20px; 
            line-height: 1.6;
            color: #000;
        }
        .header { 
            text-align: center; 
            border-bottom: 2px solid #000; 
            padding-bottom: 20px; 
            margin-bottom: 30px; 
        }
        .institution { 
            font-size: 18px; 
            font-weight: bold; 
            margin-bottom: 10px; 
        }
        .test-title { 
            font-size: 24px; 
            font-weight: bold; 
            margin: 15px 0; 
        }
        .test-info { 
            display: flex; 
            justify-content: space-between; 
            margin: 20px 0; 
            font-size: 14px; 
        }
        .instructions { 
            background: #f5f5f5; 
            padding: 15px; 
            margin: 20px 0; 
            border-left: 4px solid #333; 
        }
        .question { 
            margin: 25px 0; 
            page-break-inside: avoid; 
        }
        .question-header { 
            font-weight: bold; 
            margin-bottom: 10px; 
        }
        .options { 
            margin: 10px 0 10px 20px; 
        }
        .option { 
            margin: 8px 0; 
        }
        .answer-key { 
            page-break-before: always; 
            margin-top: 40px; 
        }
        .answer-key h2 { 
            border-bottom: 1px solid #000; 
            padding-bottom: 10px; 
        }
        .answer-item { 
            margin: 15px 0; 
            padding: 10px; 
            background: #f9f9f9; 
        }
        @media print {
            body { margin: 15px; }
            .no-print { display: none; }
            .header {
                position: static !important;
                page-break-inside: avoid;
            }
            .test-info {
                page-break-after: avoid;
            }
            .student-info {
                page-break-after: avoid;
            }
            .instructions {
                page-break-after: avoid;
            }
            .question {
                page-break-inside: avoid;
                break-inside: avoid;
            }
            .answer-key {
                page-break-before: always;
            }
            /* Hide headers and footers on subsequent pages */
            @page {
                margin: 15mm;
                @top-left { content: none; }
                @top-center { content: none; }
                @top-right { content: none; }
                @bottom-left { content: none; }
                @bottom-center { content: none; }
                @bottom-right { content: none; }
            }
            /* Ensure no page headers/footers */
            .page-header { display: none !important; }
            .page-footer { display: none !important; }
        }
        .student-info {
            border: 1px solid #000;
            padding: 15px;
            margin: 20px 0;
        }
        .student-info table {
            width: 100%;
            border-collapse: collapse;
        }
        .student-info td {
            padding: 8px;
            border-bottom: 1px dotted #666;
        }
    </style>
    <script>
        // Remove browser headers and footers
        window.onbeforeprint = function() {
            document.title = '';
        };

        window.onafterprint = function() {
            document.title = '${quiz.title}';
        };
    </script>
</head>
<body>
    <div class="header">
        ${institutionName ? `<div class="institution">${institutionName}</div>` : ''}
        <div class="test-title">${quiz.title}</div>
        ${quiz.description ? `<div style="font-style: italic; margin-top: 10px;">${quiz.description}</div>` : ''}
    </div>

    <div class="test-info">
        <div><strong>Date:</strong> ${testDate || '_____________'}</div>
        <div><strong>Time:</strong> ${quiz.metadata?.estimatedDuration || 0} minutes</div>
        <div><strong>Total Marks:</strong> ${quiz.metadata?.totalPoints || 0}</div>
        <div><strong>Questions:</strong> ${quiz.questions?.length || 0}</div>
    </div>

    <div class="student-info">
        <table>
            <tr>
                <td style="width: 20%;"><strong>Name:</strong></td>
                <td style="width: 30%;">_________________________</td>
                <td style="width: 20%;"><strong>Roll No:</strong></td>
                <td style="width: 30%;">_________________________</td>
            </tr>
            <tr>
                <td><strong>Class:</strong></td>
                <td>_________________________</td>
                <td><strong>Section:</strong></td>
                <td>_________________________</td>
            </tr>
        </table>
    </div>

    ${includeInstructions && quiz.instructions ? `
    <div class="instructions">
        <h3>Instructions:</h3>
        <div>${quiz.instructions}</div>
        <ul style="margin-top: 10px;">
            <li>Read all questions carefully before answering</li>
            <li>Write clearly and legibly</li>
            <li>Manage your time effectively</li>
            <li>Review your answers before submission</li>
        </ul>
    </div>
    ` : ''}

    <div class="questions">
        ${(quiz.questions || []).map((question, index) => `
            <div class="question">
                <div class="question-header">
                    Q${index + 1}. ${question.text} 
                    <span style="float: right; font-weight: normal;">[${question.points} mark${question.points !== 1 ? 's' : ''}]</span>
                </div>
                ${question.type === 'MCQ' && question.options ? `
                    <div class="options">
                        ${question.options.map((option, optIndex) => `
                            <div class="option">
                                (${String.fromCharCode(65 + optIndex)}) ${option}
                            </div>
                        `).join('')}
                    </div>
                ` : question.type === 'TRUE_FALSE' ? `
                    <div class="options">
                        <div class="option">(A) True</div>
                        <div class="option">(B) False</div>
                    </div>
                ` : `
                    <div style="margin: 20px 0; min-height: 60px; border-bottom: 1px dotted #666;">
                        <em>Answer:</em>
                    </div>
                `}
            </div>
        `).join('')}
    </div>

    ${includeAnswerKey ? `
    <div class="answer-key">
        <h2>Answer Key</h2>
        ${(quiz.questions || []).map((question, index) => `
            <div class="answer-item">
                <strong>Q${index + 1}:</strong> ${question.correctAnswer}
                ${question.explanation ? `<br><em>Explanation:</em> ${question.explanation}` : ''}
            </div>
        `).join('')}
    </div>
    ` : ''}
</body>
</html>
    `
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Print Format</span>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowPreview(!showPreview)}
            >
              <Eye className="h-4 w-4 mr-2" />
              {showPreview ? 'Hide' : 'Preview'}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleDownload}
            >
              <Download className="h-4 w-4 mr-2" />
              Download
            </Button>
            <Button
              size="sm"
              onClick={handlePrint}
            >
              <Printer className="h-4 w-4 mr-2" />
              Print
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center gap-4 text-sm text-muted-foreground">
            <span>Questions: {quiz.questions?.length || 0}</span>
            <span>Total Marks: {quiz.metadata?.totalPoints || 0}</span>
            <span>Duration: {quiz.metadata?.estimatedDuration || 0} minutes</span>
            {testType && <Badge variant="outline">{testType}</Badge>}
          </div>
          
          {showPreview && (
            <div 
              className="border rounded-lg p-4 max-h-96 overflow-y-auto bg-white"
              dangerouslySetInnerHTML={{ __html: generatePrintHTML() }}
            />
          )}
        </div>
      </CardContent>
    </Card>
  )
}
