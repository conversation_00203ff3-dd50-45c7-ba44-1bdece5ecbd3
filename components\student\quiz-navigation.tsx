"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Flag, CheckCircle, Circle, AlertCircle } from "lucide-react"
import { motion } from "framer-motion"

interface Question {
  id: string
  type: 'MCQ' | 'TRUE_FALSE' | 'FILL_BLANK' | 'ESSAY'
  text: string
  points: number
  difficulty?: 'EASY' | 'MEDIUM' | 'HARD'
}

interface QuizNavigationProps {
  questions: Question[]
  answers: Record<string, any>
  flaggedQuestions: string[]
  currentQuestionIndex: number
  onQuestionSelect: (index: number) => void
  disabled?: boolean
}

export function QuizNavigation({
  questions,
  answers,
  flaggedQuestions,
  currentQuestionIndex,
  onQuestionSelect,
  disabled = false
}: QuizNavigationProps) {
  const getQuestionStatus = (question: Question, index: number) => {
    const isAnswered = answers[question.id] !== undefined && answers[question.id] !== ""
    const isFlagged = flaggedQuestions.includes(question.id)
    const isCurrent = index === currentQuestionIndex

    if (isCurrent) return 'current'
    if (isAnswered) return 'answered'
    if (isFlagged) return 'flagged'
    return 'unanswered'
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'current':
        return <Circle className="h-4 w-4 fill-primary text-primary" />
      case 'answered':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'flagged':
        return <Flag className="h-4 w-4 text-red-500 fill-red-500" />
      default:
        return <Circle className="h-4 w-4 text-muted-foreground" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'current':
        return 'bg-primary text-primary-foreground border-primary'
      case 'answered':
        return 'bg-green-50 text-green-700 border-green-200 hover:bg-green-100 dark:bg-green-900/20 dark:text-green-300 dark:border-green-800'
      case 'flagged':
        return 'bg-red-50 text-red-700 border-red-200 hover:bg-red-100 dark:bg-red-900/20 dark:text-red-300 dark:border-red-800'
      default:
        return 'bg-background text-muted-foreground border-muted hover:bg-muted/50'
    }
  }

  const answeredCount = Object.keys(answers).filter(key => 
    answers[key] !== undefined && answers[key] !== ""
  ).length
  const flaggedCount = flaggedQuestions.length

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-base">Question Navigation</CardTitle>
        <div className="flex items-center gap-4 text-sm text-muted-foreground">
          <span>{answeredCount} of {questions.length} answered</span>
          {flaggedCount > 0 && (
            <span className="flex items-center gap-1">
              <Flag className="h-3 w-3 text-red-500" />
              {flaggedCount} flagged
            </span>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-5 gap-2">
          {questions.map((question, index) => {
            const status = getQuestionStatus(question, index)
            return (
              <motion.div
                key={question.id}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.2, delay: 0.02 * index }}
              >
                <Button
                  variant="outline"
                  size="sm"
                  className={`w-full h-12 p-2 flex flex-col items-center justify-center text-xs transition-all ${getStatusColor(status)} ${
                    disabled ? 'opacity-50 cursor-not-allowed' : ''
                  }`}
                  onClick={() => !disabled && onQuestionSelect(index)}
                  disabled={disabled}
                >
                  <div className="flex items-center justify-center mb-1">
                    {getStatusIcon(status)}
                  </div>
                  <span className="font-medium">{index + 1}</span>
                </Button>
              </motion.div>
            )
          })}
        </div>

        {/* Legend */}
        <div className="mt-6 space-y-3">
          <h4 className="text-sm font-medium">Legend:</h4>
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div className="flex items-center gap-2">
              <Circle className="h-3 w-3 fill-primary text-primary" />
              <span>Current</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-3 w-3 text-green-600" />
              <span>Answered</span>
            </div>
            <div className="flex items-center gap-2">
              <Flag className="h-3 w-3 text-red-500 fill-red-500" />
              <span>Flagged</span>
            </div>
            <div className="flex items-center gap-2">
              <Circle className="h-3 w-3 text-muted-foreground" />
              <span>Unanswered</span>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="mt-6 space-y-2">
          <div className="flex justify-between text-sm">
            <span>Progress:</span>
            <span className="font-medium">
              {Math.round((answeredCount / questions.length) * 100)}%
            </span>
          </div>
          <div className="w-full bg-muted rounded-full h-2">
            <motion.div
              className="bg-primary h-2 rounded-full transition-all duration-500"
              style={{ width: `${(answeredCount / questions.length) * 100}%` }}
            />
          </div>
        </div>

        {/* Question Type Summary */}
        <div className="mt-6">
          <h4 className="text-sm font-medium mb-3">Question Types:</h4>
          <div className="space-y-2">
            {Object.entries(
              questions.reduce((acc, q) => {
                acc[q.type] = (acc[q.type] || 0) + 1
                return acc
              }, {} as Record<string, number>)
            ).map(([type, count]) => (
              <div key={type} className="flex items-center justify-between text-xs">
                <Badge variant="outline" className="text-xs">
                  {type.replace('_', ' ')}
                </Badge>
                <span className="text-muted-foreground">{count}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Difficulty Distribution */}
        <div className="mt-6">
          <h4 className="text-sm font-medium mb-3">Difficulty:</h4>
          <div className="space-y-2">
            {Object.entries(
              questions.reduce((acc, q) => {
                const difficulty = q.difficulty || 'MEDIUM'
                acc[difficulty] = (acc[difficulty] || 0) + 1
                return acc
              }, {} as Record<string, number>)
            ).map(([difficulty, count]) => (
              <div key={difficulty} className="flex items-center justify-between text-xs">
                <Badge 
                  variant="outline" 
                  className={`text-xs ${
                    difficulty === 'EASY' ? 'text-green-600 border-green-600' :
                    difficulty === 'MEDIUM' ? 'text-yellow-600 border-yellow-600' :
                    'text-red-600 border-red-600'
                  }`}
                >
                  {difficulty}
                </Badge>
                <span className="text-muted-foreground">{count}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Total Points */}
        <div className="mt-6 pt-4 border-t">
          <div className="flex justify-between text-sm font-medium">
            <span>Total Points:</span>
            <span>{questions.reduce((sum, q) => sum + q.points, 0)}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
