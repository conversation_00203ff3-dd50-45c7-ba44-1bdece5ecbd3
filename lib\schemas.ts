import { z } from "zod";

// Base question schema for MCQ
export const mcqQuestionSchema = z.object({
  type: z.literal("MCQ"),
  question: z.string().describe("The question text"),
  options: z
    .array(z.string())
    .length(4)
    .describe("Four possible answers to the question. Only one should be correct. They should all be of equal lengths."),
  answer: z
    .enum(["A", "B", "C", "D"])
    .describe("The correct answer, where A is the first option, B is the second, and so on."),
  explanation: z.string().optional().describe("Optional explanation for the correct answer"),
});

// True/False question schema
export const trueFalseQuestionSchema = z.object({
  type: z.literal("TRUE_FALSE"),
  question: z.string().describe("The question text"),
  answer: z.boolean().describe("True or false answer"),
  explanation: z.string().optional().describe("Optional explanation for the correct answer"),
});

// Short answer question schema
export const shortAnswerQuestionSchema = z.object({
  type: z.literal("SHORT_ANSWER"),
  question: z.string().describe("The question text"),
  answer: z.string().describe("The expected short answer"),
  keywords: z.array(z.string()).optional().describe("Key terms that should be in the answer"),
  explanation: z.string().optional().describe("Optional explanation for the answer"),
});

// Matching question schema
export const matchingQuestionSchema = z.object({
  type: z.literal("MATCHING"),
  question: z.string().describe("The question text or instruction"),
  pairs: z.array(z.object({
    left: z.string().describe("Left side item to match"),
    right: z.string().describe("Right side item to match"),
  })).min(4).max(4).describe("Exactly 4 pairs of items to match"),
  explanation: z.string().optional().describe("Optional explanation for the matching"),
});

// Union of all question types
export const questionSchema = z.discriminatedUnion("type", [
  mcqQuestionSchema,
  trueFalseQuestionSchema,
  shortAnswerQuestionSchema,
  matchingQuestionSchema,
]);

export type Question = z.infer<typeof questionSchema>;
export type MCQQuestion = z.infer<typeof mcqQuestionSchema>;
export type TrueFalseQuestion = z.infer<typeof trueFalseQuestionSchema>;
export type ShortAnswerQuestion = z.infer<typeof shortAnswerQuestionSchema>;
export type MatchingQuestion = z.infer<typeof matchingQuestionSchema>;

// Legacy schema for backward compatibility (MCQ only)
export const legacyQuestionSchema = z.object({
  question: z.string(),
  options: z.array(z.string()).length(4),
  answer: z.enum(["A", "B", "C", "D"]),
});

export const questionsSchema = z.array(legacyQuestionSchema).length(4);

// Flexible questions schema for admin use (1-20 questions)
export const flexibleQuestionsSchema = z.array(questionSchema).min(1).max(20);

// Helper function to create dynamic questions schema
export const createQuestionsSchema = (count: number) => z.array(questionSchema).length(count);
