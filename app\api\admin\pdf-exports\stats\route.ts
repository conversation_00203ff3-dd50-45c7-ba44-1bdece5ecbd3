import { NextRequest } from 'next/server'
import { z } from 'zod'
 import { createAPIHandler, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'

const querySchema = z.object({
  period: z.enum(['7d', '30d', '90d', 'all']).optional().default('30d')
})

// GET /api/admin/pdf-exports/stats - Get PDF export statistics
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
     
    validateQuery: querySchema
  },
  async (request: NextRequest, { validatedQuery }) => {
    const { period } = validatedQuery

    // Calculate date range
    let dateFilter: any = {}
    if (period !== 'all') {
      const days = period === '7d' ? 7 : period === '30d' ? 30 : 90
      const startDate = new Date()
      startDate.setDate(startDate.getDate() - days)
      dateFilter = {
        createdAt: {
          gte: startDate
        }
      }
    }

    try {
      // Get total counts by status with error handling
      let totalExports = 0, completedExports = 0, failedExports = 0, pendingExports = 0

      try {
        [totalExports, completedExports, failedExports, pendingExports] = await Promise.all([
          prisma.pdfExport.count({
            where: dateFilter
          }),
          prisma.pdfExport.count({
            where: {
              status: 'completed',
              ...dateFilter
            }
          }),
          prisma.pdfExport.count({
            where: {
              status: 'failed',
              ...dateFilter
            }
          }),
          prisma.pdfExport.count({
            where: {
              status: { in: ['pending', 'processing'] },
              ...dateFilter
            }
          })
        ])
      } catch (countError) {
        console.error('Error fetching export counts:', countError)
        // Use fallback values - all zeros
      }

      // Get exports by type with error handling
      let exportsByType: any[] = []
      try {
        exportsByType = await prisma.pdfExport.groupBy({
          by: ['type'],
          where: dateFilter,
          _count: {
            id: true
          }
        })
      } catch (typeError) {
        console.error('Error fetching exports by type:', typeError)
        exportsByType = []
      }

      // Get total file size with error handling
      let totalSizeResult = { _sum: { size: 0 } }
      try {
        totalSizeResult = await prisma.pdfExport.aggregate({
          where: {
            status: 'completed',
            ...dateFilter
          },
          _sum: {
            size: true
          }
        })
      } catch (sizeError) {
        console.error('Error fetching total size:', sizeError)
      }

      // Get recent exports with error handling
      let recentExports: any[] = []
      try {
        recentExports = await prisma.pdfExport.findMany({
          where: dateFilter,
          take: 10,
          orderBy: {
            createdAt: 'desc'
          },
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true
              }
            }
          }
        })
      } catch (recentError) {
        console.error('Error fetching recent exports:', recentError)
        recentExports = []
      }

      // Get daily export counts for the period (for charts) - using Prisma instead of raw SQL
      let dailyExports: any[] = []
      try {
        // For now, we'll create mock daily data based on the period
        // In a real implementation, you'd want to group by date properly
        const days = period === '7d' ? 7 : period === '30d' ? 30 : period === '90d' ? 90 : 30
        const today = new Date()

        for (let i = days - 1; i >= 0; i--) {
          const date = new Date(today)
          date.setDate(date.getDate() - i)

          // Get exports for this specific date
          const dayStart = new Date(date)
          dayStart.setHours(0, 0, 0, 0)
          const dayEnd = new Date(date)
          dayEnd.setHours(23, 59, 59, 999)

          const dayExports = await prisma.pdfExport.findMany({
            where: {
              createdAt: {
                gte: dayStart,
                lte: dayEnd
              }
            }
          })

          const total = dayExports.length
          const completed = dayExports.filter(e => e.status === 'completed').length
          const failed = dayExports.filter(e => e.status === 'failed').length

          dailyExports.push({
            date: date.toISOString().split('T')[0],
            total,
            completed,
            failed
          })
        }
      } catch (error) {
        console.error('Error fetching daily exports:', error)
        // Provide fallback data
        dailyExports = []
      }

      // Calculate success rate
      const successRate = totalExports > 0 ? Math.round((completedExports / totalExports) * 100) : 0

      // Format file size
      const totalSize = totalSizeResult._sum.size || 0
      const formatFileSize = (bytes: number) => {
        if (bytes === 0) return '0 B'
        const k = 1024
        const sizes = ['B', 'KB', 'MB', 'GB']
        const i = Math.floor(Math.log(bytes) / Math.log(k))
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
      }

      const stats = {
        overview: {
          totalExports,
          completedExports,
          failedExports,
          pendingExports,
          successRate,
          totalSize: formatFileSize(totalSize),
          totalSizeBytes: totalSize
        },
        exportsByType: exportsByType.map(item => ({
          type: item.type,
          count: item._count.id
        })),
        recentExports: recentExports.map(pdfExport => ({
          id: pdfExport.id,
          type: pdfExport.type,
          filename: pdfExport.filename,
          size: pdfExport.size,
          status: pdfExport.status,
          createdAt: pdfExport.createdAt.toISOString(),
          user: pdfExport.user
        })),
        dailyExports: dailyExports,
        period
      }

      return APIResponse.success(stats, 'PDF export statistics retrieved successfully')
    } catch (error) {
      console.error('Error fetching PDF export stats:', error)

      // Return fallback stats if database is not available
      const fallbackStats = {
        overview: {
          totalExports: 0,
          completedExports: 0,
          failedExports: 0,
          pendingExports: 0,
          successRate: 0,
          totalSize: '0 B',
          totalSizeBytes: 0
        },
        exportsByType: [],
        recentExports: [],
        dailyExports: [],
        period
      }

      return APIResponse.success(fallbackStats, 'PDF export statistics retrieved (fallback data)')
    }
  }
)
