"use client"

import { useState, useEffect } from "react"
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Content,
  <PERSON>alogDescription,
  <PERSON><PERSON>Footer,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { 
  Edit, 
  Save,
  Loader2,
  Calendar,
  Clock
} from "lucide-react"
import { toast } from "sonner"

interface ScheduledQuiz {
  id: string
  title: string
  description?: string
  startTime: string
  endTime: string
  duration?: number
  maxAttempts: number
  allowLateSubmission: boolean
  showResults: boolean
  shuffleQuestions: boolean
}

interface EditScheduleModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  scheduledQuiz: ScheduledQuiz | null
  onScheduleUpdated: () => void
}

export function EditScheduleModal({ 
  open, 
  onO<PERSON>Change, 
  scheduledQuiz,
  onScheduleUpdated
}: EditScheduleModalProps) {
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    startTime: "",
    endTime: "",
    duration: 60,
    maxAttempts: 1,
    allowLateSubmission: false,
    showResults: true,
    shuffleQuestions: false
  })
  const [saving, setSaving] = useState(false)

  useEffect(() => {
    if (scheduledQuiz && open) {
      // Convert ISO strings to datetime-local format
      const startTime = new Date(scheduledQuiz.startTime)
      const endTime = new Date(scheduledQuiz.endTime)
      
      setFormData({
        title: scheduledQuiz.title,
        description: scheduledQuiz.description || "",
        startTime: startTime.toISOString().slice(0, 16), // Format for datetime-local
        endTime: endTime.toISOString().slice(0, 16),
        duration: scheduledQuiz.duration || 60,
        maxAttempts: scheduledQuiz.maxAttempts,
        allowLateSubmission: scheduledQuiz.allowLateSubmission,
        showResults: scheduledQuiz.showResults,
        shuffleQuestions: scheduledQuiz.shuffleQuestions
      })
    }
  }, [scheduledQuiz, open])

  const handleSave = async () => {
    if (!scheduledQuiz) return

    if (!formData.title.trim()) {
      toast.error('Please enter a title')
      return
    }

    if (!formData.startTime || !formData.endTime) {
      toast.error('Please select start and end times')
      return
    }

    if (new Date(formData.startTime) >= new Date(formData.endTime)) {
      toast.error('End time must be after start time')
      return
    }

    try {
      setSaving(true)
      
      const response = await fetch(`/api/admin/scheduled-quizzes/${scheduledQuiz.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          title: formData.title.trim(),
          description: formData.description.trim() || undefined,
          startTime: new Date(formData.startTime).toISOString(),
          endTime: new Date(formData.endTime).toISOString(),
          duration: formData.duration,
          maxAttempts: formData.maxAttempts,
          allowLateSubmission: formData.allowLateSubmission,
          showResults: formData.showResults,
          shuffleQuestions: formData.shuffleQuestions
        })
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to update schedule')
      }

      toast.success('Schedule updated successfully!')
      onScheduleUpdated()
      onOpenChange(false)
    } catch (error) {
      console.error('Error updating schedule:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to update schedule')
    } finally {
      setSaving(false)
    }
  }

  const handleClose = () => {
    if (!saving) {
      onOpenChange(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Edit className="h-5 w-5" />
            Edit Schedule
          </DialogTitle>
          <DialogDescription>
            Update the schedule settings for this quiz
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Basic Info */}
          <div className="space-y-4">
            <div>
              <Label htmlFor="title">Title *</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                placeholder="Enter schedule title"
                disabled={saving}
              />
            </div>

            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Optional description"
                rows={3}
                disabled={saving}
              />
            </div>
          </div>

          {/* Timing */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Schedule Timing
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="startTime">Start Time *</Label>
                <Input
                  id="startTime"
                  type="datetime-local"
                  value={formData.startTime}
                  onChange={(e) => setFormData(prev => ({ ...prev, startTime: e.target.value }))}
                  disabled={saving}
                />
              </div>

              <div>
                <Label htmlFor="endTime">End Time *</Label>
                <Input
                  id="endTime"
                  type="datetime-local"
                  value={formData.endTime}
                  onChange={(e) => setFormData(prev => ({ ...prev, endTime: e.target.value }))}
                  disabled={saving}
                />
              </div>
            </div>

            <div>
              <Label htmlFor="duration">Duration (minutes)</Label>
              <Input
                id="duration"
                type="number"
                min="1"
                value={formData.duration}
                onChange={(e) => setFormData(prev => ({ ...prev, duration: parseInt(e.target.value) || 60 }))}
                disabled={saving}
              />
            </div>
          </div>

          {/* Settings */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Quiz Settings
            </h3>

            <div>
              <Label htmlFor="maxAttempts">Maximum Attempts</Label>
              <Input
                id="maxAttempts"
                type="number"
                min="1"
                value={formData.maxAttempts}
                onChange={(e) => setFormData(prev => ({ ...prev, maxAttempts: parseInt(e.target.value) || 1 }))}
                disabled={saving}
              />
            </div>

            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="allowLateSubmission"
                  checked={formData.allowLateSubmission}
                  onCheckedChange={(checked) => setFormData(prev => ({ ...prev, allowLateSubmission: checked as boolean }))}
                  disabled={saving}
                />
                <Label htmlFor="allowLateSubmission">Allow late submission</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="showResults"
                  checked={formData.showResults}
                  onCheckedChange={(checked) => setFormData(prev => ({ ...prev, showResults: checked as boolean }))}
                  disabled={saving}
                />
                <Label htmlFor="showResults">Show results after completion</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="shuffleQuestions"
                  checked={formData.shuffleQuestions}
                  onCheckedChange={(checked) => setFormData(prev => ({ ...prev, shuffleQuestions: checked as boolean }))}
                  disabled={saving}
                />
                <Label htmlFor="shuffleQuestions">Shuffle questions</Label>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={saving}>
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={saving}>
            {saving ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
