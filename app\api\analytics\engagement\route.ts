import { NextRequest } from 'next/server'
import { z } from 'zod'
 import { createAPIHandler, APIResponse } from '@/lib/api-middleware'
import { AnalyticsService } from '@/lib/analytics-service'

const engagementQuerySchema = z.object({
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
  period: z.enum(['7d', '30d', '90d']).optional().default('30d')
})

// GET /api/analytics/engagement - Get user engagement metrics
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
     
    validateQuery: engagementQuerySchema
  },
  async (request: NextRequest, { validatedQuery }) => {
    const { startDate, endDate, period } = validatedQuery

    // Calculate time range
    let timeRange
    if (startDate && endDate) {
      timeRange = {
        start: new Date(startDate),
        end: new Date(endDate)
      }
    } else {
      const end = new Date()
      const start = new Date()
      
      switch (period) {
        case '7d':
          start.setDate(end.getDate() - 7)
          break
        case '30d':
          start.setDate(end.getDate() - 30)
          break
        case '90d':
          start.setDate(end.getDate() - 90)
          break
      }
      
      timeRange = { start, end }
    }

    const engagement = await AnalyticsService.getUserEngagementMetrics(timeRange)
    
    return APIResponse.success(
      {
        engagement,
        timeRange,
        period
      },
      'User engagement metrics retrieved successfully'
    )
  }
)
