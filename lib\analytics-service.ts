import { prisma } from '@/lib/prisma'

export interface AnalyticsTimeRange {
  start: Date
  end: Date
}

export interface PlatformOverview {
  totalUsers: number
  activeUsers: number
  totalQuizzes: number
  totalAttempts: number
  totalQuestions: number
  averageScore: number
  completionRate: number
  totalNotifications: number
  totalFiles: number
  totalApiKeys: number
}

export interface UserEngagementMetrics {
  dailyActiveUsers: Array<{ date: string; count: number }>
  weeklyActiveUsers: Array<{ week: string; count: number }>
  monthlyActiveUsers: Array<{ month: string; count: number }>
  userRetention: {
    day1: number
    day7: number
    day30: number
  }
  sessionDuration: {
    average: number
    median: number
    distribution: Array<{ range: string; count: number }>
  }
}

export interface QuizPerformanceMetrics {
  topPerformingQuizzes: Array<{
    id: string
    title: string
    attempts: number
    averageScore: number
    completionRate: number
    difficulty: string
    type: string
  }>
  quizDifficultyAnalysis: Array<{
    difficulty: string
    count: number
    averageScore: number
    completionRate: number
  }>
  questionAnalysis: Array<{
    questionId: string
    text: string
    correctRate: number
    averageTime: number
    difficulty: number
  }>
}

export interface SystemHealthMetrics {
  apiUsage: {
    totalRequests: number
    averageResponseTime: number
    errorRate: number
    topEndpoints: Array<{ endpoint: string; requests: number; avgTime: number }>
  }
  fileStorage: {
    totalFiles: number
    totalSize: number
    uploadTrends: Array<{ date: string; uploads: number; size: number }>
  }
  notifications: {
    totalSent: number
    deliveryRate: number
    readRate: number
    clickRate: number
    topCategories: Array<{ category: string; count: number; engagement: number }>
  }
}

export class AnalyticsService {
  /**
   * Get platform overview metrics
   */
  static async getPlatformOverview(timeRange?: AnalyticsTimeRange): Promise<PlatformOverview> {
    const whereClause = timeRange ? {
      createdAt: {
        gte: timeRange.start,
        lte: timeRange.end
      }
    } : {}

    const attemptWhereClause = timeRange ? {
      startedAt: {
        gte: timeRange.start,
        lte: timeRange.end
      }
    } : {}

    const [
      totalUsers,
      activeUsers,
      totalQuizzes,
      totalAttempts,
      totalQuestions,
      avgScoreResult,
      completionData,
      totalNotifications,
      totalFiles,
      totalApiKeys
    ] = await Promise.all([
      // Total users
      prisma.user.count(timeRange ? { where: whereClause } : undefined),
      
      // Active users (users with attempts in the time range)
      prisma.user.count({
        where: {
          quizAttempts: {
            some: timeRange ? attemptWhereClause : {}
          }
        }
      }),

      // Total quizzes
      prisma.quiz.count(timeRange ? { where: whereClause } : undefined),

      // Total attempts
      prisma.quizAttempt.count(timeRange ? { where: attemptWhereClause } : undefined),
      
      // Total questions
      prisma.question.count(),
      
      // Average score
      prisma.quizAttempt.aggregate({
        _avg: { percentage: true },
        where: timeRange ? attemptWhereClause : undefined
      }),

      // Completion rate
      prisma.quizAttempt.groupBy({
        by: ['isCompleted'],
        _count: { id: true },
        where: timeRange ? attemptWhereClause : undefined
      }),
      
      // Total notifications
      prisma.notification.count(timeRange ? { where: whereClause } : undefined),

      // Total files
      prisma.file.count(timeRange ? { where: whereClause } : undefined),

      // Total API keys
      prisma.apiKey.count(timeRange ? { where: whereClause } : undefined)
    ])

    const completedAttempts = completionData.find(item => item.isCompleted)?._count.id || 0
    const totalAttemptsForCompletion = completionData.reduce((sum, item) => sum + item._count.id, 0)
    const completionRate = totalAttemptsForCompletion > 0 ? (completedAttempts / totalAttemptsForCompletion) * 100 : 0

    return {
      totalUsers,
      activeUsers,
      totalQuizzes,
      totalAttempts,
      totalQuestions,
      averageScore: avgScoreResult._avg.percentage || 0,
      completionRate,
      totalNotifications,
      totalFiles,
      totalApiKeys
    }
  }

  /**
   * Get user engagement metrics
   */
  static async getUserEngagementMetrics(timeRange: AnalyticsTimeRange): Promise<UserEngagementMetrics> {
    // For now, use Prisma queries instead of raw SQL for better compatibility
    // Daily active users - get unique users per day
    const attempts = await prisma.quizAttempt.findMany({
      where: {
        startedAt: {
          gte: timeRange.start,
          lte: timeRange.end
        }
      },
      select: {
        userId: true,
        startedAt: true
      }
    })

    // Group by date
    const dailyActiveUsersMap = new Map<string, Set<string>>()
    attempts.forEach(attempt => {
      const date = attempt.startedAt.toISOString().split('T')[0]
      if (!dailyActiveUsersMap.has(date)) {
        dailyActiveUsersMap.set(date, new Set())
      }
      dailyActiveUsersMap.get(date)!.add(attempt.userId)
    })

    const dailyActiveUsers = Array.from(dailyActiveUsersMap.entries()).map(([date, users]) => ({
      date,
      count: users.size
    })).sort((a, b) => a.date.localeCompare(b.date))

    // Weekly active users - group by week
    const weeklyActiveUsersMap = new Map<string, Set<string>>()
    attempts.forEach(attempt => {
      const date = new Date(attempt.startedAt)
      const weekStart = new Date(date.getFullYear(), date.getMonth(), date.getDate() - date.getDay())
      const weekKey = weekStart.toISOString().split('T')[0]
      if (!weeklyActiveUsersMap.has(weekKey)) {
        weeklyActiveUsersMap.set(weekKey, new Set())
      }
      weeklyActiveUsersMap.get(weekKey)!.add(attempt.userId)
    })

    const weeklyActiveUsers = Array.from(weeklyActiveUsersMap.entries()).map(([week, users]) => ({
      week,
      count: users.size
    })).sort((a, b) => a.week.localeCompare(b.week))

    // User retention calculation
    const userRetention = await this.calculateUserRetention(timeRange)

    // Session duration analysis
    const sessionDuration = await this.calculateSessionDuration(timeRange)

    return {
      dailyActiveUsers: dailyActiveUsers.map(item => ({
        date: item.date,
        count: Number(item.count)
      })),
      weeklyActiveUsers: weeklyActiveUsers.map(item => ({
        week: item.week,
        count: Number(item.count)
      })),
      monthlyActiveUsers: [], // Implement if needed
      userRetention,
      sessionDuration
    }
  }

  /**
   * Get quiz performance metrics
   */
  static async getQuizPerformanceMetrics(timeRange?: AnalyticsTimeRange): Promise<QuizPerformanceMetrics> {
    const whereClause = timeRange ? {
      startedAt: {
        gte: timeRange.start,
        lte: timeRange.end
      }
    } : {}

    // Top performing quizzes
    const topPerformingQuizzes = await prisma.quiz.findMany({
      include: {
        attempts: {
          where: whereClause,
          select: {
            percentage: true,
            isCompleted: true
          }
        },
        _count: {
          select: {
            attempts: {
              where: whereClause
            }
          }
        }
      },
      take: 10
    })

    // Quiz difficulty analysis with performance metrics
    const quizDifficultyData = await prisma.quiz.findMany({
      select: {
        difficulty: true,
        attempts: {
          where: whereClause,
          select: {
            percentage: true,
            isCompleted: true
          }
        }
      }
    })

    // Group by difficulty and calculate metrics
    const difficultyMap = new Map<string, { count: number; totalScore: number; totalAttempts: number; completedAttempts: number }>()

    quizDifficultyData.forEach(quiz => {
      const difficulty = quiz.difficulty
      if (!difficultyMap.has(difficulty)) {
        difficultyMap.set(difficulty, { count: 0, totalScore: 0, totalAttempts: 0, completedAttempts: 0 })
      }

      const data = difficultyMap.get(difficulty)!
      data.count += 1
      data.totalAttempts += quiz.attempts.length
      data.completedAttempts += quiz.attempts.filter(a => a.isCompleted).length
      data.totalScore += quiz.attempts.reduce((sum, a) => sum + a.percentage, 0)
    })

    const quizDifficultyAnalysis = Array.from(difficultyMap.entries()).map(([difficulty, data]) => ({
      difficulty,
      count: data.count,
      averageScore: data.totalAttempts > 0 ? data.totalScore / data.totalAttempts : 0,
      completionRate: data.totalAttempts > 0 ? (data.completedAttempts / data.totalAttempts) * 100 : 0
    }))

    // Question analysis
    const questionAnalysis = await this.getQuestionAnalysis(timeRange)

    return {
      topPerformingQuizzes: topPerformingQuizzes.map(quiz => {
        const attempts = quiz.attempts
        const totalAttempts = attempts.length
        const completedAttempts = attempts.filter(a => a.isCompleted).length
        const averageScore = totalAttempts > 0 
          ? attempts.reduce((sum, a) => sum + a.percentage, 0) / totalAttempts 
          : 0
        const completionRate = totalAttempts > 0 ? (completedAttempts / totalAttempts) * 100 : 0

        return {
          id: quiz.id,
          title: quiz.title,
          attempts: totalAttempts,
          averageScore,
          completionRate,
          difficulty: quiz.difficulty,
          type: quiz.type
        }
      }).sort((a, b) => b.averageScore - a.averageScore),
      
      quizDifficultyAnalysis,
      
      questionAnalysis
    }
  }

  /**
   * Get system health metrics
   */
  static async getSystemHealthMetrics(timeRange: AnalyticsTimeRange): Promise<SystemHealthMetrics> {
    // API usage metrics
    const apiUsage = await this.getApiUsageMetrics(timeRange)
    
    // File storage metrics
    const fileStorage = await this.getFileStorageMetrics(timeRange)
    
    // Notification metrics
    const notifications = await this.getNotificationMetrics(timeRange)

    return {
      apiUsage,
      fileStorage,
      notifications
    }
  }

  /**
   * Get real-time analytics data
   */
  static async getRealTimeMetrics() {
    const now = new Date()
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000)
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000)

    const [
      activeUsersLastHour,
      attemptsLastHour,
      notificationsLastHour,
      apiRequestsLastHour,
      activeUsersToday,
      attemptsToday
    ] = await Promise.all([
      // Active users in last hour
      prisma.user.count({
        where: {
          quizAttempts: {
            some: {
              startedAt: { gte: oneHourAgo }
            }
          }
        }
      }),
      
      // Quiz attempts in last hour
      prisma.quizAttempt.count({
        where: { startedAt: { gte: oneHourAgo } }
      }),
      
      // Notifications sent in last hour
      prisma.notification.count({
        where: { createdAt: { gte: oneHourAgo } }
      }),
      
      // API requests in last hour
      prisma.apiUsage.count({
        where: { createdAt: { gte: oneHourAgo } }
      }),
      
      // Active users today
      prisma.user.count({
        where: {
          quizAttempts: {
            some: {
              startedAt: { gte: oneDayAgo }
            }
          }
        }
      }),
      
      // Quiz attempts today
      prisma.quizAttempt.count({
        where: { startedAt: { gte: oneDayAgo } }
      })
    ])

    return {
      lastHour: {
        activeUsers: activeUsersLastHour,
        quizAttempts: attemptsLastHour,
        notifications: notificationsLastHour,
        apiRequests: apiRequestsLastHour
      },
      today: {
        activeUsers: activeUsersToday,
        quizAttempts: attemptsToday
      },
      timestamp: now
    }
  }

  /**
   * Calculate user retention rates
   */
  private static async calculateUserRetention(timeRange: AnalyticsTimeRange) {
    // This is a simplified retention calculation
    // In production, you'd want more sophisticated cohort analysis
    
    const totalUsers = await prisma.user.count({
      where: {
        createdAt: {
          gte: timeRange.start,
          lte: timeRange.end
        }
      }
    })

    // Users who returned after 1, 7, and 30 days
    const [day1Retention, day7Retention, day30Retention] = await Promise.all([
      // 1-day retention
      prisma.user.count({
        where: {
          createdAt: {
            gte: timeRange.start,
            lte: timeRange.end
          },
          quizAttempts: {
            some: {
              startedAt: {
                gte: new Date(timeRange.start.getTime() + 24 * 60 * 60 * 1000)
              }
            }
          }
        }
      }),
      // 7-day retention
      prisma.user.count({
        where: {
          createdAt: {
            gte: timeRange.start,
            lte: timeRange.end
          },
          quizAttempts: {
            some: {
              startedAt: {
                gte: new Date(timeRange.start.getTime() + 7 * 24 * 60 * 60 * 1000)
              }
            }
          }
        }
      }),
      // 30-day retention
      prisma.user.count({
        where: {
          createdAt: {
            gte: timeRange.start,
            lte: timeRange.end
          },
          quizAttempts: {
            some: {
              startedAt: {
                gte: new Date(timeRange.start.getTime() + 30 * 24 * 60 * 60 * 1000)
              }
            }
          }
        }
      })
    ])

    return {
      day1: totalUsers > 0 ? (day1Retention / totalUsers) * 100 : 0,
      day7: totalUsers > 0 ? (day7Retention / totalUsers) * 100 : 0,
      day30: totalUsers > 0 ? (day30Retention / totalUsers) * 100 : 0
    }
  }

  /**
   * Calculate session duration metrics
   */
  private static async calculateSessionDuration(timeRange: AnalyticsTimeRange) {
    const attempts = await prisma.quizAttempt.findMany({
      where: {
        startedAt: {
          gte: timeRange.start,
          lte: timeRange.end
        },
        timeSpent: { not: null }
      },
      select: { timeSpent: true }
    })

    const durations = attempts.map(a => a.timeSpent!).filter(d => d > 0)
    const average = durations.length > 0 ? durations.reduce((sum, d) => sum + d, 0) / durations.length : 0
    const median = durations.length > 0 ? durations.sort()[Math.floor(durations.length / 2)] : 0

    return {
      average,
      median,
      distribution: [] // Implement duration distribution
    }
  }

  /**
   * Get question analysis
   */
  private static async getQuestionAnalysis(_timeRange?: AnalyticsTimeRange) {
    // This would require analyzing quiz attempt answers
    // For now, return empty array
    return []
  }

  /**
   * Get API usage metrics
   */
  private static async getApiUsageMetrics(timeRange: AnalyticsTimeRange) {
    const [totalRequests, avgResponseTime, errorCount, topEndpoints] = await Promise.all([
      prisma.apiUsage.count({
        where: {
          createdAt: {
            gte: timeRange.start,
            lte: timeRange.end
          }
        }
      }),
      
      prisma.apiUsage.aggregate({
        _avg: { responseTime: true },
        where: {
          createdAt: {
            gte: timeRange.start,
            lte: timeRange.end
          }
        }
      }),
      
      prisma.apiUsage.count({
        where: {
          createdAt: {
            gte: timeRange.start,
            lte: timeRange.end
          },
          statusCode: { gte: 400 }
        }
      }),
      
      prisma.apiUsage.groupBy({
        by: ['endpoint'],
        _count: { id: true },
        _avg: { responseTime: true },
        where: {
          createdAt: {
            gte: timeRange.start,
            lte: timeRange.end
          }
        },
        orderBy: { _count: { id: 'desc' } },
        take: 10
      })
    ])

    const errorRate = totalRequests > 0 ? (errorCount / totalRequests) * 100 : 0

    return {
      totalRequests,
      averageResponseTime: avgResponseTime._avg.responseTime || 0,
      errorRate,
      topEndpoints: topEndpoints.map(endpoint => ({
        endpoint: endpoint.endpoint,
        requests: endpoint._count.id,
        avgTime: Math.round(endpoint._avg.responseTime || 0)
      }))
    }
  }

  /**
   * Get file storage metrics
   */
  private static async getFileStorageMetrics(timeRange: AnalyticsTimeRange) {
    const [totalFiles, totalSizeResult, uploadTrends] = await Promise.all([
      prisma.file.count({
        where: {
          createdAt: {
            gte: timeRange.start,
            lte: timeRange.end
          }
        }
      }),
      
      prisma.file.aggregate({
        _sum: { size: true },
        where: {
          createdAt: {
            gte: timeRange.start,
            lte: timeRange.end
          }
        }
      }),
      
      // Get upload trends using Prisma queries for better compatibility
      prisma.file.findMany({
        where: {
          createdAt: {
            gte: timeRange.start,
            lte: timeRange.end
          }
        },
        select: {
          createdAt: true,
          size: true
        }
      }).then(files => {
        const trendsMap = new Map<string, { uploads: number; size: number }>()
        files.forEach(file => {
          const date = file.createdAt.toISOString().split('T')[0]
          if (!trendsMap.has(date)) {
            trendsMap.set(date, { uploads: 0, size: 0 })
          }
          const trend = trendsMap.get(date)!
          trend.uploads++
          trend.size += file.size
        })
        return Array.from(trendsMap.entries()).map(([date, data]) => ({
          date,
          uploads: BigInt(data.uploads),
          size: BigInt(data.size)
        })).sort((a, b) => a.date.localeCompare(b.date))
      })
    ])

    return {
      totalFiles,
      totalSize: totalSizeResult._sum.size || 0,
      uploadTrends: uploadTrends.map(trend => ({
        date: trend.date,
        uploads: Number(trend.uploads),
        size: Number(trend.size)
      }))
    }
  }

  /**
   * Get notification metrics
   */
  private static async getNotificationMetrics(timeRange: AnalyticsTimeRange) {
    const [totalSent, deliveryData, readData, clickData, topCategories] = await Promise.all([
      prisma.notification.count({
        where: {
          createdAt: {
            gte: timeRange.start,
            lte: timeRange.end
          }
        }
      }),
      
      prisma.userNotification.count({
        where: {
          createdAt: {
            gte: timeRange.start,
            lte: timeRange.end
          },
          deliveredAt: { not: null }
        }
      }),
      
      prisma.userNotification.count({
        where: {
          createdAt: {
            gte: timeRange.start,
            lte: timeRange.end
          },
          isRead: true
        }
      }),
      
      prisma.userNotification.count({
        where: {
          createdAt: {
            gte: timeRange.start,
            lte: timeRange.end
          },
          isClicked: true
        }
      }),
      
      prisma.notification.groupBy({
        by: ['category'],
        _count: { id: true },
        where: {
          createdAt: {
            gte: timeRange.start,
            lte: timeRange.end
          },
          category: { not: null }
        },
        orderBy: { _count: { id: 'desc' } },
        take: 10
      })
    ])

    const totalUserNotifications = await prisma.userNotification.count({
      where: {
        createdAt: {
          gte: timeRange.start,
          lte: timeRange.end
        }
      }
    })

    const deliveryRate = totalUserNotifications > 0 ? (deliveryData / totalUserNotifications) * 100 : 0
    const readRate = totalUserNotifications > 0 ? (readData / totalUserNotifications) * 100 : 0
    const clickRate = totalUserNotifications > 0 ? (clickData / totalUserNotifications) * 100 : 0

    return {
      totalSent,
      deliveryRate,
      readRate,
      clickRate,
      topCategories: topCategories.map(category => ({
        category: category.category || 'uncategorized',
        count: category._count.id,
        engagement: 0 // Calculate engagement rate
      }))
    }
  }
}
