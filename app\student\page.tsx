"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { AnimatedCard, StaggeredCards } from "@/components/ui/animated-card"
import { AnimatedButton } from "@/components/ui/animated-button"
import { FloatingBackground, GradientOrb } from "@/components/ui/floating-shapes"
import { useIntersectionObserver } from "@/hooks/use-intersection-observer"
import { useCounterAnimation } from "@/hooks/use-counter-animation"

import {
  BookOpen,
  Trophy,
  Clock,
  Target,
  TrendingUp,
  Calendar,
  Play,
  Star,
  Award,
  Users,
  ChevronRight,
  Zap,
  Brain,
  CheckCircle,
  XCircle,
  RotateCcw,
  Sparkles,
  TrendingDown,
  Activity,
  BarChart3
} from "lucide-react"
import Link from "next/link"
import { motion, AnimatePresence } from "framer-motion"
import { toast } from "@/lib/toast-utils"
import { StudentError<PERSON><PERSON><PERSON> } from "@/lib/student-error-handler"

interface StudentStats {
  totalQuizzes: number
  completedQuizzes: number
  averageScore: number
  totalPoints: number
  currentStreak: number
  rank: number
  totalStudents: number
  hoursSpent: number
}

interface RecentQuiz {
  id: string
  title: string
  type: string
  score: number
  maxScore: number
  completedAt: string
  difficulty: string
  timeSpent: number
}

interface UpcomingQuiz {
  id: string
  title: string
  type: string
  difficulty: string
  startTime: string
  duration: number
  questionsCount: number
  isEnrolled: boolean
}

interface Achievement {
  id: string
  title: string
  description: string
  icon: string
  unlockedAt: string
  rarity: 'common' | 'rare' | 'epic' | 'legendary'
}

export default function StudentDashboard() {
  // All hooks must be called at the top level, before any conditional logic
  const { elementRef, isVisible } = useIntersectionObserver({
    threshold: 0.1,
    freezeOnceVisible: true,
  });

  const [stats, setStats] = useState<StudentStats>({
    totalQuizzes: 0,
    completedQuizzes: 0,
    averageScore: 0,
    totalPoints: 0,
    currentStreak: 0,
    rank: 0,
    totalStudents: 0,
    hoursSpent: 0
  })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const [recentQuizzes, setRecentQuizzes] = useState<RecentQuiz[]>([])

  const [upcomingQuizzes, setUpcomingQuizzes] = useState<UpcomingQuiz[]>([])

  const [recentAchievements, setRecentAchievements] = useState<Achievement[]>([])

  // Fetch dashboard data
  const fetchDashboardData = async () => {
    setLoading(true)
    setError(null)

    try {
      const data = await StudentErrorHandler.fetchWithErrorHandling(
        '/api/student/dashboard',
        {},
        'Fetching dashboard data'
      )

      if (data.success) {
        setStats(data.data.stats)
        setRecentQuizzes(data.data.recentAttempts || [])
        setUpcomingQuizzes(data.data.enrolledQuizzes || [])
        setRecentAchievements(data.data.achievements || [])
      } else {
        throw new Error(data.message || 'Failed to fetch dashboard data')
      }
    } catch (error) {
      const errorMessage = StudentErrorHandler.handleError(
        error as Error,
        'Fetching dashboard data',
        {
          showToast: true,
          retryCallback: () => fetchDashboardData(),
          fallbackMessage: 'Failed to load dashboard data'
        }
      )
      setError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'EASY':
        return 'text-green-600 bg-green-50 border-green-200 dark:text-green-400 dark:bg-green-900/20 dark:border-green-800'
      case 'MEDIUM':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200 dark:text-yellow-400 dark:bg-yellow-900/20 dark:border-yellow-800'
      case 'HARD':
        return 'text-red-600 bg-red-50 border-red-200 dark:text-red-400 dark:bg-red-900/20 dark:border-red-800'
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200 dark:text-gray-400 dark:bg-gray-900/20 dark:border-gray-800'
    }
  }

  const getScoreColor = (score: number, maxScore: number) => {
    const percentage = (score / maxScore) * 100
    if (percentage >= 90) return 'text-green-600'
    if (percentage >= 70) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common':
        return 'border-gray-300 bg-gray-50'
      case 'rare':
        return 'border-blue-300 bg-blue-50'
      case 'epic':
        return 'border-purple-300 bg-purple-50'
      case 'legendary':
        return 'border-yellow-300 bg-yellow-50'
      default:
        return 'border-gray-300 bg-gray-50'
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-violet-50 via-white to-electric-50 dark:from-gray-900 dark:via-gray-800 dark:to-violet-900 relative overflow-hidden">
        {/* Background Elements */}
        <FloatingBackground density="low" colors={['violet', 'blue', 'cyan']} />

        <div className="relative z-10 container mx-auto p-6">
          <div className="flex items-center justify-center min-h-[400px]">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
              className="text-center"
            >
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                className="w-16 h-16 mx-auto mb-6"
              >
                <div className="w-full h-full rounded-full border-4 border-violet-200 dark:border-violet-800 border-t-violet-600 dark:border-t-violet-400"></div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className="space-y-2"
              >
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                  Loading Dashboard
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Preparing your learning experience...
                </p>
              </motion.div>
            </motion.div>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-violet-50 via-white to-electric-50 dark:from-gray-900 dark:via-gray-800 dark:to-violet-900 relative overflow-hidden">
        {/* Background Elements */}
        <FloatingBackground density="low" colors={['violet', 'blue', 'cyan']} />
        <GradientOrb
          className="top-20 right-20"
          size={200}
          colors={['#8b5cf6', '#3b82f6']}
        />

        <div className="relative z-10 container mx-auto p-6">
          <div className="flex items-center justify-center min-h-[400px]">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
            >
              <AnimatedCard variant="glass" className="max-w-md text-center">
                <CardContent className="pt-8 pb-8">
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ duration: 0.5, delay: 0.2 }}
                    className="mb-6"
                  >
                    <div className="w-16 h-16 mx-auto rounded-full bg-gradient-to-br from-red-500 to-pink-600 flex items-center justify-center mb-4">
                      <XCircle className="h-8 w-8 text-white" />
                    </div>
                  </motion.div>

                  <motion.h3
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.3 }}
                    className="text-xl font-semibold text-gray-900 dark:text-white mb-3"
                  >
                    Failed to Load Dashboard
                  </motion.h3>

                  <motion.p
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.4 }}
                    className="text-gray-600 dark:text-gray-400 mb-6"
                  >
                    {error}
                  </motion.p>

                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.5 }}
                  >
                    <AnimatedButton
                      onClick={fetchDashboardData}
                      variant="primary"
                      size="lg"
                      className="group"
                    >
                      <RotateCcw className="h-4 w-4 mr-2 group-hover:rotate-180 transition-transform duration-500" />
                      Try Again
                    </AnimatedButton>
                  </motion.div>
                </CardContent>
              </AnimatedCard>
            </motion.div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-violet-50 via-white to-electric-50 dark:from-gray-900 dark:via-gray-800 dark:to-violet-900 relative overflow-hidden">
      {/* Background Elements */}
      <FloatingBackground density="medium" colors={['violet', 'blue', 'cyan', 'pink']} />
      <GradientOrb
        className="top-20 left-20"
        size={300}
        colors={['#8b5cf6', '#3b82f6']}
      />
      <GradientOrb
        className="bottom-20 right-20"
        size={250}
        colors={['#06b6d4', '#f472b6']}
      />

      <div ref={elementRef as any} className="relative z-10 p-6 space-y-8">
        {/* Welcome Header - Sidebar Style */}
        <motion.div
          initial={{ opacity: 0, x: -30 }}
          animate={isVisible ? { opacity: 1, x: 0 } : {}}
          transition={{ duration: 0.8, ease: 'easeOut' }}
          className="flex items-center justify-between mb-8"
        >
          <div className="flex items-center gap-6">
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={isVisible ? { opacity: 1, scale: 1 } : {}}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="w-16 h-16 rounded-2xl bg-gradient-primary flex items-center justify-center shadow-glow"
            >
              <Brain className="w-8 h-8 text-white" />
            </motion.div>

            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                Good morning, Student!
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-1">
                Let's make today productive
              </p>
            </div>
          </div>

          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={isVisible ? { opacity: 1, scale: 1 } : {}}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="flex items-center gap-3"
          >
            <div className="text-right">
              <div className="text-sm text-gray-500 dark:text-gray-400">Current Streak</div>
              <div className="text-2xl font-bold text-gradient bg-gradient-accent">
                {stats.currentStreak} days
              </div>
            </div>
            <div className="w-12 h-12 rounded-xl bg-gradient-accent flex items-center justify-center">
              <Zap className="w-6 h-6 text-white" />
            </div>
          </motion.div>
        </motion.div>

        {/* Performance Overview - Horizontal Layout */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isVisible ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.3 }}
          className="glass rounded-3xl p-8 mb-8"
        >
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Performance Overview</h2>
            <div className="flex items-center gap-2 px-3 py-1 rounded-full bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400">
              <TrendingUp className="w-4 h-4" />
              <span className="text-sm font-medium">Improving</span>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Quizzes Progress */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={isVisible ? { opacity: 1, x: 0 } : {}}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="space-y-4"
            >
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-blue-500 to-cyan-600 flex items-center justify-center">
                  <BookOpen className="w-5 h-5 text-white" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-gray-900 dark:text-white">
                    {useCounterAnimation({ end: stats.completedQuizzes, isVisible, duration: 2000 })}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Quizzes Completed</div>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">Progress</span>
                  <span className="text-gray-900 dark:text-white font-medium">
                    {Math.round((stats.completedQuizzes / Math.max(stats.totalQuizzes, 1)) * 100)}%
                  </span>
                </div>
                <div className="relative h-3 w-full overflow-hidden rounded-full bg-gray-200 dark:bg-gray-700">
                  <motion.div
                    className="h-full bg-gradient-to-r from-blue-500 to-cyan-600 rounded-full"
                    initial={{ width: 0 }}
                    animate={isVisible ? {
                      width: `${Math.min((stats.completedQuizzes / Math.max(stats.totalQuizzes, 1)) * 100, 100)}%`
                    } : {}}
                    transition={{ duration: 1.5, delay: 0.8 }}
                  />
                </div>
              </div>
            </motion.div>

            {/* Average Score */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={isVisible ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.6, delay: 0.5 }}
              className="space-y-4"
            >
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-green-500 to-emerald-600 flex items-center justify-center">
                  <Target className="w-5 h-5 text-white" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-gray-900 dark:text-white">
                    {useCounterAnimation({ end: stats.averageScore, suffix: '%', isVisible, duration: 2000 })}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Average Score</div>
                </div>
              </div>

              <div className="flex items-center gap-2 text-green-600 dark:text-green-400">
                <TrendingUp className="h-4 w-4" />
                <span className="text-sm">+5.2% from last month</span>
              </div>
            </motion.div>

            {/* Rank & Leaderboard */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={isVisible ? { opacity: 1, x: 0 } : {}}
              transition={{ duration: 0.6, delay: 0.6 }}
              className="space-y-4"
            >
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-purple-500 to-violet-600 flex items-center justify-center">
                  <Trophy className="w-5 h-5 text-white" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-gray-900 dark:text-white">
                    #{useCounterAnimation({ end: stats.rank, isVisible, duration: 2000 })}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Global Rank</div>
                </div>
              </div>

              <div className="text-xs text-gray-500 dark:text-gray-400">
                Out of {stats.totalStudents.toLocaleString()} students
              </div>
            </motion.div>
          </div>
        </motion.div>

        {/* Dashboard Widgets Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
          {/* Quick Actions Widget */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            animate={isVisible ? { opacity: 1, x: 0 } : {}}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="lg:col-span-4"
          >
            <div className="glass rounded-2xl p-6 h-full">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Quick Actions</h3>
              <div className="space-y-3">
                <Link href="/student/browse">
                  <AnimatedButton
                    variant="primary"
                    size="lg"
                    className="w-full justify-start group"
                  >
                    <Play className="w-5 h-5 mr-3 group-hover:scale-110 transition-transform" />
                    Start New Quiz
                  </AnimatedButton>
                </Link>

                <Link href="/student/history">
                  <AnimatedButton
                    variant="outline"
                    size="md"
                    className="w-full justify-start group"
                  >
                    <BarChart3 className="w-4 h-4 mr-3 group-hover:scale-110 transition-transform" />
                    View Analytics
                  </AnimatedButton>
                </Link>

                <Link href="/student/profile">
                  <AnimatedButton
                    variant="outline"
                    size="md"
                    className="w-full justify-start group"
                  >
                    <Users className="w-4 h-4 mr-3 group-hover:scale-110 transition-transform" />
                    Update Profile
                  </AnimatedButton>
                </Link>
              </div>
            </div>
          </motion.div>

          {/* Recent Activity Widget */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={isVisible ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8, delay: 0.7 }}
            className="lg:col-span-8"
          >
            <div className="glass rounded-2xl p-6 h-full">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
                  <Activity className="w-5 h-5 text-violet-600 dark:text-violet-400" />
                  Recent Activity
                </h3>
                <Link href="/student/history" className="group text-violet-600 dark:text-violet-400">
                  <AnimatedButton variant="ghost" size="sm">
                    View All
                    <ChevronRight className="h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform" />
                  </AnimatedButton>
                </Link>
              </div>

              <div className="space-y-3 max-h-80 overflow-y-auto">
                {recentQuizzes.map((quiz, index) => (
                  <motion.div
                    key={quiz.id}
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: 0.1 * index }}
                    className="flex items-center justify-between p-4 rounded-xl bg-white/50 dark:bg-black/20 border border-white/30 dark:border-white/10 hover:bg-white/70 dark:hover:bg-black/30 transition-all duration-300 group"
                  >
                    <div className="flex items-center gap-4 flex-1">
                      <div className={`w-3 h-3 rounded-full ${
                        quiz.score / quiz.maxScore >= 0.9 ? 'bg-green-500' :
                        quiz.score / quiz.maxScore >= 0.7 ? 'bg-yellow-500' : 'bg-red-500'
                      }`} />

                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900 dark:text-white truncate group-hover:text-violet-600 dark:group-hover:text-violet-400 transition-colors">
                          {quiz.title}
                        </h4>
                        <div className="flex items-center gap-2 mt-1">
                          <span className="text-xs text-gray-500 dark:text-gray-400">
                            {new Date(quiz.completedAt).toLocaleDateString()}
                          </span>
                          <span className="text-xs text-gray-400">•</span>
                          <span className="text-xs text-gray-500 dark:text-gray-400">
                            {quiz.timeSpent} min
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="text-right">
                      <div className={`text-lg font-bold ${getScoreColor(quiz.score, quiz.maxScore)}`}>
                        {Math.round((quiz.score / quiz.maxScore) * 100)}%
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>

          {/* Upcoming Quizzes Widget */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={isVisible ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="lg:col-span-6"
          >
            <div className="glass rounded-2xl p-6 h-full">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
                  <Calendar className="w-5 h-5 text-violet-600 dark:text-violet-400" />
                  Upcoming Quizzes
                </h3>
                <AnimatedButton variant="ghost" size="sm" asChild>
                  <Link href="/student/browse" className="group text-violet-600 dark:text-violet-400">
                    Browse All
                    <ChevronRight className="h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform" />
                  </Link>
                </AnimatedButton>
              </div>

              <div className="space-y-3">
                <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                  <Calendar className="w-12 h-12 mx-auto mb-3 opacity-50" />
                  <p>No upcoming scheduled quizzes</p>
                  <p className="text-sm mt-1">Browse available quizzes to get started</p>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Study Streak Widget */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={isVisible ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8, delay: 0.9 }}
            className="lg:col-span-6"
          >
            <div className="glass rounded-2xl p-6 h-full">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
                  <Zap className="w-5 h-5 text-orange-500" />
                  Study Streak
                </h3>
              </div>

              <div className="text-center">
                <div className="text-4xl font-bold text-gradient bg-gradient-accent mb-2">
                  {stats.currentStreak}
                </div>
                <div className="text-gray-600 dark:text-gray-400 mb-4">Days in a row</div>

                <div className="flex justify-center gap-1 mb-4">
                  {[...Array(7)].map((_, i) => (
                    <div
                      key={i}
                      className={`w-8 h-8 rounded-lg flex items-center justify-center text-xs font-medium ${
                        i < stats.currentStreak
                          ? 'bg-gradient-accent text-white'
                          : 'bg-gray-200 dark:bg-gray-700 text-gray-400'
                      }`}
                    >
                      {i + 1}
                    </div>
                  ))}
                </div>

                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Keep going! You're doing great 🔥
                </p>
              </div>
            </div>
          </motion.div>
      </div>
    </div>
    </div>
  )
  }

