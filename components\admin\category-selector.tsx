"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Plus, Loader2 } from "lucide-react"
import { toast } from "@/lib/toast-utils"

interface Subject {
  id: string
  name: string
  description?: string
  chapters: Chapter[]
}

interface Chapter {
  id: string
  name: string
  description?: string
  subjectId: string
  topics: Topic[]
}

interface Topic {
  id: string
  name: string
  description?: string
  chapterId: string
}

interface CategorySelectorProps {
  selectedSubjectId?: string
  selectedChapterId?: string
  selectedTopicId?: string
  onSelectionChange: (selection: {
    subjectId?: string
    chapterId?: string
    topicId?: string
  }) => void
  className?: string
}

export function CategorySelector({
  selectedSubjectId,
  selectedChapterId,
  selectedTopicId,
  onSelectionChange,
  className
}: CategorySelectorProps) {
  const [subjects, setSubjects] = useState<Subject[]>([])
  const [loading, setLoading] = useState(true)
  const [showAddSubject, setShowAddSubject] = useState(false)
  const [showAddChapter, setShowAddChapter] = useState(false)
  const [showAddTopic, setShowAddTopic] = useState(false)
  const [newSubjectName, setNewSubjectName] = useState("")
  const [newChapterName, setNewChapterName] = useState("")
  const [newTopicName, setNewTopicName] = useState("")
  const [creating, setCreating] = useState(false)

  useEffect(() => {
    fetchSubjects()
  }, [])

  const fetchSubjects = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/admin/categories/subjects')
      
      if (!response.ok) {
        throw new Error('Failed to fetch subjects')
      }
      
      const data = await response.json()
      setSubjects(data.subjects || [])
    } catch (error) {
      console.error('Error fetching subjects:', error)
      toast.error('Failed to load subjects')
    } finally {
      setLoading(false)
    }
  }

  const createSubject = async () => {
    if (!newSubjectName.trim()) {
      toast.error('Subject name is required')
      return
    }

    try {
      setCreating(true)
      const response = await fetch('/api/admin/categories/subjects', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: newSubjectName.trim() })
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to create subject')
      }

      const data = await response.json()
      setSubjects(prev => [...prev, data.subject])
      setNewSubjectName("")
      setShowAddSubject(false)
      toast.success('Subject created successfully')
      
      // Auto-select the new subject
      onSelectionChange({ subjectId: data.subject.id })
    } catch (error) {
      console.error('Error creating subject:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to create subject')
    } finally {
      setCreating(false)
    }
  }

  const createChapter = async () => {
    if (!newChapterName.trim() || !selectedSubjectId) {
      toast.error('Chapter name and subject selection are required')
      return
    }

    try {
      setCreating(true)
      const response = await fetch('/api/admin/categories/chapters', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          name: newChapterName.trim(),
          subjectId: selectedSubjectId
        })
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to create chapter')
      }

      const data = await response.json()
      
      // Update subjects with new chapter
      setSubjects(prev => prev.map(subject => 
        subject.id === selectedSubjectId 
          ? { ...subject, chapters: [...subject.chapters, data.chapter] }
          : subject
      ))
      
      setNewChapterName("")
      setShowAddChapter(false)
      toast.success('Chapter created successfully')
      
      // Auto-select the new chapter
      onSelectionChange({ 
        subjectId: selectedSubjectId,
        chapterId: data.chapter.id
      })
    } catch (error) {
      console.error('Error creating chapter:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to create chapter')
    } finally {
      setCreating(false)
    }
  }

  const createTopic = async () => {
    if (!newTopicName.trim() || !selectedChapterId) {
      toast.error('Topic name and chapter selection are required')
      return
    }

    try {
      setCreating(true)
      const response = await fetch('/api/admin/categories/topics', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          name: newTopicName.trim(),
          chapterId: selectedChapterId
        })
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to create topic')
      }

      const data = await response.json()
      
      // Update subjects with new topic
      setSubjects(prev => prev.map(subject => ({
        ...subject,
        chapters: subject.chapters.map(chapter =>
          chapter.id === selectedChapterId
            ? { ...chapter, topics: [...chapter.topics, data.topic] }
            : chapter
        )
      })))
      
      setNewTopicName("")
      setShowAddTopic(false)
      toast.success('Topic created successfully')
      
      // Auto-select the new topic
      onSelectionChange({ 
        subjectId: selectedSubjectId,
        chapterId: selectedChapterId,
        topicId: data.topic.id
      })
    } catch (error) {
      console.error('Error creating topic:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to create topic')
    } finally {
      setCreating(false)
    }
  }

  const selectedSubject = subjects.find(s => s.id === selectedSubjectId)
  const selectedChapter = selectedSubject?.chapters.find(c => c.id === selectedChapterId)

  if (loading) {
    return (
      <div className={`space-y-4 ${className}`}>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-20 mb-2"></div>
          <div className="h-10 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Subject Selection */}
      <div className="space-y-2">
        <Label>Subject</Label>
        <div className="flex gap-2">
          <Select
            value={selectedSubjectId || ""}
            onValueChange={(value) => {
              onSelectionChange({ 
                subjectId: value,
                chapterId: undefined,
                topicId: undefined
              })
            }}
          >
            <SelectTrigger className="flex-1">
              <SelectValue placeholder="Select subject" />
            </SelectTrigger>
            <SelectContent>
              {subjects.map((subject) => (
                <SelectItem key={subject.id} value={subject.id}>
                  {subject.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <Dialog open={showAddSubject} onOpenChange={setShowAddSubject}>
            <DialogTrigger asChild>
              <Button variant="outline" size="icon">
                <Plus className="h-4 w-4" />
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add New Subject</DialogTitle>
                <DialogDescription>
                  Create a new subject category for organizing quizzes.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="subject-name">Subject Name</Label>
                  <Input
                    id="subject-name"
                    value={newSubjectName}
                    onChange={(e) => setNewSubjectName(e.target.value)}
                    placeholder="Enter subject name"
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setShowAddSubject(false)}>
                  Cancel
                </Button>
                <Button onClick={createSubject} disabled={creating}>
                  {creating && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                  Create Subject
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Chapter Selection */}
      {selectedSubjectId && (
        <div className="space-y-2">
          <Label>Chapter</Label>
          <div className="flex gap-2">
            <Select
              value={selectedChapterId}
              onValueChange={(value) => {
                onSelectionChange({
                  subjectId: selectedSubjectId,
                  chapterId: value,
                  topicId: undefined
                })
              }}
            >
              <SelectTrigger className="flex-1">
                <SelectValue placeholder="Select chapter" />
              </SelectTrigger>
              <SelectContent>
                {selectedSubject?.chapters.map((chapter) => (
                  <SelectItem key={chapter.id} value={chapter.id}>
                    {chapter.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <Dialog open={showAddChapter} onOpenChange={setShowAddChapter}>
              <DialogTrigger asChild>
                <Button variant="outline" size="icon">
                  <Plus className="h-4 w-4" />
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Add New Chapter</DialogTitle>
                  <DialogDescription>
                    Create a new chapter under "{selectedSubject?.name}".
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="chapter-name">Chapter Name</Label>
                    <Input
                      id="chapter-name"
                      value={newChapterName}
                      onChange={(e) => setNewChapterName(e.target.value)}
                      placeholder="Enter chapter name"
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setShowAddChapter(false)}>
                    Cancel
                  </Button>
                  <Button onClick={createChapter} disabled={creating}>
                    {creating && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                    Create Chapter
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>
      )}

      {/* Topic Selection */}
      {selectedChapterId && (
        <div className="space-y-2">
          <Label>Topic</Label>
          <div className="flex gap-2">
            <Select
              value={selectedTopicId}
              onValueChange={(value) => {
                onSelectionChange({
                  subjectId: selectedSubjectId,
                  chapterId: selectedChapterId,
                  topicId: value
                })
              }}
            >
              <SelectTrigger className="flex-1">
                <SelectValue placeholder="Select topic" />
              </SelectTrigger>
              <SelectContent>
                {selectedChapter?.topics.map((topic) => (
                  <SelectItem key={topic.id} value={topic.id}>
                    {topic.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <Dialog open={showAddTopic} onOpenChange={setShowAddTopic}>
              <DialogTrigger asChild>
                <Button variant="outline" size="icon">
                  <Plus className="h-4 w-4" />
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Add New Topic</DialogTitle>
                  <DialogDescription>
                    Create a new topic under "{selectedChapter?.name}".
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="topic-name">Topic Name</Label>
                    <Input
                      id="topic-name"
                      value={newTopicName}
                      onChange={(e) => setNewTopicName(e.target.value)}
                      placeholder="Enter topic name"
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setShowAddTopic(false)}>
                    Cancel
                  </Button>
                  <Button onClick={createTopic} disabled={creating}>
                    {creating && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                    Create Topic
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>
      )}
    </div>
  )
}
