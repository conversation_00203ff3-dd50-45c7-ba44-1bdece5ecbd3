// Preview data fetcher for PDF exports
export class PreviewDataFetcher {
  
  // Fetch quiz result data for preview
  static async fetchQuizResultData(quizId: string, userId: string) {
    try {
      // Get quiz attempts for the specific quiz and user
      const attemptsResponse = await fetch(
        `/api/admin/quiz-attempts?quizId=${quizId}&userId=${userId}&status=completed&includeQuestions=true&limit=1`
      )
      
      if (!attemptsResponse.ok) {
        const errorData = await attemptsResponse.json().catch(() => null)
        const errorMessage = errorData?.error?.message || `HTTP ${attemptsResponse.status}: ${attemptsResponse.statusText}`
        throw new Error(`Failed to fetch quiz attempts: ${errorMessage}`)
      }
      
      const attemptsData = await attemptsResponse.json()
      const attempts = attemptsData.data?.attempts || []
      
      if (attempts.length === 0) {
        throw new Error('No completed quiz attempts found for this student and quiz')
      }
      
      const attempt = attempts[0] // Get the most recent attempt
      
      // Transform to enhanced PDF generator format (QuizAttemptData)
      const quizAttemptData = {
        id: attempt.id,
        quiz: {
          id: attempt.quiz.id,
          title: attempt.quiz.title,
          description: attempt.quiz.description || '',
          questions: attempt.quiz.questions?.map((q: any) => ({
            id: q.id,
            text: q.text,
            type: q.type as 'MCQ' | 'TRUE_FALSE' | 'SHORT_ANSWER' | 'ESSAY',
            options: q.options || undefined,
            correctAnswer: q.correctAnswer,
            explanation: q.explanation || undefined,
            points: q.points,
            order: q.order
          })) || [],
          metadata: {
            totalPoints: attempt.totalPoints,
            estimatedDuration: attempt.quiz.timeLimit || 0,
            difficulty: attempt.quiz.difficulty || undefined,
            tags: attempt.quiz.tags || undefined
          },
          createdAt: attempt.quiz.createdAt,
          updatedAt: attempt.quiz.updatedAt
        },
        student: {
          id: attempt.user.id,
          name: attempt.user.name || 'Unknown Student',
          email: attempt.user.email || undefined
        },
        answers: attempt.answers as Record<string, string>,
        score: attempt.score,
        percentage: attempt.percentage,
        timeSpent: attempt.timeSpent || 0, // Keep in seconds for enhanced generator
        startedAt: attempt.startedAt,
        completedAt: attempt.completedAt || new Date().toISOString(),
        isCompleted: attempt.isCompleted
      }

      return quizAttemptData
      
    } catch (error) {
      console.error('Error fetching quiz result data:', error)
      throw error
    }
  }
  
  // Fetch analytics data for preview
  static async fetchAnalyticsData(userId: string, dateRange?: { start: string; end: string }) {
    try {
      const params = new URLSearchParams()
      if (dateRange) {
        params.append('startDate', dateRange.start)
        params.append('endDate', dateRange.end)
      } else {
        params.append('period', '30d')
      }
      
      const response = await fetch(`/api/admin/analytics/student/${userId}?${params}`)
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => null)
        const errorMessage = errorData?.error?.message || `HTTP ${response.status}: ${response.statusText}`
        throw new Error(`Failed to fetch analytics data: ${errorMessage}`)
      }
      
      const data = await response.json()
      const analyticsData = data.data

      // Return data in the format expected by the PDF preview component
      return {
        analytics: analyticsData,
        studentName: analyticsData.user?.name || 'Unknown Student'
      }
      
    } catch (error) {
      console.error('Error fetching analytics data:', error)
      throw error
    }
  }
  
  // Fetch certificate data for preview
  static async fetchCertificateData(quizId: string, userId: string) {
    try {
      // Get eligible certificate attempts
      const response = await fetch(
        `/api/admin/certificates/eligible?quizId=${quizId}&userId=${userId}&limit=1`
      )
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => null)
        const errorMessage = errorData?.error?.message || `HTTP ${response.status}: ${response.statusText}`
        throw new Error(`Failed to fetch certificate data: ${errorMessage}`)
      }
      
      const data = await response.json()
      const attempts = data.data?.eligibleAttempts || []
      
      if (attempts.length === 0) {
        throw new Error('No eligible certificate attempts found. Student may not have passed the quiz.')
      }
      
      const attempt = attempts[0] // Get the best attempt
      return attempt.certificateData
      
    } catch (error) {
      console.error('Error fetching certificate data:', error)
      throw error
    }
  }
  
  // Validate if preview data can be fetched
  static async validatePreviewData(type: 'quiz-result' | 'analytics' | 'certificate', params: any) {
    try {
      switch (type) {
        case 'quiz-result':
          if (!params.quizId || !params.userId) {
            return { valid: false, message: 'Quiz and student selection required' }
          }
          
          // Check if there are completed attempts
          const attemptsResponse = await fetch(
            `/api/admin/quiz-attempts?quizId=${params.quizId}&userId=${params.userId}&status=completed&limit=1`
          )
          
          if (!attemptsResponse.ok) {
            return { valid: false, message: 'Failed to validate quiz attempts' }
          }
          
          const attemptsData = await attemptsResponse.json()
          const hasAttempts = (attemptsData.data?.attempts || []).length > 0
          
          if (!hasAttempts) {
            return { valid: false, message: 'No completed quiz attempts found for this student' }
          }
          
          return { valid: true, message: 'Preview data available' }
          
        case 'analytics':
          if (!params.userId) {
            return { valid: false, message: 'Student selection required' }
          }
          
          // Check if user exists and has data
          const analyticsResponse = await fetch(`/api/admin/analytics/student/${params.userId}?period=30d`)
          
          if (!analyticsResponse.ok) {
            return { valid: false, message: 'Failed to validate analytics data' }
          }
          
          const analyticsData = await analyticsResponse.json()
          const hasData = analyticsData.data?.overview?.totalAttempts > 0
          
          if (!hasData) {
            return { valid: false, message: 'No quiz attempts found for this student' }
          }
          
          return { valid: true, message: 'Preview data available' }
          
        case 'certificate':
          if (!params.quizId || !params.userId) {
            return { valid: false, message: 'Quiz and student selection required' }
          }
          
          // Check if there are eligible certificate attempts
          const certResponse = await fetch(
            `/api/admin/certificates/eligible?quizId=${params.quizId}&userId=${params.userId}&limit=1`
          )
          
          if (!certResponse.ok) {
            return { valid: false, message: 'Failed to validate certificate eligibility' }
          }
          
          const certData = await certResponse.json()
          const hasEligible = (certData.data?.eligibleAttempts || []).length > 0
          
          if (!hasEligible) {
            return { valid: false, message: 'Student has not passed this quiz or no attempts found' }
          }
          
          return { valid: true, message: 'Certificate preview available' }
          
        default:
          return { valid: false, message: 'Invalid preview type' }
      }
    } catch (error) {
      console.error('Error validating preview data:', error)
      return { valid: false, message: 'Failed to validate preview data' }
    }
  }
}

// Helper function to format preview data for display
export function formatPreviewSummary(type: 'quiz-result' | 'analytics' | 'certificate', data: any) {
  switch (type) {
    case 'quiz-result':
      return {
        title: `Quiz Result: ${data.quiz?.title || 'Unknown Quiz'}`,
        subtitle: `Student: ${data.student?.name || 'Unknown Student'}`,
        details: [
          `Score: ${data.score}/${data.quiz?.totalPoints} (${data.percentage}%)`,
          `Time: ${Math.floor((data.timeSpent || 0) / 60)} minutes`,
          `Questions: ${data.quiz?.totalQuestions || 0}`
        ]
      }
      
    case 'analytics':
      return {
        title: `Analytics Report: ${data.user?.name || 'Unknown Student'}`,
        subtitle: `Period: ${data.dateRange?.period || 'Custom range'}`,
        details: [
          `Total Attempts: ${data.overview?.totalAttempts || 0}`,
          `Average Score: ${data.overview?.averagePercentage?.toFixed(1) || 0}%`,
          `Time Spent: ${Math.floor((data.overview?.totalTimeSpent || 0) / 60)} minutes`
        ]
      }
      
    case 'certificate':
      return {
        title: `Certificate: ${data.quizTitle || 'Unknown Quiz'}`,
        subtitle: `Student: ${data.studentName || 'Unknown Student'}`,
        details: [
          `Score: ${data.percentage}%`,
          `Difficulty: ${data.difficulty}`,
          `Certificate ID: ${data.certificateId}`
        ]
      }
      
    default:
      return {
        title: 'Unknown Preview',
        subtitle: '',
        details: []
      }
  }
}
