import type { Config } from "tailwindcss";

const config: Config = {
    darkMode: ["class"],
    content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
  	extend: {
  		backgroundImage: {
  			'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
  			'gradient-conic': 'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))',
  			'gradient-hero': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  			'gradient-primary': 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%)',
  			'gradient-secondary': 'linear-gradient(135deg, #3b82f6 0%, #06b6d4 100%)',
  			'gradient-accent': 'linear-gradient(135deg, #f472b6 0%, #a78bfa 50%, #34d399 100%)',
  			'gradient-glass': 'linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%)',
  			'gradient-dark-glass': 'linear-gradient(135deg, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.05) 100%)',
  			'gradient-mesh': 'radial-gradient(at 40% 20%, hsla(28,100%,74%,1) 0px, transparent 50%), radial-gradient(at 80% 0%, hsla(189,100%,56%,1) 0px, transparent 50%), radial-gradient(at 0% 50%, hsla(355,100%,93%,1) 0px, transparent 50%), radial-gradient(at 80% 50%, hsla(340,100%,76%,1) 0px, transparent 50%), radial-gradient(at 0% 100%, hsla(22,100%,77%,1) 0px, transparent 50%), radial-gradient(at 80% 100%, hsla(242,100%,70%,1) 0px, transparent 50%), radial-gradient(at 0% 0%, hsla(343,100%,76%,1) 0px, transparent 50%)'
  		},
  		borderRadius: {
  			lg: 'var(--radius)',
  			md: 'calc(var(--radius) - 2px)',
  			sm: 'calc(var(--radius) - 4px)',
  			'2xl': '1rem',
  			'3xl': '1.5rem',
  			'4xl': '2rem'
  		},
  		colors: {
  			background: 'hsl(var(--background))',
  			foreground: 'hsl(var(--foreground))',
  			card: {
  				DEFAULT: 'hsl(var(--card))',
  				foreground: 'hsl(var(--card-foreground))'
  			},
  			popover: {
  				DEFAULT: 'hsl(var(--popover))',
  				foreground: 'hsl(var(--popover-foreground))'
  			},
  			primary: {
  				DEFAULT: 'hsl(var(--primary))',
  				foreground: 'hsl(var(--primary-foreground))',
  				50: '#f0f9ff',
  				100: '#e0f2fe',
  				200: '#bae6fd',
  				300: '#7dd3fc',
  				400: '#38bdf8',
  				500: '#0ea5e9',
  				600: '#0284c7',
  				700: '#0369a1',
  				800: '#075985',
  				900: '#0c4a6e',
  				950: '#082f49'
  			},
  			secondary: {
  				DEFAULT: 'hsl(var(--secondary))',
  				foreground: 'hsl(var(--secondary-foreground))',
  				50: '#f8fafc',
  				100: '#f1f5f9',
  				200: '#e2e8f0',
  				300: '#cbd5e1',
  				400: '#94a3b8',
  				500: '#64748b',
  				600: '#475569',
  				700: '#334155',
  				800: '#1e293b',
  				900: '#0f172a',
  				950: '#020617'
  			},
  			muted: {
  				DEFAULT: 'hsl(var(--muted))',
  				foreground: 'hsl(var(--muted-foreground))'
  			},
  			accent: {
  				DEFAULT: 'hsl(var(--accent))',
  				foreground: 'hsl(var(--accent-foreground))',
  				50: '#fdf4ff',
  				100: '#fae8ff',
  				200: '#f5d0fe',
  				300: '#f0abfc',
  				400: '#e879f9',
  				500: '#d946ef',
  				600: '#c026d3',
  				700: '#a21caf',
  				800: '#86198f',
  				900: '#701a75',
  				950: '#4a044e'
  			},
  			destructive: {
  				DEFAULT: 'hsl(var(--destructive))',
  				foreground: 'hsl(var(--destructive-foreground))'
  			},
  			border: 'hsl(var(--border))',
  			input: 'hsl(var(--input))',
  			ring: 'hsl(var(--ring))',
  			chart: {
  				'1': 'hsl(var(--chart-1))',
  				'2': 'hsl(var(--chart-2))',
  				'3': 'hsl(var(--chart-3))',
  				'4': 'hsl(var(--chart-4))',
  				'5': 'hsl(var(--chart-5))'
  			},
  			// 2025 Design System Colors
  			violet: {
  				50: '#f5f3ff',
  				100: '#ede9fe',
  				200: '#ddd6fe',
  				300: '#c4b5fd',
  				400: '#a78bfa',
  				500: '#8b5cf6',
  				600: '#7c3aed',
  				700: '#6d28d9',
  				800: '#5b21b6',
  				900: '#4c1d95',
  				950: '#2e1065'
  			},
  			electric: {
  				50: '#eff6ff',
  				100: '#dbeafe',
  				200: '#bfdbfe',
  				300: '#93c5fd',
  				400: '#60a5fa',
  				500: '#3b82f6',
  				600: '#2563eb',
  				700: '#1d4ed8',
  				800: '#1e40af',
  				900: '#1e3a8a',
  				950: '#172554'
  			},
  			cyan: {
  				50: '#ecfeff',
  				100: '#cffafe',
  				200: '#a5f3fc',
  				300: '#67e8f9',
  				400: '#22d3ee',
  				500: '#06b6d4',
  				600: '#0891b2',
  				700: '#0e7490',
  				800: '#155e75',
  				900: '#164e63',
  				950: '#083344'
  			}
  		},
  		animation: {
  			'fade-in': 'fadeIn 0.6s ease-out',
  			'fade-in-up': 'fadeInUp 0.6s ease-out',
  			'fade-in-down': 'fadeInDown 0.6s ease-out',
  			'fade-in-left': 'fadeInLeft 0.6s ease-out',
  			'fade-in-right': 'fadeInRight 0.6s ease-out',
  			'slide-up': 'slideUp 0.6s ease-out',
  			'slide-down': 'slideDown 0.6s ease-out',
  			'slide-left': 'slideLeft 0.6s ease-out',
  			'slide-right': 'slideRight 0.6s ease-out',
  			'scale-in': 'scaleIn 0.6s ease-out',
  			'scale-out': 'scaleOut 0.6s ease-out',
  			'bounce-in': 'bounceIn 0.8s ease-out',
  			'float': 'float 6s ease-in-out infinite',
  			'float-delayed': 'float 6s ease-in-out infinite 2s',
  			'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
  			'spin-slow': 'spin 8s linear infinite',
  			'wiggle': 'wiggle 1s ease-in-out infinite',
  			'gradient-x': 'gradient-x 15s ease infinite',
  			'gradient-y': 'gradient-y 15s ease infinite',
  			'gradient-xy': 'gradient-xy 15s ease infinite',
  			'shimmer': 'shimmer 2s linear infinite',
  			'ripple': 'ripple 0.6s linear',
  			'glow': 'glow 2s ease-in-out infinite alternate',
  			'typing': 'typing 3.5s steps(40, end), blink-caret 0.75s step-end infinite'
  		},
  		keyframes: {
  			fadeIn: {
  				'0%': { opacity: '0' },
  				'100%': { opacity: '1' }
  			},
  			fadeInUp: {
  				'0%': { opacity: '0', transform: 'translateY(30px)' },
  				'100%': { opacity: '1', transform: 'translateY(0)' }
  			},
  			fadeInDown: {
  				'0%': { opacity: '0', transform: 'translateY(-30px)' },
  				'100%': { opacity: '1', transform: 'translateY(0)' }
  			},
  			fadeInLeft: {
  				'0%': { opacity: '0', transform: 'translateX(-30px)' },
  				'100%': { opacity: '1', transform: 'translateX(0)' }
  			},
  			fadeInRight: {
  				'0%': { opacity: '0', transform: 'translateX(30px)' },
  				'100%': { opacity: '1', transform: 'translateX(0)' }
  			},
  			slideUp: {
  				'0%': { transform: 'translateY(100%)' },
  				'100%': { transform: 'translateY(0)' }
  			},
  			slideDown: {
  				'0%': { transform: 'translateY(-100%)' },
  				'100%': { transform: 'translateY(0)' }
  			},
  			slideLeft: {
  				'0%': { transform: 'translateX(100%)' },
  				'100%': { transform: 'translateX(0)' }
  			},
  			slideRight: {
  				'0%': { transform: 'translateX(-100%)' },
  				'100%': { transform: 'translateX(0)' }
  			},
  			scaleIn: {
  				'0%': { opacity: '0', transform: 'scale(0.9)' },
  				'100%': { opacity: '1', transform: 'scale(1)' }
  			},
  			scaleOut: {
  				'0%': { opacity: '1', transform: 'scale(1)' },
  				'100%': { opacity: '0', transform: 'scale(0.9)' }
  			},
  			bounceIn: {
  				'0%': { opacity: '0', transform: 'scale(0.3)' },
  				'50%': { opacity: '1', transform: 'scale(1.05)' },
  				'70%': { transform: 'scale(0.9)' },
  				'100%': { opacity: '1', transform: 'scale(1)' }
  			},
  			float: {
  				'0%, 100%': { transform: 'translateY(0px)' },
  				'50%': { transform: 'translateY(-20px)' }
  			},
  			wiggle: {
  				'0%, 100%': { transform: 'rotate(-3deg)' },
  				'50%': { transform: 'rotate(3deg)' }
  			},
  			'gradient-x': {
  				'0%, 100%': { 'background-size': '200% 200%', 'background-position': 'left center' },
  				'50%': { 'background-size': '200% 200%', 'background-position': 'right center' }
  			},
  			'gradient-y': {
  				'0%, 100%': { 'background-size': '200% 200%', 'background-position': 'center top' },
  				'50%': { 'background-size': '200% 200%', 'background-position': 'center bottom' }
  			},
  			'gradient-xy': {
  				'0%, 100%': { 'background-size': '400% 400%', 'background-position': 'left center' },
  				'50%': { 'background-size': '400% 400%', 'background-position': 'right center' }
  			},
  			shimmer: {
  				'0%': { transform: 'translateX(-100%)' },
  				'100%': { transform: 'translateX(100%)' }
  			},
  			ripple: {
  				'0%': { transform: 'scale(0)', opacity: '1' },
  				'100%': { transform: 'scale(4)', opacity: '0' }
  			},
  			glow: {
  				'0%': { 'box-shadow': '0 0 5px rgba(139, 92, 246, 0.5)' },
  				'100%': { 'box-shadow': '0 0 20px rgba(139, 92, 246, 0.8), 0 0 30px rgba(139, 92, 246, 0.6)' }
  			},
  			typing: {
  				'0%': { width: '0' },
  				'100%': { width: '100%' }
  			},
  			'blink-caret': {
  				'0%, 100%': { 'border-color': 'transparent' },
  				'50%': { 'border-color': 'currentColor' }
  			}
  		},
  		backdropBlur: {
  			xs: '2px',
  			'4xl': '72px'
  		},
  		boxShadow: {
  			'glass': '0 8px 32px 0 rgba(31, 38, 135, 0.37)',
  			'glass-dark': '0 8px 32px 0 rgba(0, 0, 0, 0.37)',
  			'glow': '0 0 20px rgba(139, 92, 246, 0.5)',
  			'glow-lg': '0 0 40px rgba(139, 92, 246, 0.6)',
  			'neon': '0 0 5px theme(colors.violet.400), 0 0 20px theme(colors.violet.400), 0 0 40px theme(colors.violet.400)',
  			'neon-blue': '0 0 5px theme(colors.electric.400), 0 0 20px theme(colors.electric.400), 0 0 40px theme(colors.electric.400)'
  		},
  		fontFamily: {
  			sans: ['Inter', 'system-ui', 'sans-serif'],
  			display: ['Cal Sans', 'Inter', 'system-ui', 'sans-serif']
  		},
  		fontSize: {
  			'xs': ['0.75rem', { lineHeight: '1rem' }],
  			'sm': ['0.875rem', { lineHeight: '1.25rem' }],
  			'base': ['1rem', { lineHeight: '1.5rem' }],
  			'lg': ['1.125rem', { lineHeight: '1.75rem' }],
  			'xl': ['1.25rem', { lineHeight: '1.75rem' }],
  			'2xl': ['1.5rem', { lineHeight: '2rem' }],
  			'3xl': ['1.875rem', { lineHeight: '2.25rem' }],
  			'4xl': ['2.25rem', { lineHeight: '2.5rem' }],
  			'5xl': ['3rem', { lineHeight: '1' }],
  			'6xl': ['3.75rem', { lineHeight: '1' }],
  			'7xl': ['4.5rem', { lineHeight: '1' }],
  			'8xl': ['6rem', { lineHeight: '1' }],
  			'9xl': ['8rem', { lineHeight: '1' }]
  		}
  	}
  },
  plugins: [
    require("tailwindcss-animate"),
    function({ addUtilities }: any) {
      const newUtilities = {
        '.glass': {
          background: 'rgba(255, 255, 255, 0.1)',
          'backdrop-filter': 'blur(10px)',
          '-webkit-backdrop-filter': 'blur(10px)',
          border: '1px solid rgba(255, 255, 255, 0.2)',
        },
        '.glass-dark': {
          background: 'rgba(0, 0, 0, 0.1)',
          'backdrop-filter': 'blur(10px)',
          '-webkit-backdrop-filter': 'blur(10px)',
          border: '1px solid rgba(255, 255, 255, 0.1)',
        },
        '.glass-strong': {
          background: 'rgba(255, 255, 255, 0.2)',
          'backdrop-filter': 'blur(20px)',
          '-webkit-backdrop-filter': 'blur(20px)',
          border: '1px solid rgba(255, 255, 255, 0.3)',
        },
        '.text-gradient': {
          'background-clip': 'text',
          '-webkit-background-clip': 'text',
          '-webkit-text-fill-color': 'transparent',
        },
        '.perspective-1000': {
          perspective: '1000px',
        },
        '.preserve-3d': {
          'transform-style': 'preserve-3d',
        },
        '.backface-hidden': {
          'backface-visibility': 'hidden',
        },
        '.rotate-y-180': {
          transform: 'rotateY(180deg)',
        }
      }
      addUtilities(newUtilities)
    }
  ],
};
export default config;
