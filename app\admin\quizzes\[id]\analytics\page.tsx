"use client"

import { useState, useEffect } from "react"
import { useParams } from "next/navigation"
import { toast } from "sonner"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  ArrowLeft, 
  BarChart3, 
  TrendingUp, 
  Users, 
  Clock,
  Target,
  Award,
  Download,
  Calendar
} from "lucide-react"
import Link from "next/link"

interface QuizAnalytics {
  quiz: {
    id: string
    title: string
    questionCount: number
  }
  overview: {
    totalAttempts: number
    uniqueUsers: number
    averageScore: number
    averageTime: number
    completionRate: number
    passRate: number
  }
  questionAnalytics: Array<{
    questionId: string
    questionText: string
    questionType: string
    correctAnswers: number
    totalAnswers: number
    averageTime: number
    difficulty: number
    correctRate: number
  }>
  timeAnalytics: Array<{
    date: string
    attempts: number
    averageScore: number
  }>
  userPerformance: Array<{
    userId: string
    userName: string
    attempts: number
    bestScore: number
    averageScore: number
    totalTime: number
  }>
}

export default function QuizAnalyticsPage() {
  const params = useParams()
  const [analytics, setAnalytics] = useState<QuizAnalytics | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchAnalytics()
  }, [params.id])

  const fetchAnalytics = async () => {
    try {
      // Fetch real analytics data from the API
      const response = await fetch(`/api/admin/quizzes/${params.id}/analytics`)

      if (!response.ok) {
        throw new Error('Failed to fetch analytics')
      }

      const result = await response.json()

      if (result.success) {
        setAnalytics(result.data)
        console.log('Analytics loaded successfully:', result.data)
      } else {
        throw new Error(result.message || 'Failed to load analytics')
      }
    } catch (error) {
      console.error('Error fetching analytics:', error)
      toast.error('Failed to load analytics')
    } finally {
      setLoading(false)
    }
  }

  const getDifficultyColor = (difficulty: number) => {
    if (difficulty >= 0.8) return 'text-green-600 bg-green-50 border-green-200'
    if (difficulty >= 0.6) return 'text-yellow-600 bg-yellow-50 border-yellow-200'
    return 'text-red-600 bg-red-50 border-red-200'
  }

  const getDifficultyLabel = (difficulty: number) => {
    if (difficulty >= 0.8) return 'Easy'
    if (difficulty >= 0.6) return 'Medium'
    return 'Hard'
  }

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-muted rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-muted rounded"></div>
            ))}
          </div>
          <div className="h-64 bg-muted rounded"></div>
        </div>
      </div>
    )
  }

  if (!analytics) {
    return (
      <div className="p-6">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-12">
              <BarChart3 className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">Analytics Not Available</h3>
              <p className="text-muted-foreground mb-6">
                Unable to load analytics data for this quiz.
              </p>
              <Button asChild>
                <Link href="/admin/quizzes">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Quizzes
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href={`/admin/quizzes/${params.id}`}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Quiz
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold">Quiz Analytics</h1>
            <p className="text-muted-foreground mt-1">
              Performance insights for "{analytics.quiz.title}"
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline">
            <Calendar className="h-4 w-4 mr-2" />
            Date Range
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export Report
          </Button>
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-full">
                <Users className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <div className="text-2xl font-bold">{analytics.overview.totalAttempts}</div>
                <p className="text-xs text-muted-foreground">Total Attempts</p>
                <p className="text-xs text-blue-600">{analytics.overview.uniqueUsers} unique users</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-full">
                <TrendingUp className="h-5 w-5 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <div className="text-2xl font-bold">{analytics.overview.averageScore}%</div>
                <p className="text-xs text-muted-foreground">Average Score</p>
                <p className="text-xs text-green-600">{analytics.overview.passRate}% pass rate</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-full">
                <Clock className="h-5 w-5 text-purple-600 dark:text-purple-400" />
              </div>
              <div>
                <div className="text-2xl font-bold">{analytics.overview.averageTime}m</div>
                <p className="text-xs text-muted-foreground">Average Time</p>
                <p className="text-xs text-purple-600">{analytics.overview.completionRate}% completion</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Question Performance */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Question Performance Analysis
          </CardTitle>
          <CardDescription>
            Detailed breakdown of how students performed on each question
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {analytics.questionAnalytics.map((question, index) => (
              <div key={question.questionId} className="p-4 border rounded-lg">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-semibold">
                        Question {index + 1}
                      </h4>
                      <Badge variant="secondary" className="text-xs">
                        {question.questionType}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground line-clamp-2">
                      {question.questionText}
                    </p>
                  </div>
                  <Badge variant="outline" className={getDifficultyColor(question.difficulty)}>
                    {getDifficultyLabel(question.difficulty)}
                  </Badge>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="font-medium">Correct Answers:</span>
                    <div className="text-lg font-bold text-green-600">
                      {question.correctAnswers}/{question.totalAnswers}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {Math.round((question.correctAnswers / question.totalAnswers) * 100)}% accuracy
                    </div>
                  </div>
                  <div>
                    <span className="font-medium">Average Time:</span>
                    <div className="text-lg font-bold">
                      {question.averageTime}s
                    </div>
                  </div>
                  <div>
                    <span className="font-medium">Difficulty Score:</span>
                    <div className="text-lg font-bold">
                      {(question.difficulty * 100).toFixed(0)}%
                    </div>
                  </div>
                  <div className="flex items-center">
                    <div className="w-full bg-muted rounded-full h-2">
                      <div 
                        className="bg-green-500 h-2 rounded-full" 
                        style={{ width: `${(question.correctAnswers / question.totalAnswers) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Top Performers */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Award className="h-5 w-5" />
            Top Performers
          </CardTitle>
          <CardDescription>
            Students with the highest performance on this quiz
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {analytics.userPerformance.map((user, index) => (
              <div key={user.userId} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                    <span className="text-sm font-bold">#{index + 1}</span>
                  </div>
                  <div>
                    <h4 className="font-semibold">{user.userName}</h4>
                    <p className="text-sm text-muted-foreground">
                      {user.attempts} attempt{user.attempts !== 1 ? 's' : ''}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-lg font-bold text-green-600">
                    {user.bestScore}%
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Avg: {user.averageScore}%
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {user.totalTime}m total
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Performance Trends */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Performance Trends
          </CardTitle>
          <CardDescription>
            Quiz performance over time
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {analytics.timeAnalytics.map((day, index) => (
              <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <div className="font-semibold">
                    {new Date(day.date).toLocaleDateString()}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {day.attempts} attempts
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-lg font-bold">
                    {day.averageScore}%
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Average Score
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
