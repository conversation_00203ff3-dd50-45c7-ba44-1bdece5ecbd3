"use client"

import { useState, useEffect } from "react"
import { useParams } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { ArrowLeft, ArrowRight, Clock, Users, Target, BookOpen } from "lucide-react"
import { toast } from "sonner"
import Link from "next/link"

interface Question {
  id: string
  type: string
  text: string
  options: string[]
  correctAnswer: string
  explanation?: string
  points: number
  order: number
}

interface QuizPreview {
  id: string
  title: string
  description: string
  type: string
  difficulty: string
  tags: string[]
  timeLimit: number
  maxAttempts: number
  passingScore: number
  instructions: string
  questions: Question[]
  creator: {
    name: string
    email: string
  }
}

export default function QuizPreviewPage() {
  const params = useParams()
  const [loading, setLoading] = useState(true)
  const [quiz, setQuiz] = useState<QuizPreview | null>(null)
  const [currentQuestion, setCurrentQuestion] = useState(0)
  const [selectedAnswers, setSelectedAnswers] = useState<Record<string, string>>({})

  useEffect(() => {
    fetchQuizData()
  }, [params.id])

  const fetchQuizData = async () => {
    try {
      const response = await fetch(`/api/admin/quizzes/${params.id}`)
      if (!response.ok) throw new Error('Failed to fetch quiz')
      
      const data = await response.json()
      setQuiz(data)
    } catch (error) {
      console.error('Error fetching quiz:', error)
      toast.error('Failed to load quiz preview')
    } finally {
      setLoading(false)
    }
  }

  const handleAnswerSelect = (questionId: string, answer: string) => {
    setSelectedAnswers(prev => ({
      ...prev,
      [questionId]: answer
    }))
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'EASY': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case 'MEDIUM': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
      case 'HARD': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'QUIZ': return <Target className="h-5 w-5" />
      case 'TEST_SERIES': return <BookOpen className="h-5 w-5" />
      case 'DAILY_PRACTICE': return <Clock className="h-5 w-5" />
      default: return <Target className="h-5 w-5" />
    }
  }

  if (loading) {
    return <div className="p-6">Loading preview...</div>
  }

  if (!quiz) {
    return <div className="p-6">Quiz not found</div>
  }

  const currentQ = quiz.questions[currentQuestion]
  const progress = ((currentQuestion + 1) / quiz.questions.length) * 100

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href={`/admin/quizzes/${params.id}`}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Quiz
            </Link>
          </Button>
          <div>
            <div className="flex items-center gap-3">
              {getTypeIcon(quiz.type)}
              <h1 className="text-3xl font-bold">{quiz.title}</h1>
            </div>
            <p className="text-muted-foreground mt-1">
              Preview Mode - by {quiz.creator.name}
            </p>
          </div>
        </div>
        <Badge className={getDifficultyColor(quiz.difficulty)}>
          {quiz.difficulty}
        </Badge>
      </div>

      {/* Quiz Info */}
      <Card>
        <CardHeader>
          <CardTitle>Quiz Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{quiz.questions.length}</div>
              <div className="text-xs text-muted-foreground">Questions</div>
            </div>
            <div className="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <div className="text-2xl font-bold text-green-600">{quiz.timeLimit}</div>
              <div className="text-xs text-muted-foreground">Minutes</div>
            </div>
            <div className="text-center p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">{quiz.maxAttempts}</div>
              <div className="text-xs text-muted-foreground">Max Attempts</div>
            </div>
            <div className="text-center p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
              <div className="text-2xl font-bold text-orange-600">{quiz.passingScore}%</div>
              <div className="text-xs text-muted-foreground">Passing Score</div>
            </div>
          </div>
          
          {quiz.description && (
            <div className="mt-4">
              <p className="text-muted-foreground">{quiz.description}</p>
            </div>
          )}

          {quiz.instructions && (
            <div className="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <h4 className="font-semibold mb-2">Instructions:</h4>
              <p className="text-sm">{quiz.instructions}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Question Preview */}
      {quiz.questions.length > 0 && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Question {currentQuestion + 1} of {quiz.questions.length}</CardTitle>
              <Badge variant="outline">{currentQ.points} point{currentQ.points !== 1 ? 's' : ''}</Badge>
            </div>
            <Progress value={progress} className="w-full" />
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-lg font-medium">
              {currentQ.text}
            </div>

            {currentQ.type === 'MCQ' && (
              <div className="space-y-2">
                {currentQ.options.map((option, index) => {
                  const optionLabel = ['A', 'B', 'C', 'D'][index]
                  const isSelected = selectedAnswers[currentQ.id] === optionLabel
                  const isCorrect = currentQ.correctAnswer === optionLabel
                  
                  return (
                    <div
                      key={index}
                      className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                        isSelected 
                          ? isCorrect 
                            ? 'border-green-500 bg-green-50 dark:bg-green-900/20' 
                            : 'border-red-500 bg-red-50 dark:bg-red-900/20'
                          : isCorrect
                            ? 'border-green-500 bg-green-50 dark:bg-green-900/20'
                            : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => handleAnswerSelect(currentQ.id, optionLabel)}
                    >
                      <div className="flex items-center gap-3">
                        <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center text-sm font-medium ${
                          isSelected || isCorrect ? 'border-current' : 'border-gray-300'
                        }`}>
                          {optionLabel}
                        </div>
                        <span>{option}</span>
                      </div>
                    </div>
                  )
                })}
              </div>
            )}

            {currentQ.explanation && (
              <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-900/20 rounded-lg">
                <h4 className="font-semibold mb-2">Explanation:</h4>
                <p className="text-sm">{currentQ.explanation}</p>
              </div>
            )}

            {/* Navigation */}
            <div className="flex justify-between pt-4">
              <Button
                variant="outline"
                onClick={() => setCurrentQuestion(Math.max(0, currentQuestion - 1))}
                disabled={currentQuestion === 0}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Previous
              </Button>
              
              <Button
                onClick={() => setCurrentQuestion(Math.min(quiz.questions.length - 1, currentQuestion + 1))}
                disabled={currentQuestion === quiz.questions.length - 1}
              >
                Next
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {quiz.questions.length === 0 && (
        <Card>
          <CardContent className="text-center py-8">
            <p className="text-muted-foreground">No questions added to this quiz yet.</p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
