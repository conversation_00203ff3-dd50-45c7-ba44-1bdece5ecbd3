import { NextRequest } from 'next/server'
import { createAPIHandler, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const getQuizAttemptsSchema = z.object({
  quizId: z.string().optional(),
  userId: z.string().optional(),
  status: z.enum(['completed', 'in_progress', 'all']).optional().default('all'),
  limit: z.string().transform(val => parseInt(val)).optional().default("50"),
  page: z.string().transform(val => parseInt(val)).optional().default("1"),
  includeQuestions: z.string().optional().default('false').transform(val => val === 'true')
})

// GET /api/admin/quiz-attempts - Get quiz attempts with filtering
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    validateQuery: getQuizAttemptsSchema
  },
  async (request: NextRequest, { validatedQuery }) => {
    const { quizId, userId, status, limit, page, includeQuestions } = validatedQuery

    try {
      // Build where clause
      const where: any = {}
      
      if (quizId) {
        where.quizId = quizId
      }
      
      if (userId) {
        where.userId = userId
      }
      
      if (status !== 'all') {
        where.isCompleted = status === 'completed'
      }

      // Get total count for pagination
      const totalCount = await prisma.quizAttempt.count({ where })
      const totalPages = Math.ceil(totalCount / limit)
      const skip = (page - 1) * limit

      // Fetch quiz attempts with related data
      const attempts = await prisma.quizAttempt.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          quiz: {
            include: {
              questions: includeQuestions ? {
                orderBy: { order: 'asc' }
              } : false
            }
          }
        },
        orderBy: {
          startedAt: 'desc'
        },
        skip,
        take: limit
      })

      // Transform data for response
      const transformedAttempts = attempts.map((attempt: any) => ({
        id: attempt.id,
        quizId: attempt.quizId,
        userId: attempt.userId,
        score: attempt.score,
        totalPoints: attempt.totalPoints,
        percentage: attempt.percentage,
        correctAnswers: attempt.correctAnswers,
        incorrectAnswers: attempt.incorrectAnswers,
        unansweredQuestions: attempt.unansweredQuestions,
        totalQuestions: attempt.totalQuestions,
        timeSpent: attempt.timeSpent,
        isCompleted: attempt.isCompleted,
        startedAt: attempt.startedAt.toISOString(),
        completedAt: attempt.completedAt?.toISOString(),
        answers: attempt.answers,
        user: attempt.user,
        quiz: {
          id: attempt.quiz.id,
          title: attempt.quiz.title,
          description: attempt.quiz.description,
          type: attempt.quiz.type,
          difficulty: attempt.quiz.difficulty,
          timeLimit: attempt.quiz.timeLimit,
          questions: includeQuestions ? attempt.quiz.questions : undefined
        }
      }))

      return APIResponse.success({
        attempts: transformedAttempts,
        pagination: {
          currentPage: page,
          totalPages,
          totalCount,
          limit,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      }, 'Quiz attempts retrieved successfully')

    } catch (error) {
      console.error('Error fetching quiz attempts:', error)
      return APIResponse.error(
        error instanceof Error ? error.message : 'Failed to fetch quiz attempts',
        500
      )
    }
  }
)
