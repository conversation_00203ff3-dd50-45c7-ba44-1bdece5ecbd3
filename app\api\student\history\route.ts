import { NextRequest } from 'next/server'
import { createAPIHandler, APIResponse, commonSchemas } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const querySchema = commonSchemas.pagination.extend({
  status: z.enum(['all', 'completed', 'in_progress', 'abandoned']).optional().default('all'),
  type: z.enum(['QUIZ', 'TEST_SERIES', 'DAILY_PRACTICE']).optional(),
  difficulty: z.enum(['EASY', 'MEDIUM', 'HARD']).optional(),
  dateFrom: z.string().datetime().optional(),
  dateTo: z.string().datetime().optional(),
  sortBy: z.enum(['date', 'score', 'title']).optional().default('date'),
  sortOrder: z.enum(['asc', 'desc']).optional().default('desc')
})

// GET /api/student/history - Get student quiz attempt history
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT',
     
    validateQuery: querySchema
  },
  async (request: NextRequest, { user, validatedQuery }) => {
    try {
      const { 
        page, 
        limit, 
        status, 
        type, 
        difficulty, 
        dateFrom, 
        dateTo, 
        sortBy, 
        sortOrder 
      } = validatedQuery

      // Build where clause
      const where: any = {
        userId: user.id
      }

      // Filter by status
      if (status !== 'all') {
        switch (status) {
          case 'completed':
            where.completedAt = { not: null }
            where.isCompleted = true
            break
          case 'in_progress':
            where.completedAt = null
            where.isCompleted = false
            break
          case 'abandoned':
            where.completedAt = null
            where.isCompleted = false
            // Add condition for attempts older than 24 hours
            where.startedAt = {
              lt: new Date(Date.now() - 24 * 60 * 60 * 1000)
            }
            break
        }
      }

      // Filter by quiz type
      if (type) {
        where.quiz = {
          type
        }
      }

      // Filter by difficulty
      if (difficulty) {
        where.quiz = {
          ...where.quiz,
          difficulty
        }
      }

      // Filter by date range
      if (dateFrom || dateTo) {
        where.startedAt = {}
        if (dateFrom) {
          where.startedAt.gte = new Date(dateFrom)
        }
        if (dateTo) {
          where.startedAt.lte = new Date(dateTo)
        }
      }

      // Build order by clause
      let orderBy: any = {}
      switch (sortBy) {
        case 'date':
          orderBy = { startedAt: sortOrder }
          break
        case 'score':
          orderBy = { percentage: sortOrder }
          break
        case 'title':
          orderBy = { quiz: { title: sortOrder } }
          break
      }

      // Get total count
      const total = await prisma.quizAttempt.count({ where })

      // Get attempts with pagination
      const attempts = await prisma.quizAttempt.findMany({
        where,
        include: {
          quiz: {
            select: {
              id: true,
              title: true,
              description: true,
              type: true,
              difficulty: true,
              tags: true,
              thumbnail: true,
              timeLimit: true,
              passingScore: true,
              _count: {
                select: {
                  questions: true
                }
              }
            }
          }
        },
        orderBy,
        skip: (page - 1) * limit,
        take: limit
      })

      // Transform data for frontend
      const transformedAttempts = attempts.map(attempt => {
        const timeSpent = attempt.completedAt 
          ? Math.round((attempt.completedAt.getTime() - attempt.startedAt.getTime()) / (1000 * 60))
          : null

        let status = 'in_progress'
        if (attempt.isCompleted && attempt.completedAt) {
          status = 'completed'
        } else if (!attempt.isCompleted && attempt.startedAt < new Date(Date.now() - 24 * 60 * 60 * 1000)) {
          status = 'abandoned'
        }

        return {
          id: attempt.id,
          quiz: {
            id: attempt.quiz.id,
            title: attempt.quiz.title,
            description: attempt.quiz.description,
            type: attempt.quiz.type,
            difficulty: attempt.quiz.difficulty,
            category: attempt.quiz.tags[0] || 'General', // Use first tag as category
            tags: attempt.quiz.tags,
            thumbnail: attempt.quiz.thumbnail,
            timeLimit: attempt.quiz.timeLimit,
            questionCount: attempt.quiz._count.questions
          },
          score: attempt.score,
          percentage: attempt.percentage,
          timeSpent,
          status,
          startedAt: attempt.startedAt.toISOString(),
          completedAt: attempt.completedAt?.toISOString() || null,
          isCompleted: attempt.isCompleted,
          correctAnswers: attempt.correctAnswers || Math.floor((attempt.percentage || 0) / 100 * (attempt.quiz._count.questions || 10)),
          totalQuestions: attempt.totalQuestions || attempt.quiz._count.questions || 10,
          passed: (attempt.percentage || 0) >= (attempt.quiz.passingScore || 0),
          rank: undefined // TODO: Calculate actual rank from quiz leaderboard
        }
      })

      // Get leaderboard position and total students
      const leaderboardPosition = await getLeaderboardPosition(user.id)
      const totalStudents = await prisma.user.count({
        where: { role: 'STUDENT' }
      })

      // Calculate summary statistics
      const completedAttempts = attempts.filter(a => a.isCompleted && a.completedAt)
      const summary = {
        totalAttempts: total,
        completedAttempts: completedAttempts.length,
        inProgressAttempts: attempts.filter(a => !a.isCompleted && a.startedAt >= new Date(Date.now() - 24 * 60 * 60 * 1000)).length,
        abandonedAttempts: attempts.filter(a => !a.isCompleted && a.startedAt < new Date(Date.now() - 24 * 60 * 60 * 1000)).length,
        averageScore: completedAttempts.length > 0
          ? Math.round(completedAttempts.reduce((sum, attempt) => sum + (attempt.percentage || 0), 0) / completedAttempts.length)
          : 0,
        totalTimeSpent: completedAttempts.reduce((total, attempt) => {
          const timeSpent = attempt.completedAt!.getTime() - attempt.startedAt.getTime()
          return total + Math.round(timeSpent / (1000 * 60))
        }, 0),
        bestScore: completedAttempts.length > 0
          ? Math.max(...completedAttempts.map(a => a.percentage || 0))
          : 0,
        rank: leaderboardPosition?.position || 0,
        totalStudents,
        recentActivity: attempts.slice(0, 5).map(attempt => ({
          id: attempt.id,
          quizTitle: attempt.quiz.title,
          score: attempt.percentage,
          date: attempt.startedAt.toISOString(),
          status: attempt.isCompleted ? 'completed' : 'in_progress'
        }))
      }

      // Get unique categories from user's quiz attempts
      const categories = await prisma.quiz.findMany({
        where: {
          attempts: {
            some: { userId: user.id }
          },
          category: { not: null }
        },
        select: { category: true },
        distinct: ['category']
      })

      const uniqueCategories = categories
        .map(q => q.category)
        .filter(Boolean)
        .sort()

      return APIResponse.success({
        attempts: transformedAttempts,
        summary,
        categories: uniqueCategories,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
          hasNext: page < Math.ceil(total / limit),
          hasPrev: page > 1
        }
      }, 'History retrieved successfully')

    } catch (error) {
      console.error('Error fetching history:', error)
      return APIResponse.error('Failed to fetch history', 500)
    }
  }
)

// Helper function to get leaderboard position
async function getLeaderboardPosition(userId: string) {
  // Get user's total points
  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: { totalPoints: true }
  })

  if (!user) return null

  // Count users with more points
  const usersAbove = await prisma.user.count({
    where: {
      totalPoints: { gt: user.totalPoints },
      role: 'STUDENT'
    }
  })

  return {
    position: usersAbove + 1,
    totalPoints: user.totalPoints
  }
}
