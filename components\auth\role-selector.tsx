"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { GraduationCap, Shield } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { motion } from "framer-motion"
import { toast } from "@/lib/toast-utils"
import { useRouter } from "next/navigation"

interface RoleSelectorProps {
  onRoleSelected: (role: 'STUDENT' | 'ADMIN') => void
  isLoading?: boolean
}

export function RoleSelector({ onRoleSelected, isLoading = false }: RoleSelectorProps) {
  const [selectedRole, setSelectedRole] = useState<'STUDENT' | 'ADMIN'>('STUDENT')
  const router = useRouter()

  const handleSubmit = async () => {
    try {
      const response = await fetch('/api/auth/update-role', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ role: selectedRole }),
      })

      if (!response.ok) {
        throw new Error('Failed to update role')
      }

      toast.success('Role updated successfully!')
      onRoleSelected(selectedRole)
      
      // Redirect based on role
      if (selectedRole === 'ADMIN') {
        router.push('/admin')
      } else {
        router.push('/dashboard')
      }
    } catch (error) {
      toast.error('Failed to update role. Please try again.')
      console.error('Error updating role:', error)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-md"
      >
        <Card className="shadow-2xl border-0 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm">
          <CardHeader className="text-center space-y-4">
            <div>
              <CardTitle className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Choose Your Role
              </CardTitle>
              <CardDescription className="text-base mt-2">
                Select how you'd like to use QuizMaster
              </CardDescription>
            </div>
          </CardHeader>
          
          <CardContent className="space-y-6">
            <RadioGroup
              value={selectedRole}
              onValueChange={(value) => setSelectedRole(value as 'STUDENT' | 'ADMIN')}
              className="space-y-4"
            >
              <motion.div
                whileHover={{ scale: 1.02 }}
                className={`flex items-center space-x-3 p-4 rounded-lg border-2 transition-all cursor-pointer ${
                  selectedRole === 'STUDENT' 
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
                    : 'border-gray-200 dark:border-gray-700 hover:border-gray-300'
                }`}
                onClick={() => setSelectedRole('STUDENT')}
              >
                <RadioGroupItem value="STUDENT" id="student" />
                <div className="flex items-center space-x-3 flex-1">
                  <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-full">
                    <GraduationCap className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <Label htmlFor="student" className="font-semibold cursor-pointer">
                      Student
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      Take quizzes, track progress, and learn
                    </p>
                  </div>
                </div>
                {selectedRole === 'STUDENT' && (
                  <Badge variant="default" className="bg-blue-500">
                    Selected
                  </Badge>
                )}
              </motion.div>

              <motion.div
                whileHover={{ scale: 1.02 }}
                className={`flex items-center space-x-3 p-4 rounded-lg border-2 transition-all cursor-pointer ${
                  selectedRole === 'ADMIN' 
                    ? 'border-purple-500 bg-purple-50 dark:bg-purple-900/20' 
                    : 'border-gray-200 dark:border-gray-700 hover:border-gray-300'
                }`}
                onClick={() => setSelectedRole('ADMIN')}
              >
                <RadioGroupItem value="ADMIN" id="admin" />
                <div className="flex items-center space-x-3 flex-1">
                  <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-full">
                    <Shield className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                  </div>
                  <div>
                    <Label htmlFor="admin" className="font-semibold cursor-pointer">
                      Admin
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      Create quizzes, manage users, and analyze data
                    </p>
                  </div>
                </div>
                {selectedRole === 'ADMIN' && (
                  <Badge variant="default" className="bg-purple-500">
                    Selected
                  </Badge>
                )}
              </motion.div>
            </RadioGroup>

            <Button
              onClick={handleSubmit}
              disabled={isLoading}
              className="w-full h-12 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold shadow-lg hover:shadow-xl transition-all duration-200"
            >
              {isLoading ? (
                <div className="flex items-center space-x-2">
                  <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full"></div>
                  <span>Setting up your account...</span>
                </div>
              ) : (
                'Continue'
              )}
            </Button>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}
