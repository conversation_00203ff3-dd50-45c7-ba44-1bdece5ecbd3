import { NextRequest } from 'next/server'
 import { createAP<PERSON><PERSON>andler, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const saveProgressSchema = z.object({
  answers: z.record(z.string(), z.any()),
  currentQuestionIndex: z.number().optional(),
  isPaused: z.boolean().optional()
})

// PUT /api/student/quizzes/[id]/attempt/[attemptId]/save - Save quiz progress
export const PUT = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT',
     
    validateBody: saveProgressSchema
  },
  async (request: NextRequest, { user, params, validatedBody }) => {
    const resolvedParams = await params
    const quizId = resolvedParams?.id as string
    const attemptId = resolvedParams?.attemptId as string
    const { answers, currentQuestionIndex, isPaused } = validatedBody

    if (!quizId || !attemptId) {
      return APIResponse.error('Quiz ID and Attempt ID are required', 400)
    }

    try {
      console.log(`Saving progress for attempt ${attemptId} by user ${user.id}`)

      // Verify the attempt belongs to the user and is active
      const attempt = await prisma.quizAttempt.findUnique({
        where: {
          id: attemptId,
          userId: user.id,
          quizId
        }
      })

      if (!attempt) {
        return APIResponse.error('Quiz attempt not found', 404)
      }

      if (attempt.isCompleted) {
        return APIResponse.error('Cannot save progress for completed quiz', 400)
      }

      // Update the attempt with current progress
      const updatedAttempt = await prisma.quizAttempt.update({
        where: { id: attemptId },
        data: {
          answers: answers,
          isPaused: isPaused || false
        }
      })

      console.log(`Progress saved successfully for attempt ${attemptId}`)

      return APIResponse.success({
        attemptId: updatedAttempt.id,
        answers: updatedAttempt.answers,
        isPaused: updatedAttempt.isPaused,
        savedAt: new Date().toISOString()
      }, 'Progress saved successfully')

    } catch (error) {
      console.error('Error saving quiz progress:', error)
      console.error('Error details:', error instanceof Error ? error.message : String(error))
      
      return APIResponse.error(
        error instanceof Error ? error.message : 'Failed to save progress',
        500
      )
    }
  }
)
