'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { toast } from 'sonner'
import { 
  BookOpen, 
  Calendar, 
  Clock, 
  FileText, 
  GraduationCap, 
  Printer,
  Download,
  Sparkles,
  Target,
  Users
} from 'lucide-react'

interface TestSeriesGeneratorProps {
  onTestGenerated: (test: any) => void
  onClose: () => void
}

export function TestSeriesGenerator({ onTestGenerated, onClose }: TestSeriesGeneratorProps) {
  const [isGenerating, setIsGenerating] = useState(false)
  const [testConfig, setTestConfig] = useState({
    testType: 'WEEKLY_TEST',
    examPattern: '',
    syllabusCoverage: 'CURRENT_WEEK',
    subject: '',
    chapter: '',
    topic: '',
    questionCount: 20,
    difficulty: 'MEDIUM',
    questionTypes: ['MCQ'],
    timeLimit: 40,
    institutionName: '',
    testDate: '',
    printFormat: true,
    includeAnswerKey: true,
    includeInstructions: true,
    content: '',
    language: 'ENGLISH'
  })

  const testTypes = [
    { 
      value: 'DAILY_PRACTICE', 
      label: 'Daily Practice', 
      icon: BookOpen,
      description: 'Quick daily revision (5-10 questions)',
      defaultQuestions: 5,
      defaultTime: 10
    },
    { 
      value: 'WEEKLY_TEST', 
      label: 'Weekly Test', 
      icon: Calendar,
      description: 'Weekly assessment (15-25 questions)',
      defaultQuestions: 20,
      defaultTime: 40
    },
    { 
      value: 'MONTHLY_TEST', 
      label: 'Monthly Test', 
      icon: FileText,
      description: 'Monthly evaluation (40-60 questions)',
      defaultQuestions: 50,
      defaultTime: 90
    },
    { 
      value: 'TEST_SERIES', 
      label: 'Test Series', 
      icon: GraduationCap,
      description: 'Full exam simulation (80-120 questions)',
      defaultQuestions: 100,
      defaultTime: 180
    }
  ]

  const examPatterns = [
    { value: 'JEE_MAIN', label: 'JEE Main', category: 'Engineering' },
    { value: 'JEE_ADVANCED', label: 'JEE Advanced', category: 'Engineering' },
    { value: 'NEET', label: 'NEET', category: 'Medical' },
    { value: 'UPSC_PRELIMS', label: 'UPSC Prelims', category: 'Civil Services' },
    { value: 'SSC_CGL', label: 'SSC CGL', category: 'Government' },
    { value: 'GATE', label: 'GATE', category: 'Engineering' },
    { value: 'CAT', label: 'CAT', category: 'Management' },
    { value: 'CLAT', label: 'CLAT', category: 'Law' },
    { value: 'CBSE_BOARD', label: 'CBSE Board', category: 'School' },
    { value: 'ICSE_BOARD', label: 'ICSE Board', category: 'School' },
    { value: 'AIIMS_NURSING', label: 'AIIMS Nursing', category: 'Nursing' },
    { value: 'NEET_NURSING', label: 'NEET Nursing', category: 'Nursing' },
    { value: 'PGIMER_NURSING', label: 'PGIMER Nursing', category: 'Nursing' }
  ]

  const handleTestTypeChange = (value: string) => {
    const selectedType = testTypes.find(t => t.value === value)
    if (selectedType) {
      setTestConfig(prev => ({
        ...prev,
        testType: value,
        questionCount: selectedType.defaultQuestions,
        timeLimit: selectedType.defaultTime
      }))
    }
  }

  const generateTest = async () => {
    if (!testConfig.subject.trim()) {
      toast.error('Please enter a subject')
      return
    }

    setIsGenerating(true)
    try {
      const response = await fetch('/api/admin/test-series/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(testConfig)
      })

      if (response.ok) {
        const data = await response.json()
        onTestGenerated(data.data.test)
        toast.success('Test generated successfully!')
        onClose()
      } else {
        const error = await response.json()
        toast.error(error.error || 'Failed to generate test')
      }
    } catch (error) {
      console.error('Error generating test:', error)
      toast.error('Failed to generate test')
    } finally {
      setIsGenerating(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5 text-purple-600" />
            Institutional Test Series Generator
          </CardTitle>
          <CardDescription>
            Generate professional test papers for your institution with AI assistance
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Test Type Selection */}
          <div>
            <Label className="text-base font-semibold mb-3 block">Test Type</Label>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
              {testTypes.map((type) => {
                const Icon = type.icon
                return (
                  <Card 
                    key={type.value}
                    className={`cursor-pointer transition-all hover:shadow-md ${
                      testConfig.testType === type.value 
                        ? 'ring-2 ring-primary bg-primary/5' 
                        : 'hover:bg-muted/50'
                    }`}
                    onClick={() => handleTestTypeChange(type.value)}
                  >
                    <CardContent className="p-4 text-center">
                      <Icon className="h-8 w-8 mx-auto mb-2 text-primary" />
                      <div className="font-medium text-sm">{type.label}</div>
                      <div className="text-xs text-muted-foreground mt-1">
                        {type.description}
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          </div>

          {/* Basic Configuration */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="subject">Subject *</Label>
              <Input
                id="subject"
                value={testConfig.subject}
                onChange={(e) => setTestConfig(prev => ({ ...prev, subject: e.target.value }))}
                placeholder="e.g., Mathematics, Physics, Biology"
              />
            </div>
            <div>
              <Label htmlFor="examPattern">Exam Pattern (Optional)</Label>
              <Select
                value={testConfig.examPattern}
                onValueChange={(value) => setTestConfig(prev => ({ ...prev, examPattern: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select exam pattern" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">No specific pattern</SelectItem>
                  {examPatterns.map((pattern) => (
                    <SelectItem key={pattern.value} value={pattern.value}>
                      <div className="flex items-center gap-2">
                        <span>{pattern.label}</span>
                        <Badge variant="outline" className="text-xs">
                          {pattern.category}
                        </Badge>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Institution Details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="institutionName">Institution Name</Label>
              <Input
                id="institutionName"
                value={testConfig.institutionName}
                onChange={(e) => setTestConfig(prev => ({ ...prev, institutionName: e.target.value }))}
                placeholder="Your institution name"
              />
            </div>
            <div>
              <Label htmlFor="testDate">Test Date</Label>
              <Input
                id="testDate"
                type="date"
                value={testConfig.testDate}
                onChange={(e) => setTestConfig(prev => ({ ...prev, testDate: e.target.value }))}
              />
            </div>
          </div>

          {/* Test Configuration */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="questionCount">Number of Questions</Label>
              <Input
                id="questionCount"
                type="number"
                min="5"
                max="200"
                value={testConfig.questionCount}
                onChange={(e) => setTestConfig(prev => ({ ...prev, questionCount: parseInt(e.target.value) || 10 }))}
              />
            </div>
            <div>
              <Label htmlFor="timeLimit">Time Limit (minutes)</Label>
              <Input
                id="timeLimit"
                type="number"
                min="5"
                max="300"
                value={testConfig.timeLimit}
                onChange={(e) => setTestConfig(prev => ({ ...prev, timeLimit: parseInt(e.target.value) || 30 }))}
              />
            </div>
            <div>
              <Label htmlFor="difficulty">Difficulty Level</Label>
              <Select
                value={testConfig.difficulty}
                onValueChange={(value: any) => setTestConfig(prev => ({ ...prev, difficulty: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="EASY">Easy</SelectItem>
                  <SelectItem value="MEDIUM">Medium</SelectItem>
                  <SelectItem value="HARD">Hard</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Language Selection */}
          <div>
            <Label>Language</Label>
            <Select
              value={testConfig.language}
              onValueChange={(value: any) => setTestConfig(prev => ({ ...prev, language: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select language" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ENGLISH">English Only</SelectItem>
                <SelectItem value="HINDI">Hindi Only (हिंदी)</SelectItem>
                <SelectItem value="BILINGUAL">Bilingual (English + Hindi)</SelectItem>
              </SelectContent>
            </Select>
            {testConfig.language === 'BILINGUAL' && (
              <div className="mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <div className="text-sm text-blue-700 dark:text-blue-300">
                  <div className="font-medium mb-1">Bilingual Format</div>
                  <div>Questions will be generated in both English and Hindi for better comprehension.</div>
                </div>
              </div>
            )}
          </div>

          {/* Content Input */}
          <div>
            <Label htmlFor="content">Additional Content/Instructions (Optional)</Label>
            <Textarea
              id="content"
              value={testConfig.content}
              onChange={(e) => setTestConfig(prev => ({ ...prev, content: e.target.value }))}
              placeholder="Provide specific topics, learning objectives, or content to focus on..."
              rows={3}
            />
          </div>

          {/* Format Options */}
          <div className="flex flex-wrap gap-4">
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="printFormat"
                checked={testConfig.printFormat}
                onChange={(e) => setTestConfig(prev => ({ ...prev, printFormat: e.target.checked }))}
                className="rounded border-gray-300"
              />
              <Label htmlFor="printFormat" className="text-sm flex items-center gap-1">
                <Printer className="h-4 w-4" />
                Print Format
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="includeAnswerKey"
                checked={testConfig.includeAnswerKey}
                onChange={(e) => setTestConfig(prev => ({ ...prev, includeAnswerKey: e.target.checked }))}
                className="rounded border-gray-300"
              />
              <Label htmlFor="includeAnswerKey" className="text-sm">Include Answer Key</Label>
            </div>
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="includeInstructions"
                checked={testConfig.includeInstructions}
                onChange={(e) => setTestConfig(prev => ({ ...prev, includeInstructions: e.target.checked }))}
                className="rounded border-gray-300"
              />
              <Label htmlFor="includeInstructions" className="text-sm">Include Instructions</Label>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button 
              onClick={generateTest} 
              disabled={isGenerating || !testConfig.subject.trim()}
              className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
            >
              {isGenerating ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  Generating...
                </>
              ) : (
                <>
                  <Sparkles className="h-4 w-4 mr-2" />
                  Generate Test
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
