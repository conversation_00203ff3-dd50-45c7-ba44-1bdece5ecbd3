import { NextRequest } from 'next/server'
import { createAPIHandler, APIResponse, commonSchemas } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const querySchema = commonSchemas.pagination.extend({
  roomId: z.string().min(1, "Room ID is required"),
  before: z.string().optional(), // For pagination - get messages before this ID
  limit: z.coerce.number().min(1).max(100).optional().default(50)
})

const createMessageSchema = z.object({
  roomId: z.string().min(1, "Room ID is required"),
  message: z.string().min(1, "Message cannot be empty").max(2000, "Message too long"),
  type: z.enum(['text', 'image', 'file']).optional().default('text')
})

// GET /api/chat/messages - Get chat messages for a room
export const GET = createAPIHandler(
  {
    requireAuth: true,
    validateQuery: querySchema
  },
  async (request: NextRequest, { user, query }) => {
    // Extract query parameters manually if query is undefined
    const url = new URL(request.url)
    const roomId = query?.roomId || url.searchParams.get('roomId')
    const before = query?.before || url.searchParams.get('before')
    const limit = query?.limit || parseInt(url.searchParams.get('limit') || '50')

    if (!roomId) {
      return APIResponse.error('Room ID is required', 400)
    }

    // Build where clause for pagination
    const whereClause: any = {
      roomId
    }

    if (before) {
      whereClause.id = {
        lt: before
      }
    }

    const messages = await prisma.chatMessage.findMany({
      where: whereClause,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            image: true,
            role: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: limit
    })

    // Reverse to get chronological order (oldest first)
    const chronologicalMessages = messages.reverse()

    return APIResponse.success({
      messages: chronologicalMessages,
      hasMore: messages.length === limit,
      nextCursor: messages.length > 0 ? messages[0].id : null
    }, `Retrieved ${chronologicalMessages.length} messages`)
  }
)

// POST /api/chat/messages - Send a new chat message
export const POST = createAPIHandler(
  {
    requireAuth: true,
    validateBody: createMessageSchema
  },
  async (request: NextRequest, { user, body }) => {
    const { roomId, message, type } = body

    // Validate room access (you might want to add room-specific permissions here)
    // For now, we'll allow any authenticated user to send messages to any room

    // Create the message
    const chatMessage = await prisma.chatMessage.create({
      data: {
        roomId,
        userId: user.id,
        message,
        type
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            image: true,
            role: true
          }
        }
      }
    })

    return APIResponse.success(chatMessage, 'Message sent successfully')
  }
)

// DELETE /api/chat/messages/[id] - Delete a message (only message author or admin)
export const DELETE = createAPIHandler(
  {
    requireAuth: true
  },
  async (request: NextRequest, { user }) => {
    const url = new URL(request.url)
    const messageId = url.pathname.split('/').pop()

    if (!messageId) {
      return APIResponse.error('Message ID is required', 400)
    }

    // Find the message
    const message = await prisma.chatMessage.findUnique({
      where: { id: messageId }
    })

    if (!message) {
      return APIResponse.error('Message not found', 404)
    }

    // Check if user can delete this message (author or admin)
    if (message.userId !== user.id && user.role !== 'ADMIN') {
      return APIResponse.error('You can only delete your own messages', 403)
    }

    // Delete the message
    await prisma.chatMessage.delete({
      where: { id: messageId }
    })

    return APIResponse.success(null, 'Message deleted successfully')
  }
)
