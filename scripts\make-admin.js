const { PrismaClient } = require('../lib/generated/prisma');

const prisma = new PrismaClient();

async function makeAdmin() {
  try {
    // Update the current user (<PERSON>) to admin role
    const user = await prisma.user.update({
      where: {
        email: '<EMAIL>'
      },
      data: {
        role: 'ADMIN'
      }
    });
    
    console.log('✅ User updated to admin:', user);
    
    // Also create a dedicated admin user for testing
    try {
      const adminUser = await prisma.user.create({
        data: {
          name: 'Admin User',
          email: '<EMAIL>',
          role: 'ADMIN',
          bio: 'Test admin user',
          points: 0,
          level: 1,
        },
      });
      console.log('✅ Admin user created:', adminUser);
    } catch (error) {
      if (error.code === 'P2002') {
        console.log('ℹ️ Admin user already exists');
      } else {
        console.error('❌ Error creating admin user:', error);
      }
    }
    
  } catch (error) {
    console.error('❌ Error updating user:', error);
  } finally {
    await prisma.$disconnect();
  }
}

makeAdmin();
