# Socket Server Setup

This application includes a real-time socket server for notifications, live quiz sessions, and real-time monitoring.

## Quick Start

### 1. Install Dependencies
```bash
npm install
```

### 2. Run Both Servers (Recommended)
```bash
npm run dev:full
```
This runs both the Next.js app (port 3000) and the Socket server (port 3001) concurrently.

### 3. Or Run Separately

**Terminal 1 - Next.js App:**
```bash
npm run dev
```

**Terminal 2 - Socket Server:**
```bash
npm run dev:socket
```

## Features

### ✅ Real-time Notifications
- Admin can send notifications to specific users or broadcast to all
- Toast notifications appear instantly
- Notification history and management

### ✅ Live Quiz Sessions
- Real-time quiz progress updates
- Live participant tracking
- Quiz room management

### ✅ Connection Monitoring
- Live user connection status
- Real-time metrics dashboard
- Connection health monitoring

### ✅ Chat System
- Real-time messaging
- Room-based chat
- Message history

## Usage

### Admin Real-time Dashboard
1. Navigate to `/admin/realtime`
2. The page will automatically connect to the socket server
3. Use the interface to:
   - Send test notifications
   - Monitor live connections
   - View real-time metrics
   - Test quiz progress updates

### Sending Notifications
```javascript
// Broadcast to all users
socketClient.broadcastNotification({
  type: 'info',
  title: 'System Update',
  message: 'The system will be updated in 10 minutes',
  data: { priority: 'high' }
})

// Send to specific user
socketClient.sendNotification({
  targetUserId: 'user-123',
  type: 'success',
  title: 'Quiz Completed',
  message: 'You scored 95%!',
  data: { score: 95, quizId: 'quiz-456' }
})
```

### Quiz Progress Updates
```javascript
socketClient.sendQuizProgress({
  quizId: 'quiz-123',
  questionIndex: 5,
  timeRemaining: 120,
  answered: true
})
```

## Environment Variables

Add to your `.env.local`:
```env
# Socket server configuration
SOCKET_PORT=3001
NEXT_PUBLIC_SOCKET_URL=http://localhost:3001

# Enable socket features
NEXT_PUBLIC_SOCKET_ENABLED=true
```

## Production Deployment

For production, you'll need to:

1. **Deploy Socket Server Separately:**
   ```bash
   npm run start:socket
   ```

2. **Update Environment Variables:**
   ```env
   NEXT_PUBLIC_SOCKET_URL=wss://your-socket-server.com
   SOCKET_PORT=3001
   ```

3. **Use Process Manager (PM2):**
   ```bash
   pm2 start server/socket-server.js --name "socket-server"
   pm2 start npm --name "nextjs-app" -- start
   ```

## Troubleshooting

### Socket Connection Issues
- Ensure both servers are running
- Check firewall settings for port 3001
- Verify CORS configuration in socket server

### Authentication Issues
- Check user data is properly set in localStorage
- Verify JWT tokens if using authentication
- Check browser console for connection errors

### Performance Issues
- Monitor connected users count
- Check memory usage of socket server
- Consider using Redis for scaling

## Architecture

```
┌─────────────────┐    ┌─────────────────┐
│   Next.js App   │    │  Socket Server  │
│   (Port 3000)   │◄──►│   (Port 3001)   │
└─────────────────┘    └─────────────────┘
         │                       │
         ▼                       ▼
┌─────────────────┐    ┌─────────────────┐
│   Web Browser   │    │   Socket.IO     │
│   (Client)      │◄──►│   (WebSocket)   │
└─────────────────┘    └─────────────────┘
```

## API Events

### Client → Server
- `authenticate` - User authentication
- `send_notification` - Send notification
- `quiz_progress` - Quiz progress update
- `join_quiz` - Join quiz room
- `leave_quiz` - Leave quiz room
- `chat_message` - Send chat message
- `get_metrics` - Request real-time metrics

### Server → Client
- `connected` - Connection established
- `authenticated` - Authentication successful
- `notification` - Receive notification
- `notification_sent` - Notification delivery confirmation
- `quiz_progress_update` - Quiz progress update
- `chat_message` - Receive chat message
- `metrics_update` - Real-time metrics
- `user:joined` - User joined notification
- `user:left` - User left notification

## Security

- CORS configured for your domain
- User authentication required
- Role-based access control
- Rate limiting on sensitive operations
- Input validation on all events

## Support

If you encounter issues:
1. Check the browser console for errors
2. Verify both servers are running
3. Check network connectivity
4. Review the socket server logs
