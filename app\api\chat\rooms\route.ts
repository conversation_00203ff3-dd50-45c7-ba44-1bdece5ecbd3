import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'

// GET /api/chat/rooms - Get available chat rooms
export const GET = createAPIHandler(
  {
    requireAuth: true
  },
  async (request: NextRequest, { user }) => {
    // Define available chat rooms based on user role
    const baseRooms = [
      {
        id: 'student-general',
        name: 'General Discussion',
        description: 'General chat for all students',
        type: 'public',
        allowedRoles: ['STUDENT', 'ADMIN']
      },
      {
        id: 'student-study-help',
        name: 'Study Help & Questions',
        description: 'Get help with your studies',
        type: 'public',
        allowedRoles: ['STUDENT', 'ADMIN']
      },
      {
        id: 'student-quiz-discussion',
        name: 'Quiz Discussion',
        description: 'Discuss quiz strategies and concepts',
        type: 'quiz',
        allowedRoles: ['STUDENT', 'ADMIN']
      }
    ]

    // Add admin-only rooms if user is admin
    if (user.role === 'ADMIN') {
      baseRooms.push(
        {
          id: 'general',
          name: 'Admin General',
          description: 'General admin discussion',
          type: 'public',
          allowedRoles: ['ADMIN']
        },
        {
          id: 'quiz-help',
          name: 'Admin Quiz Help',
          description: 'Admin quiz management discussions',
          type: 'quiz',
          allowedRoles: ['ADMIN']
        }
      )
    }

    // Filter rooms based on user role
    const availableRooms = baseRooms.filter(room => 
      room.allowedRoles.includes(user.role)
    )

    // Get recent message counts for each room
    const roomsWithStats = await Promise.all(
      availableRooms.map(async (room) => {
        const messageCount = await prisma.chatMessage.count({
          where: { roomId: room.id }
        })

        const lastMessage = await prisma.chatMessage.findFirst({
          where: { roomId: room.id },
          include: {
            user: {
              select: {
                id: true,
                name: true,
                image: true
              }
            }
          },
          orderBy: { createdAt: 'desc' }
        })

        return {
          ...room,
          messageCount,
          lastMessage,
          lastActivity: lastMessage?.createdAt || null
        }
      })
    )

    return APIResponse.success({
      rooms: roomsWithStats
    }, `Retrieved ${roomsWithStats.length} available chat rooms`)
  }
)
