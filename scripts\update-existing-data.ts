import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function updateExistingData() {
  console.log('Updating existing quiz attempts with calculated stats...')

  // Get all quiz attempts
  const attempts = await prisma.quizAttempt.findMany({
    include: {
      quiz: {
        include: {
          questions: true
        }
      }
    }
  })

  for (const attempt of attempts) {
    const userAnswers = attempt.answers as any || {}
    const questions = attempt.quiz.questions
    
    let correctAnswers = 0
    let incorrectAnswers = 0
    let unansweredQuestions = 0
    
    for (const question of questions) {
      const userAnswer = userAnswers[question.id]
      
      if (userAnswer === undefined || userAnswer === null || userAnswer === '') {
        unansweredQuestions++
      } else if (userAnswer === question.correctAnswer) {
        correctAnswers++
      } else {
        incorrectAnswers++
      }
    }
    
    // Update the attempt with calculated stats
    await prisma.quizAttempt.update({
      where: { id: attempt.id },
      data: {
        correctAnswers,
        incorrectAnswers,
        unansweredQuestions,
        totalQuestions: questions.length
      }
    })
    
    console.log(`Updated attempt ${attempt.id}: ${correctAnswers}/${questions.length} correct`)
  }

  console.log('Updating existing questions with default difficulty and tags...')

  // Update all questions to have default difficulty and tags
  await prisma.question.updateMany({
    data: {
      difficulty: 'MEDIUM',
      tags: ['General']
    }
  })

  console.log('Data update completed!')
}

updateExistingData()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
