import { NextRequest } from 'next/server'
import { z } from 'zod'
 import { createAPIHandler, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'

const updateUserSchema = z.object({
  name: z.string().min(1, "Name is required").optional(),
  email: z.string().email("Valid email is required").optional(),
  role: z.enum(['STUDENT', 'ADMIN']).optional(),
  bio: z.string().optional()
})

// GET /api/admin/users/[id] - Get specific user
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest, { params }: { params: Promise<{ id: string } > }) => {
    const { id } = await params
    const userId = id

    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        quizAttempts: {
          select: {
            id: true,
            startedAt: true,
            completedAt: true,
            score: true,
            percentage: true,
            isCompleted: true,
            quiz: {
              select: {
                id: true,
                title: true
              }
            }
          },
          orderBy: {
            startedAt: 'desc'
          },
          take: 10
        },
        quizzes: {
          select: {
            id: true,
            title: true,
            createdAt: true,
            isPublished: true,
            _count: {
              select: {
                attempts: true
              }
            }
          },
          orderBy: {
            createdAt: 'desc'
          },
          take: 10
        },
        _count: {
          select: {
            quizAttempts: true,
            quizzes: true
          }
        }
      }
    })

    if (!user) {
      return APIResponse.error('User not found', 404, 'USER_NOT_FOUND')
    }

    // Calculate additional stats
    const avgScoreResult = await prisma.quizAttempt.aggregate({
      where: {
        userId: user.id,
        isCompleted: true
      },
      _avg: {
        percentage: true
      }
    })

    const lastAttempt = user.quizAttempts[0]
    const lastActive = lastAttempt 
      ? lastAttempt.startedAt 
      : user.updatedAt

    const userWithStats = {
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role,
      bio: user.bio,
      image: user.image,
      points: user.points,
      level: user.level,
      createdAt: user.createdAt.toISOString(),
      updatedAt: user.updatedAt.toISOString(),
      lastActive: lastActive.toISOString(),
      stats: {
        quizzesCompleted: user._count.quizAttempts,
        quizzesCreated: user._count.quizzes,
        averageScore: Math.round(avgScoreResult._avg.percentage || 0)
      },
      recentAttempts: user.quizAttempts.map(attempt => ({
        id: attempt.id,
        quiz: attempt.quiz,
        score: attempt.score,
        percentage: attempt.percentage,
        isCompleted: attempt.isCompleted,
        startedAt: attempt.startedAt.toISOString(),
        completedAt: attempt.completedAt?.toISOString()
      })),
      createdQuizzes: user.quizzes.map(quiz => ({
        id: quiz.id,
        title: quiz.title,
        isPublished: quiz.isPublished,
        createdAt: quiz.createdAt.toISOString(),
        attemptCount: quiz._count.attempts
      }))
    }

    return APIResponse.success(userWithStats, 'User retrieved successfully')
  }
)

// PUT /api/admin/users/[id] - Update user
export const PUT = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
     
    validateBody: updateUserSchema
  },
  async (request: NextRequest, { params, validatedBody, user }: { params: Promise<{ id: string } >, validatedBody: any, user: any }) => {
    const { id } = await params
    const userId = id
    const { name, email, role, bio } = validatedBody

    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id: userId }
    })

    if (!existingUser) {
      return APIResponse.error('User not found', 404, 'USER_NOT_FOUND')
    }

    // Prevent admin from changing their own role to STUDENT if they're the only admin
    if (userId === user.id && role === 'STUDENT' && existingUser.role === 'ADMIN') {
      const adminCount = await prisma.user.count({
        where: { role: 'ADMIN' }
      })

      if (adminCount <= 1) {
        return APIResponse.error(
          'Cannot change role. At least one admin must remain.',
          400,
          'CANNOT_REMOVE_LAST_ADMIN'
        )
      }
    }

    // Check if email is already taken by another user
    if (email && email !== existingUser.email) {
      const emailExists = await prisma.user.findUnique({
        where: { email }
      })

      if (emailExists) {
        return APIResponse.error(
          'Email is already taken by another user',
          400,
          'EMAIL_TAKEN'
        )
      }
    }

    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: {
        ...(name && { name }),
        ...(email && { email }),
        ...(role && { role }),
        ...(bio !== undefined && { bio: bio || null })
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        bio: true,
        image: true,
        points: true,
        level: true,
        createdAt: true,
        updatedAt: true
      }
    })

    return APIResponse.success(
      {
        ...updatedUser,
        createdAt: updatedUser.createdAt.toISOString(),
        updatedAt: updatedUser.updatedAt.toISOString()
      },
      'User updated successfully'
    )
  }
)

// DELETE /api/admin/users/[id] - Delete user
export const DELETE = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest, { params, user }: { params: Promise<{ id: string } >, user: any }) => {
    const { id } = await params
    const userId = id

    // Prevent admin from deleting themselves
    if (userId === user.id) {
      return APIResponse.error(
        'Cannot delete your own account',
        400,
        'CANNOT_DELETE_SELF'
      )
    }

    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id: userId }
    })

    if (!existingUser) {
      return APIResponse.error('User not found', 404, 'USER_NOT_FOUND')
    }

    // Prevent deletion of the last admin
    if (existingUser.role === 'ADMIN') {
      const adminCount = await prisma.user.count({
        where: { role: 'ADMIN' }
      })

      if (adminCount <= 1) {
        return APIResponse.error(
          'Cannot delete the last admin user',
          400,
          'CANNOT_DELETE_LAST_ADMIN'
        )
      }
    }

    await prisma.user.delete({
      where: { id: userId }
    })

    return APIResponse.success(
      { deletedUserId: userId },
      'User deleted successfully'
    )
  }
)
