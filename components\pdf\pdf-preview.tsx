"use client"

import { useState, useEffect } from "react"
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Eye,
  Download,
  ZoomIn,
  ZoomOut,
  RotateCw,
  Maximize2,
  Minimize2,
  FileText,
  Award,
  BarChart3,
  Loader2,
  Check
} from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import { toast } from "@/lib/toast-utils"

interface PDFPreviewProps {
  type: 'quiz-result' | 'analytics' | 'certificate'
  data: any
  template?: string
  className?: string
}

export function PDFPreview({ type, data, template = 'modern', className = "" }: PDFPreviewProps) {
  const [isGenerating, setIsGenerating] = useState(false)
  const [pdfUrl, setPdfUrl] = useState<string | null>(null)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [zoom, setZoom] = useState(100)

  const getPreviewTitle = () => {
    switch (type) {
      case 'quiz-result':
        return 'Quiz Result Preview'
      case 'analytics':
        return 'Analytics Report Preview'
      case 'certificate':
        return 'Certificate Preview'
      default:
        return 'PDF Preview'
    }
  }

  const getPreviewIcon = () => {
    switch (type) {
      case 'quiz-result':
        return <FileText className="h-5 w-5" />
      case 'analytics':
        return <BarChart3 className="h-5 w-5" />
      case 'certificate':
        return <Award className="h-5 w-5" />
      default:
        return <Eye className="h-5 w-5" />
    }
  }

  const generatePreview = async () => {
    setIsGenerating(true)
    try {
      // Import enhanced PDF generator dynamically to avoid SSR issues
      const { generateQuizResultPDF, generateAnalyticsReportPDF, generateCertificatePDF } = await import('@/lib/enhanced-pdf-generator')

      let blob: Blob

      const templateOptions = { template: template as any }

      switch (type) {
        case 'quiz-result':
          blob = await generateQuizResultPDF(data, templateOptions)
          break
        case 'analytics':
          blob = await generateAnalyticsReportPDF(data.analytics, data.studentName, templateOptions)
          break
        case 'certificate':
          blob = await generateCertificatePDF(data, templateOptions)
        default:
          throw new Error('Invalid preview type')
      }

      const url = URL.createObjectURL(blob)
      setPdfUrl(url)

    } catch (error) {
      console.error('Preview generation error:', error)
      toast.error('Failed to generate preview')
    } finally {
      setIsGenerating(false)
    }
  }

  useEffect(() => {
    return () => {
      if (pdfUrl) {
        URL.revokeObjectURL(pdfUrl)
      }
    }
  }, [pdfUrl])

  const downloadPDF = () => {
    if (!pdfUrl) return
    
    const link = document.createElement('a')
    link.href = pdfUrl
    link.download = `${type}-${new Date().toISOString().split('T')[0]}.pdf`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    toast.success('PDF downloaded successfully!')
  }

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen)
  }

  const zoomIn = () => {
    setZoom(prev => Math.min(prev + 25, 200))
  }

  const zoomOut = () => {
    setZoom(prev => Math.max(prev - 25, 50))
  }

  const resetZoom = () => {
    setZoom(100)
  }

  return (
    <div className={className}>
      <Card className={isFullscreen ? 'fixed inset-4 z-50' : ''}>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {getPreviewIcon()}
              <div>
                <CardTitle>{getPreviewTitle()}</CardTitle>
                <CardDescription>
                  Preview your PDF before downloading
                </CardDescription>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              {pdfUrl && (
                <>
                  <Badge variant="outline">{zoom}%</Badge>
                  <Button variant="ghost" size="sm" onClick={zoomOut}>
                    <ZoomOut className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="sm" onClick={resetZoom}>
                    Reset
                  </Button>
                  <Button variant="ghost" size="sm" onClick={zoomIn}>
                    <ZoomIn className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="sm" onClick={toggleFullscreen}>
                    {isFullscreen ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
                  </Button>
                  <Button variant="outline" size="sm" onClick={downloadPDF}>
                    <Download className="h-4 w-4 mr-2" />
                    Download
                  </Button>
                </>
              )}
            </div>
          </div>
        </CardHeader>

        <CardContent className="p-0">
          {!pdfUrl ? (
            <div className="text-center py-12 border-t">
              <div className="space-y-4">
                {isGenerating ? (
                  <>
                    <Loader2 className="h-12 w-12 animate-spin mx-auto text-primary" />
                    <p className="text-lg font-medium">Generating PDF Preview...</p>
                    <p className="text-sm text-muted-foreground">
                      This may take a few seconds
                    </p>
                  </>
                ) : (
                  <>
                    <Eye className="h-12 w-12 mx-auto text-muted-foreground" />
                    <p className="text-lg font-medium">PDF Preview</p>
                    <p className="text-sm text-muted-foreground">
                      Click the button below to generate a preview
                    </p>
                    <Button onClick={generatePreview}>
                      <Eye className="h-4 w-4 mr-2" />
                      Generate Preview
                    </Button>
                  </>
                )}
              </div>
            </div>
          ) : (
            <div className="border-t">
              <div 
                className="overflow-auto"
                style={{ 
                  height: isFullscreen ? 'calc(100vh - 200px)' : '600px',
                  transform: `scale(${zoom / 100})`,
                  transformOrigin: 'top left'
                }}
              >
                <iframe
                  src={pdfUrl}
                  className="w-full h-full border-0"
                  title="PDF Preview"
                />
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Fullscreen overlay */}
      {isFullscreen && (
        <div 
          className="fixed inset-0 bg-black/50 z-40"
          onClick={toggleFullscreen}
        />
      )}
    </div>
  )
}

// Specialized preview components
export function QuizResultPreview({ 
  result, 
  className = "" 
}: { 
  result: any
  className?: string 
}) {
  return (
    <PDFPreview
      type="quiz-result"
      data={result}
      className={className}
    />
  )
}

export function AnalyticsPreview({ 
  analytics, 
  studentName,
  className = "" 
}: { 
  analytics: any
  studentName: string
  className?: string 
}) {
  return (
    <PDFPreview
      type="analytics"
      data={{ analytics, studentName }}
      className={className}
    />
  )
}

export function CertificatePreview({ 
  certificateData,
  className = "" 
}: { 
  certificateData: any
  className?: string 
}) {
  return (
    <PDFPreview
      type="certificate"
      data={certificateData}
      className={className}
    />
  )
}

// PDF Template Selector
export function PDFTemplateSelector({
  onTemplateSelect,
  selectedTemplate = 'modern'
}: {
  onTemplateSelect: (template: string) => void
  selectedTemplate?: string
}) {
  const templates = [
    {
      id: 'modern',
      name: 'Modern Professional',
      description: 'Clean design with gradient headers and modern typography',
      preview: '/templates/modern-preview.png',
      color: 'from-blue-500 to-purple-600',
      features: ['Gradient Headers', 'Modern Typography', 'Clean Layout']
    },
    {
      id: 'classic',
      name: 'Academic Classic',
      description: 'Traditional academic style with serif fonts',
      preview: '/templates/classic-preview.png',
      color: 'from-slate-600 to-slate-800',
      features: ['Serif Typography', 'Formal Layout', 'Academic Style']
    },
    {
      id: 'minimal',
      name: 'Minimal Clean',
      description: 'Ultra-clean design with maximum readability',
      preview: '/templates/minimal-preview.png',
      color: 'from-gray-400 to-gray-600',
      features: ['Minimal Design', 'High Readability', 'Space Efficient']
    },
    {
      id: 'corporate',
      name: 'Corporate Elite',
      description: 'Professional corporate design with brand elements',
      preview: '/templates/corporate-preview.png',
      color: 'from-emerald-500 to-teal-600',
      features: ['Brand Elements', 'Corporate Style', 'Professional']
    },
    {
      id: 'creative',
      name: 'Creative Modern',
      description: 'Vibrant and creative design with visual elements',
      preview: '/templates/creative-preview.png',
      color: 'from-pink-500 to-orange-500',
      features: ['Creative Layout', 'Visual Elements', 'Vibrant Colors']
    },
    {
      id: 'certificate',
      name: 'Certificate Premium',
      description: 'Elegant certificate design with decorative borders',
      preview: '/templates/certificate-preview.png',
      color: 'from-amber-500 to-yellow-600',
      features: ['Decorative Borders', 'Premium Look', 'Certificate Style']
    }
  ]

  return (
    <Card className="overflow-hidden">
      <CardHeader className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950 dark:to-purple-950">
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          PDF Template Selection
        </CardTitle>
        <CardDescription>
          Choose from our professionally designed templates for different document types
        </CardDescription>
      </CardHeader>
      <CardContent className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {templates.map((template) => (
            <motion.div
              key={template.id}
              whileHover={{ scale: 1.02, y: -2 }}
              whileTap={{ scale: 0.98 }}
              className={`relative border-2 rounded-xl p-4 cursor-pointer transition-all duration-200 ${
                selectedTemplate === template.id
                  ? 'border-primary shadow-lg ring-2 ring-primary/20'
                  : 'border-border hover:border-primary/50 hover:shadow-md'
              }`}
              onClick={() => onTemplateSelect(template.id)}
            >
              {/* Selection Indicator */}
              {selectedTemplate === template.id && (
                <div className="absolute -top-2 -right-2 bg-primary text-primary-foreground rounded-full p-1">
                  <Check className="h-4 w-4" />
                </div>
              )}

              {/* Template Preview */}
              <div className={`aspect-[3/4] bg-gradient-to-br ${template.color} rounded-lg mb-4 flex items-center justify-center relative overflow-hidden`}>
                <div className="absolute inset-0 bg-white/10 backdrop-blur-sm" />
                <FileText className="h-12 w-12 text-white relative z-10" />
                <div className="absolute bottom-2 left-2 right-2">
                  <div className="bg-white/20 backdrop-blur-sm rounded px-2 py-1">
                    <div className="h-1 bg-white/60 rounded mb-1" />
                    <div className="h-1 bg-white/40 rounded w-3/4" />
                  </div>
                </div>
              </div>

              {/* Template Info */}
              <div className="space-y-2">
                <h4 className="font-semibold text-lg">{template.name}</h4>
                <p className="text-sm text-muted-foreground leading-relaxed">
                  {template.description}
                </p>

                {/* Features */}
                <div className="flex flex-wrap gap-1">
                  {template.features.map((feature, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {feature}
                    </Badge>
                  ))}
                </div>
              </div>

              {/* Hover Effect */}
              <div className="absolute inset-0 bg-primary/5 opacity-0 hover:opacity-100 transition-opacity rounded-xl" />
            </motion.div>
          ))}
        </div>

        {/* Template Categories */}
        <div className="mt-8 pt-6 border-t">
          <h4 className="font-medium mb-4">Template Categories</h4>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            <div className="flex items-center gap-2 p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg">
              <FileText className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium">Quiz Results</span>
            </div>
            <div className="flex items-center gap-2 p-3 bg-purple-50 dark:bg-purple-950/20 rounded-lg">
              <BarChart3 className="h-4 w-4 text-purple-600" />
              <span className="text-sm font-medium">Analytics</span>
            </div>
            <div className="flex items-center gap-2 p-3 bg-amber-50 dark:bg-amber-950/20 rounded-lg">
              <Award className="h-4 w-4 text-amber-600" />
              <span className="text-sm font-medium">Certificates</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

// PDF Export Queue
export function PDFExportQueue({ 
  queue 
}: { 
  queue: Array<{
    id: string
    type: string
    status: 'pending' | 'processing' | 'completed' | 'failed'
    progress: number
    filename: string
  }>
}) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Download className="h-5 w-5" />
          Export Queue
        </CardTitle>
        <CardDescription>
          Track your PDF export progress
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <AnimatePresence>
            {queue.map((item) => (
              <motion.div
                key={item.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 20 }}
                className="flex items-center gap-3 p-3 border rounded-lg"
              >
                <div className="flex-shrink-0">
                  {item.status === 'processing' ? (
                    <Loader2 className="h-4 w-4 animate-spin text-blue-600" />
                  ) : item.status === 'completed' ? (
                    <Download className="h-4 w-4 text-green-600" />
                  ) : item.status === 'failed' ? (
                    <FileText className="h-4 w-4 text-red-600" />
                  ) : (
                    <FileText className="h-4 w-4 text-gray-600" />
                  )}
                </div>
                
                <div className="flex-1">
                  <p className="font-medium text-sm">{item.filename}</p>
                  <div className="flex items-center gap-2 mt-1">
                    <div className="flex-1 bg-muted rounded-full h-2">
                      <div 
                        className="bg-primary h-2 rounded-full transition-all duration-300"
                        style={{ width: `${item.progress}%` }}
                      />
                    </div>
                    <span className="text-xs text-muted-foreground">
                      {item.progress}%
                    </span>
                  </div>
                </div>
                
                <Badge variant={
                  item.status === 'completed' ? 'default' :
                  item.status === 'processing' ? 'secondary' :
                  item.status === 'failed' ? 'destructive' : 'outline'
                }>
                  {item.status}
                </Badge>
              </motion.div>
            ))}
          </AnimatePresence>
          
          {queue.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              <Download className="h-12 w-12 mx-auto mb-3 opacity-50" />
              <p>No exports in queue</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
