"use client"

import { useEffect, useState } from 'react'
import { useStore } from './store'

/**
 * Custom hook to handle Zustand store hydration safely
 * Prevents hydration mismatches by only accessing store after client-side hydration
 */
export function useStoreHydration() {
  const [isHydrated, setIsHydrated] = useState(false)

  useEffect(() => {
    // Mark as hydrated after first render
    setIsHydrated(true)
  }, [])

  return isHydrated
}

/**
 * Safe auth hook that prevents hydration issues
 */
export function useSafeAuth() {
  const isHydrated = useStoreHydration()
  const auth = useStore((state) => ({
    user: state.user,
    isAuthenticated: state.isAuthenticated,
    isLoading: state.isLoading,
    setUser: state.setUser,
    setLoading: state.setLoading,
    signOut: state.signOut,
  }))

  // Return default values during SSR/hydration
  if (!isHydrated) {
    return {
      user: null,
      isAuthenticated: false,
      isLoading: true,
      setUser: () => {},
      setLoading: () => {},
      signOut: () => {},
    }
  }

  return auth
}

/**
 * Safe quiz hook that prevents hydration issues
 */
export function useSafeQuiz() {
  const isHydrated = useStoreHydration()
  const quiz = useStore((state) => ({
    questions: state.questions,
    currentQuestionIndex: state.currentQuestionIndex,
    answers: state.answers,
    isSubmitted: state.isSubmitted,
    score: state.score,
    title: state.title,
    setQuestions: state.setQuestions,
    setCurrentQuestionIndex: state.setCurrentQuestionIndex,
    setAnswer: state.setAnswer,
    setIsSubmitted: state.setIsSubmitted,
    setScore: state.setScore,
    setTitle: state.setTitle,
    resetQuiz: state.resetQuiz,
  }))

  // Return default values during SSR/hydration
  if (!isHydrated) {
    return {
      questions: [],
      currentQuestionIndex: 0,
      answers: {},
      isSubmitted: false,
      score: 0,
      title: '',
      setQuestions: () => {},
      setCurrentQuestionIndex: () => {},
      setAnswer: () => {},
      setIsSubmitted: () => {},
      setScore: () => {},
      setTitle: () => {},
      resetQuiz: () => {},
    }
  }

  return quiz
}

/**
 * Safe UI hook that prevents hydration issues
 */
export function useSafeUI() {
  const isHydrated = useStoreHydration()
  const ui = useStore((state) => ({
    theme: state.theme,
    sidebarOpen: state.sidebarOpen,
    isLoading: state.isLoading,
    notifications: state.notifications,
    setTheme: state.setTheme,
    setSidebarOpen: state.setSidebarOpen,
    setIsLoading: state.setIsLoading,
    addNotification: state.addNotification,
    removeNotification: state.removeNotification,
  }))

  // Return default values during SSR/hydration
  if (!isHydrated) {
    return {
      theme: 'system' as const,
      sidebarOpen: false,
      isLoading: false,
      notifications: [],
      setTheme: () => {},
      setSidebarOpen: () => {},
      setIsLoading: () => {},
      addNotification: () => {},
      removeNotification: () => {},
    }
  }

  return ui
}
