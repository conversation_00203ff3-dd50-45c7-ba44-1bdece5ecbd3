import { NextRequest } from 'next/server'
import { createAPIHandler, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'

// GET /api/student/quizzes/[id]/favorite - Check if quiz is favorited
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT'
  },
  async (request: NextRequest, { user, params }) => {
    try {
      const resolvedParams = await params
      const quizId = resolvedParams?.id as string

      console.log('GET Favorite - Params:', resolvedParams)
      console.log('GET Favorite - Quiz ID:', quizId)
      console.log('GET Favorite - User ID:', user.id)

      if (!quizId) {
        console.error('GET Favorite - Missing quiz ID')
        return APIResponse.error('Quiz ID is required', 400)
      }

    const favorite = await prisma.quizFavorite.findUnique({
      where: {
        quizId_userId: {
          quizId,
          userId: user.id
        }
      }
    })

      return APIResponse.success({
        isFavorited: !!favorite,
        favoritedAt: favorite?.createdAt || null
      })
    } catch (error) {
      console.error('GET Favorite - Error:', error)
      return APIResponse.error('Failed to check favorite status', 500)
    }
  }
)

// POST /api/student/quizzes/[id]/favorite - Add quiz to favorites
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT'
  },
  async (request: NextRequest, { user, params }) => {
    const resolvedParams = await params
    const quizId = resolvedParams?.id as string

    if (!quizId) {
      return APIResponse.error('Quiz ID is required', 400)
    }

    // Check if quiz exists and is published
    const quiz = await prisma.quiz.findFirst({
      where: {
        id: quizId,
        isPublished: true
      }
    })

    if (!quiz) {
      return APIResponse.error('Quiz not found', 404)
    }

    // Check if already favorited
    const existingFavorite = await prisma.quizFavorite.findUnique({
      where: {
        quizId_userId: {
          quizId,
          userId: user.id
        }
      }
    })

    if (existingFavorite) {
      return APIResponse.error('Quiz is already in favorites', 400)
    }

    // Add to favorites
    const favorite = await prisma.quizFavorite.create({
      data: {
        quizId,
        userId: user.id
      },
      include: {
        quiz: {
          select: {
            id: true,
            title: true,
            thumbnail: true
          }
        }
      }
    })

    return APIResponse.success(favorite, 'Quiz added to favorites', 201)
  }
)

// DELETE /api/student/quizzes/[id]/favorite - Remove quiz from favorites
export const DELETE = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT'
  },
  async (request: NextRequest, { user, params }) => {
    const resolvedParams = await params
    const quizId = resolvedParams?.id as string

    if (!quizId) {
      return APIResponse.error('Quiz ID is required', 400)
    }

    // Check if favorited
    const existingFavorite = await prisma.quizFavorite.findUnique({
      where: {
        quizId_userId: {
          quizId,
          userId: user.id
        }
      }
    })

    if (!existingFavorite) {
      return APIResponse.error('Quiz is not in favorites', 400)
    }

    // Remove from favorites
    await prisma.quizFavorite.delete({
      where: {
        quizId_userId: {
          quizId,
          userId: user.id
        }
      }
    })

    return APIResponse.success({ message: 'Quiz removed from favorites' })
  }
)
