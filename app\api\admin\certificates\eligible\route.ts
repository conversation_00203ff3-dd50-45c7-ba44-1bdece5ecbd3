import { NextRequest } from 'next/server'
 import { createAPIHandler, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const getEligibleCertificatesSchema = z.object({
  quizId: z.string().optional(),
  userId: z.string().optional(),
  minScore: z.string().optional().transform(val => parseInt(val || '60') || 60),
  limit: z.string().optional().transform(val => parseInt(val || '50') || 50),
  page: z.string().optional().transform(val => parseInt(val || '1') || 1)
})

// GET /api/admin/certificates/eligible - Get quiz attempts eligible for certificates
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
     
    validateQuery: getEligibleCertificatesSchema
  },
  async (request: NextRequest, { validatedQuery }) => {
    const { quizId, userId, minScore, limit, page } = validatedQuery

    try {
      // Build where clause for eligible attempts
      const where: any = {
        isCompleted: true,
        percentage: {
          gte: minScore // Only attempts with score >= minScore are eligible
        }
      }
      
      if (quizId) {
        where.quizId = quizId
      }
      
      if (userId) {
        where.userId = userId
      }

      // Get total count for pagination
      const totalCount = await prisma.quizAttempt.count({ where })
      const totalPages = Math.ceil(totalCount / limit)
      const skip = (page - 1) * limit

      // Fetch eligible quiz attempts
      const attempts = await prisma.quizAttempt.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          quiz: {
            select: {
              id: true,
              title: true,
              description: true,
              type: true,
              difficulty: true,
              passingScore: true
            }
          }
        },
        orderBy: [
          { percentage: 'desc' }, // Best scores first
          { completedAt: 'desc' }  // Most recent first
        ],
        skip,
        take: limit
      })

      // Transform data for certificate generation
      const eligibleAttempts = attempts.map(attempt => ({
        id: attempt.id,
        quizId: attempt.quizId,
        userId: attempt.userId,
        score: attempt.score,
        totalPoints: attempt.totalPoints,
        percentage: attempt.percentage,
        timeSpent: attempt.timeSpent,
        completedAt: attempt.completedAt?.toISOString(),
        user: attempt.user,
        quiz: attempt.quiz,
        certificateData: {
          studentName: attempt.user.name || 'Unknown Student',
          studentEmail: attempt.user.email || '',
          quizTitle: attempt.quiz.title,
          quizDescription: attempt.quiz.description,
          score: attempt.score,
          totalPoints: attempt.totalPoints,
          percentage: attempt.percentage,
          difficulty: attempt.quiz.difficulty,
          completedAt: attempt.completedAt?.toISOString(),
          certificateId: `CERT-${attempt.id.slice(0, 8).toUpperCase()}`
        }
      }))

      // Group by quiz for better organization
      const groupedByQuiz = eligibleAttempts.reduce((acc: any, attempt) => {
        const quizId = attempt.quizId
        if (!acc[quizId]) {
          acc[quizId] = {
            quiz: attempt.quiz,
            attempts: []
          }
        }
        acc[quizId].attempts.push(attempt)
        return acc
      }, {})

      return APIResponse.success({
        eligibleAttempts,
        groupedByQuiz,
        pagination: {
          currentPage: page,
          totalPages,
          totalCount,
          limit,
          hasNext: page < totalPages,
          hasPrev: page > 1
        },
        criteria: {
          minScore,
          quizId: quizId || 'all',
          userId: userId || 'all'
        }
      }, 'Eligible certificate attempts retrieved successfully')

    } catch (error) {
      console.error('Error fetching eligible certificate attempts:', error)
      return APIResponse.error(
        error instanceof Error ? error.message : 'Failed to fetch eligible attempts',
        500
      )
    }
  }
)

// POST /api/admin/certificates/eligible - Generate certificates for eligible attempts
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
 
    validateBody: z.object({
      attemptIds: z.array(z.string()).min(1, 'At least one attempt ID is required'),
      template: z.string().optional().default('modern'),
      customOptions: z.object({
        headerText: z.string().optional(),
        footerText: z.string().optional(),
        logoUrl: z.string().optional(),
        backgroundColor: z.string().optional(),
        textColor: z.string().optional()
      }).optional()
    })
  },
  async (request: NextRequest, { validatedBody, user }) => {
    const { attemptIds, template, customOptions } = validatedBody

    try {
      // Fetch the attempts to generate certificates for
      const attempts = await prisma.quizAttempt.findMany({
        where: {
          id: { in: attemptIds },
          isCompleted: true
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          quiz: {
            select: {
              id: true,
              title: true,
              description: true,
              difficulty: true
            }
          }
        }
      })

      if (attempts.length === 0) {
        return APIResponse.error('No eligible attempts found', 404)
      }

      // Create PDF export records for each certificate
      const exportPromises = attempts.map(async (attempt) => {
        const filename = `certificate-${attempt.user.name?.replace(/\s+/g, '-') || 'unknown'}-${attempt.quiz.title.replace(/\s+/g, '-')}-${new Date().toISOString().split('T')[0]}.pdf`
        
        return prisma.pdfExport.create({
          data: {
            type: 'certificate',
            filename,
            size: 0, // Will be updated when PDF is generated
            status: 'pending',
            userId: user.id,
            options: JSON.stringify({
              attemptId: attempt.id,
              template,
              customOptions,
              certificateData: {
                studentName: attempt.user.name || 'Unknown Student',
                studentEmail: attempt.user.email || '',
                quizTitle: attempt.quiz.title,
                score: attempt.score,
                totalPoints: attempt.totalPoints,
                percentage: attempt.percentage,
                difficulty: attempt.quiz.difficulty,
                completedAt: attempt.completedAt?.toISOString(),
                certificateId: `CERT-${attempt.id.slice(0, 8).toUpperCase()}`
              }
            })
          }
        })
      })

      const exports = await Promise.all(exportPromises)

      return APIResponse.success({
        exports: exports.map(exp => ({
          id: exp.id,
          type: exp.type,
          filename: exp.filename,
          status: exp.status,
          createdAt: exp.createdAt.toISOString()
        })),
        message: `${exports.length} certificate(s) queued for generation`
      }, 'Certificates queued successfully')

    } catch (error) {
      console.error('Error queuing certificates:', error)
      return APIResponse.error(
        error instanceof Error ? error.message : 'Failed to queue certificates',
        500
      )
    }
  }
)
