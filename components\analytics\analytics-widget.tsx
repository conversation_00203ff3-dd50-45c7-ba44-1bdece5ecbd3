"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown,
  Users, 
  Activity,
  CheckCircle,
  Clock,
  ArrowUpRight,
  ArrowDownRight,
  Minus
} from "lucide-react"
import { motion } from "framer-motion"
import Link from "next/link"

interface AnalyticsOverview {
  totalUsers: number
  activeUsers: number
  totalQuizzes: number
  totalAttempts: number
  averageScore: number
  completionRate: number
  trends: {
    usersGrowth: number
    attemptsGrowth: number
    scoreImprovement: number
    completionGrowth: number
  }
}

interface AnalyticsWidgetProps {
  period?: '7d' | '30d' | '90d'
  showTrends?: boolean
  compact?: boolean
}

export function AnalyticsWidget({ 
  period = '30d', 
  showTrends = true,
  compact = false 
}: AnalyticsWidgetProps) {
  const [data, setData] = useState<AnalyticsOverview | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    loadAnalytics()
  }, [period])

  const loadAnalytics = async () => {
    try {
      setIsLoading(true)
      setError(null)
      
      const response = await fetch(`/api/analytics/overview?period=${period}`)
      if (response.ok) {
        const result = await response.json()
        
        // Use real data without mock trends for now
        setData({
          ...result.data.overview,
          trends: {
            usersGrowth: 0,
            attemptsGrowth: 0,
            scoreImprovement: 0,
            completionGrowth: 0
          }
        })
      } else {
        setError('Failed to load analytics data')
      }
    } catch (error) {
      console.error('Error loading analytics:', error)
      setError('Failed to load analytics data')
    } finally {
      setIsLoading(false)
    }
  }

  const formatNumber = (num: number) => {
    if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M'
    if (num >= 1000) return (num / 1000).toFixed(1) + 'K'
    return num.toString()
  }

  const getTrendIcon = (value: number) => {
    if (value > 0) return <ArrowUpRight className="h-3 w-3 text-green-600" />
    if (value < 0) return <ArrowDownRight className="h-3 w-3 text-red-600" />
    return <Minus className="h-3 w-3 text-gray-600" />
  }

  const getTrendColor = (value: number) => {
    if (value > 0) return 'text-green-600'
    if (value < 0) return 'text-red-600'
    return 'text-gray-600'
  }

  const MetricCard = ({ 
    title, 
    value, 
    icon, 
    trend, 
    color = "text-blue-600" 
  }: {
    title: string
    value: string | number
    icon: React.ReactNode
    trend?: number
    color?: string
  }) => (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.2 }}
    >
      <Card className={compact ? "p-3" : ""}>
        <CardContent className={compact ? "p-0" : "pt-6"}>
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <div className={`text-2xl font-bold ${color}`}>
                {typeof value === 'number' ? formatNumber(value) : value}
              </div>
              <p className={`text-sm text-muted-foreground ${compact ? 'text-xs' : ''}`}>
                {title}
              </p>
              {showTrends && trend !== undefined && (
                <div className="flex items-center gap-1 mt-1">
                  {getTrendIcon(trend)}
                  <span className={`text-xs ${getTrendColor(trend)}`}>
                    {Math.abs(trend).toFixed(1)}%
                  </span>
                </div>
              )}
            </div>
            <div className={color}>
              {icon}
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Analytics Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-32">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
              <p className="text-sm text-muted-foreground">Loading analytics...</p>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error || !data) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Analytics Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-32">
            <div className="text-center">
              <div className="text-red-500 mb-2">⚠️</div>
              <p className="text-sm text-muted-foreground">{error}</p>
              <Button variant="outline" size="sm" onClick={loadAnalytics} className="mt-2">
                Retry
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Analytics Overview
            </CardTitle>
            <CardDescription>
              Platform performance for the last {period === '7d' ? '7 days' : period === '30d' ? '30 days' : '90 days'}
            </CardDescription>
          </div>
          
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-xs">
              Last {period}
            </Badge>
            <Button variant="ghost" size="sm" asChild>
              <Link href="/admin/analytics-dashboard">
                View Details
              </Link>
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className={`grid grid-cols-2 ${compact ? 'lg:grid-cols-4' : 'lg:grid-cols-3'} gap-4`}>
          <MetricCard
            title="Total Users"
            value={data.totalUsers}
            icon={<Users className="h-6 w-6" />}
            trend={showTrends ? data.trends.usersGrowth : undefined}
            color="text-blue-600"
          />
          
          <MetricCard
            title="Active Users"
            value={data.activeUsers}
            icon={<Activity className="h-6 w-6" />}
            trend={showTrends ? data.trends.usersGrowth * 0.8 : undefined}
            color="text-green-600"
          />
          
          <MetricCard
            title="Quiz Attempts"
            value={data.totalAttempts}
            icon={<TrendingUp className="h-6 w-6" />}
            trend={showTrends ? data.trends.attemptsGrowth : undefined}
            color="text-purple-600"
          />
          
          <MetricCard
            title="Avg Score"
            value={`${data.averageScore.toFixed(1)}%`}
            icon={<CheckCircle className="h-6 w-6" />}
            trend={showTrends ? data.trends.scoreImprovement : undefined}
            color="text-orange-600"
          />
          
          <MetricCard
            title="Completion Rate"
            value={`${data.completionRate.toFixed(1)}%`}
            icon={<Clock className="h-6 w-6" />}
            trend={showTrends ? data.trends.completionGrowth : undefined}
            color="text-teal-600"
          />
          
          <MetricCard
            title="Total Quizzes"
            value={data.totalQuizzes}
            icon={<BarChart3 className="h-6 w-6" />}
            color="text-indigo-600"
          />
        </div>

        {showTrends && (
          <div className="mt-6 pt-4 border-t">
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Performance Trends</span>
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-1">
                  <ArrowUpRight className="h-3 w-3 text-green-600" />
                  <span className="text-green-600 text-xs">Positive</span>
                </div>
                <div className="flex items-center gap-1">
                  <ArrowDownRight className="h-3 w-3 text-red-600" />
                  <span className="text-red-600 text-xs">Negative</span>
                </div>
              </div>
            </div>
            
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 mt-3">
              <div className="text-center p-2 bg-muted rounded-lg">
                <div className={`text-sm font-medium ${getTrendColor(data.trends.usersGrowth)}`}>
                  {data.trends.usersGrowth > 0 ? '+' : ''}{data.trends.usersGrowth.toFixed(1)}%
                </div>
                <div className="text-xs text-muted-foreground">Users</div>
              </div>
              
              <div className="text-center p-2 bg-muted rounded-lg">
                <div className={`text-sm font-medium ${getTrendColor(data.trends.attemptsGrowth)}`}>
                  {data.trends.attemptsGrowth > 0 ? '+' : ''}{data.trends.attemptsGrowth.toFixed(1)}%
                </div>
                <div className="text-xs text-muted-foreground">Attempts</div>
              </div>
              
              <div className="text-center p-2 bg-muted rounded-lg">
                <div className={`text-sm font-medium ${getTrendColor(data.trends.scoreImprovement)}`}>
                  {data.trends.scoreImprovement > 0 ? '+' : ''}{data.trends.scoreImprovement.toFixed(1)}%
                </div>
                <div className="text-xs text-muted-foreground">Avg Score</div>
              </div>
              
              <div className="text-center p-2 bg-muted rounded-lg">
                <div className={`text-sm font-medium ${getTrendColor(data.trends.completionGrowth)}`}>
                  {data.trends.completionGrowth > 0 ? '+' : ''}{data.trends.completionGrowth.toFixed(1)}%
                </div>
                <div className="text-xs text-muted-foreground">Completion</div>
              </div>
            </div>
          </div>
        )}

        <div className="mt-6 pt-4 border-t">
          <div className="flex items-center justify-between">
            <p className="text-sm text-muted-foreground">
              Last updated: {new Date().toLocaleTimeString()}
            </p>
            <Button variant="ghost" size="sm" asChild>
              <Link href="/admin/analytics-dashboard">
                <BarChart3 className="h-4 w-4 mr-2" />
                Full Dashboard
              </Link>
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
