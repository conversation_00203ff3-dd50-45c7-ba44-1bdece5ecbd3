import { NextRequest } from 'next/server'
import { APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { auth } from '@/auth'

export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return APIResponse.error('Please sign in to view your schedule', 401)
    }

    const { searchParams } = new URL(request.url)
    const filter = searchParams.get('filter') || 'all'
    const timeFilter = searchParams.get('timeFilter') || 'week'

    const user = await prisma.user.findUnique({
      where: { id: session.user.id }
    })

    if (!user) {
      return APIResponse.error('User not found', 404)
    }

    // Calculate date filter based on timeFilter
    let dateFilter = {}
    const now = new Date()
    
    if (timeFilter === 'week') {
      const weekStart = new Date(now)
      weekStart.setDate(now.getDate() - now.getDay())
      weekStart.setHours(0, 0, 0, 0)
      
      const weekEnd = new Date(weekStart)
      weekEnd.setDate(weekStart.getDate() + 7)
      
      dateFilter = {
        startTime: {
          gte: weekStart,
          lt: weekEnd
        }
      }
    } else if (timeFilter === 'month') {
      const monthStart = new Date(now.getFullYear(), now.getMonth(), 1)
      const monthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 1)
      
      dateFilter = {
        startTime: {
          gte: monthStart,
          lt: monthEnd
        }
      }
    }

    // Get scheduled quizzes (quizzes with startTime and endTime)
    const scheduledQuizzes = await prisma.quiz.findMany({
      where: {
        isPublished: true,
        startTime: { not: null },
        endTime: { not: null },
        ...dateFilter,
        enrollments: {
          some: {
            userId: user.id
          }
        }
      },
      include: {
        creator: {
          select: {
            name: true,
            email: true
          }
        },
        questions: {
          select: {
            id: true
          }
        },
        attempts: {
          where: {
            userId: user.id
          },
          select: {
            id: true,
            score: true,
            percentage: true,
            completedAt: true
          },
          orderBy: {
            completedAt: 'desc'
          }
        },
        enrollments: {
          where: {
            userId: user.id
          },
          select: {
            id: true
          }
        },
        _count: {
          select: {
            enrollments: true
          }
        }
      },
      orderBy: {
        startTime: 'asc'
      }
    })

    // Transform and calculate status for each quiz
    const transformedQuizzes = scheduledQuizzes.map(quiz => {
      const now = new Date()
      const startTime = new Date(quiz.startTime!)
      const endTime = new Date(quiz.endTime!)
      
      let status: 'upcoming' | 'active' | 'completed' | 'missed'
      
      if (now < startTime) {
        status = 'upcoming'
      } else if (now >= startTime && now <= endTime) {
        status = 'active'
      } else if (quiz.attempts.length > 0) {
        status = 'completed'
      } else {
        status = 'missed'
      }

      return {
        id: quiz.id,
        title: quiz.title,
        description: quiz.description || '',
        type: quiz.type,
        difficulty: quiz.difficulty,
        startTime: quiz.startTime!.toISOString(),
        endTime: quiz.endTime!.toISOString(),
        duration: quiz.timeLimit || 60,
        questionCount: quiz.questions.length,
        isEnrolled: quiz.enrollments.length > 0,
        status,
        instructor: {
          name: quiz.creator.name || 'Unknown',
          email: quiz.creator.email || ''
        },
        enrollmentCount: quiz._count.enrollments,
        maxAttempts: quiz.maxAttempts,
        userAttempts: quiz.attempts.length
      }
    })

    // Filter by status if specified
    const filteredQuizzes = filter === 'all' 
      ? transformedQuizzes 
      : transformedQuizzes.filter(quiz => quiz.status === filter)

    return APIResponse.success(
      filteredQuizzes,
      'Schedule retrieved successfully'
    )

  } catch (error) {
    console.error('Error fetching schedule:', error)
    return APIResponse.error('Failed to fetch schedule', 500)
  }
}
