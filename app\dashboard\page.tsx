"use client"

import { useEffect } from "react"
import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { Card, CardContent } from "@/components/ui/card"

export default function DashboardRedirect() {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (status === "loading") return

    if (!session) {
      // Redirect to sign in if not authenticated
      router.push('/auth/signin')
      return
    }

    // Redirect based on user role
    if (session.user.role === 'STUDENT') {
      router.push('/student')
    } else if (session.user.role === 'ADMIN') {
      router.push('/admin')
    } else {
      // Fallback for unknown roles
      router.push('/auth/signin')
    }
  }, [session, status, router])

  // Show loading state while redirecting
  if (status === "loading") {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    )
  }

  // Show redirecting message
  return (
    <div className="min-h-screen flex items-center justify-center">
      <Card className="max-w-md">
        <CardContent className="pt-6">
          <div className="text-center space-y-4">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
            <h3 className="text-xl font-semibold">Redirecting...</h3>
            <p className="text-muted-foreground">
              Taking you to your dashboard
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
