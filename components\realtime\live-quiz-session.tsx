"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Progress } from "@/components/ui/progress"
import { 
  Users, 
  Trophy, 
  Clock, 
  Target, 
  UserPlus, 
  UserMinus,
  Play,
  Pause,
  CheckCircle,
  Circle,
  Zap
} from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import { getSocketClient, SocketUser, QuizProgress } from "@/lib/socket-client"
import { toast } from "sonner"

interface LiveQuizSessionProps {
  quizId: string
  quizTitle: string
  totalQuestions: number
  duration: number
  onJoin?: () => void
  onLeave?: () => void
  className?: string
}

interface Participant extends SocketUser {
  progress?: QuizProgress
  completed?: boolean
  score?: number
  rank?: number
}

export function LiveQuizSession({
  quizId,
  quizTitle,
  totalQuestions,
  duration,
  onJoin,
  onLeave,
  className = ""
}: LiveQuizSessionProps) {
  const [isJoined, setIsJoined] = useState(false)
  const [participants, setParticipants] = useState<Participant[]>([])
  const [participantCount, setParticipantCount] = useState(0)
  const [sessionStatus, setSessionStatus] = useState<'waiting' | 'active' | 'completed'>('waiting')
  const [currentUser, setCurrentUser] = useState<string | null>(null)

  useEffect(() => {
    // Only run on client side
    if (typeof window === 'undefined') return

    const socketClient = getSocketClient()

    // Get current user ID (you'd get this from your auth context)
    try {
      const userData = JSON.parse(localStorage.getItem('socket_user_data') || '{}')
      setCurrentUser(userData.id)
    } catch (error) {
      console.warn('Failed to parse user data from localStorage:', error)
    }

    // Quiz session events
    const handleQuizJoined = (data: {
      sessionId: string
      quizId: string
      participantCount: number
      participants: SocketUser[]
    }) => {
      setIsJoined(true)
      setParticipantCount(data.participantCount)
      setParticipants(data.participants.map(p => ({ ...p, progress: undefined })))
      toast.success('Joined quiz session successfully')
      onJoin?.()
    }

    const handleParticipantJoined = (data: {
      userId: string
      name: string
      participantCount: number
    }) => {
      setParticipantCount(data.participantCount)
      setParticipants(prev => {
        const exists = prev.find(p => p.id === data.userId)
        if (!exists) {
          return [...prev, {
            id: data.userId,
            name: data.name,
            email: '',
            role: 'student',
            socketId: '',
            joinedAt: new Date(),
            lastSeen: new Date()
          }]
        }
        return prev
      })
      
      toast(`${data.name} joined the session`, {
        icon: <UserPlus className="h-4 w-4" />
      })
    }

    const handleParticipantLeft = (data: {
      userId: string
      participantCount: number
    }) => {
      setParticipantCount(data.participantCount)
      setParticipants(prev => {
        const participant = prev.find(p => p.id === data.userId)
        if (participant) {
          toast(`${participant.name} left the session`, {
            icon: <UserMinus className="h-4 w-4" />
          })
        }
        return prev.filter(p => p.id !== data.userId)
      })
    }

    const handleParticipantProgress = (data: QuizProgress) => {
      setParticipants(prev => prev.map(p => 
        p.id === data.userId 
          ? { ...p, progress: data }
          : p
      ))
    }

    const handleParticipantCompleted = (data: {
      userId: string
      score: number
      timeSpent: number
      rank?: number
    }) => {
      setParticipants(prev => prev.map(p => 
        p.id === data.userId 
          ? { ...p, completed: true, score: data.score, rank: data.rank }
          : p
      ))

      const participant = participants.find(p => p.id === data.userId)
      if (participant) {
        toast.success(`${participant.name} completed the quiz!`, {
          description: `Score: ${data.score}% ${data.rank ? `• Rank: #${data.rank}` : ''}`
        })
      }
    }

    // Set up event listeners
    socketClient.on('quiz:joined', handleQuizJoined)
    socketClient.on('quiz:participant_joined', handleParticipantJoined)
    socketClient.on('quiz:participant_left', handleParticipantLeft)
    socketClient.on('quiz:participant_progress', handleParticipantProgress)
    socketClient.on('quiz:participant_completed', handleParticipantCompleted)

    // Cleanup
    return () => {
      socketClient.off('quiz:joined', handleQuizJoined)
      socketClient.off('quiz:participant_joined', handleParticipantJoined)
      socketClient.off('quiz:participant_left', handleParticipantLeft)
      socketClient.off('quiz:participant_progress', handleParticipantProgress)
      socketClient.off('quiz:participant_completed', handleParticipantCompleted)
    }
  }, [quizId, onJoin, participants])

  const joinSession = () => {
    const socketClient = getSocketClient()
    socketClient.joinQuiz(quizId)
  }

  const leaveSession = () => {
    const socketClient = getSocketClient()
    socketClient.leaveQuiz(quizId)
    setIsJoined(false)
    setParticipants([])
    setParticipantCount(0)
    toast.info('Left quiz session')
    onLeave?.()
  }

  const updateProgress = (questionIndex: number, timeRemaining: number, answered: boolean) => {
    if (!isJoined) return
    
    const socketClient = getSocketClient()
    socketClient.updateQuizProgress({
      quizId,
      questionIndex,
      timeRemaining,
      answered
    })
  }

  const completeQuiz = (score: number, timeSpent: number, rank?: number) => {
    if (!isJoined) return
    
    const socketClient = getSocketClient()
    socketClient.completeQuiz({
      quizId,
      score,
      timeSpent,
      rank
    })
  }

  const getProgressPercentage = (participant: Participant) => {
    if (!participant.progress) return 0
    return ((participant.progress.questionIndex + 1) / totalQuestions) * 100
  }

  const getParticipantStatus = (participant: Participant) => {
    if (participant.completed) return 'completed'
    if (participant.progress?.answered) return 'answered'
    if (participant.progress) return 'active'
    return 'waiting'
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'answered':
        return <CheckCircle className="h-4 w-4 text-blue-600" />
      case 'active':
        return <Play className="h-4 w-4 text-orange-600" />
      default:
        return <Circle className="h-4 w-4 text-gray-400" />
    }
  }

  const sortedParticipants = [...participants].sort((a, b) => {
    // Completed participants first, sorted by score
    if (a.completed && b.completed) {
      return (b.score || 0) - (a.score || 0)
    }
    if (a.completed) return -1
    if (b.completed) return 1
    
    // Then by progress
    const aProgress = a.progress?.questionIndex || 0
    const bProgress = b.progress?.questionIndex || 0
    return bProgress - aProgress
  })

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Session Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5 text-orange-500" />
                Live Quiz Session
              </CardTitle>
              <CardDescription>{quizTitle}</CardDescription>
            </div>
            
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="flex items-center gap-1">
                <Users className="h-3 w-3" />
                {participantCount} participant{participantCount !== 1 ? 's' : ''}
              </Badge>
              
              {!isJoined ? (
                <Button onClick={joinSession}>
                  <UserPlus className="h-4 w-4 mr-2" />
                  Join Session
                </Button>
              ) : (
                <Button variant="outline" onClick={leaveSession}>
                  <UserMinus className="h-4 w-4 mr-2" />
                  Leave Session
                </Button>
              )}
            </div>
          </div>
        </CardHeader>

        {isJoined && (
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">{totalQuestions}</div>
                <div className="text-sm text-muted-foreground">Questions</div>
              </div>
              <div className="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                <div className="text-2xl font-bold text-green-600">{duration}m</div>
                <div className="text-sm text-muted-foreground">Duration</div>
              </div>
              <div className="text-center p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                <div className="text-2xl font-bold text-purple-600">{participantCount}</div>
                <div className="text-sm text-muted-foreground">Live Participants</div>
              </div>
            </div>
          </CardContent>
        )}
      </Card>

      {/* Participants List */}
      {isJoined && participants.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Live Participants ({participants.length})
            </CardTitle>
            <CardDescription>
              Real-time progress of all quiz participants
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <AnimatePresence>
                {sortedParticipants.map((participant, index) => (
                  <motion.div
                    key={participant.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    transition={{ duration: 0.3, delay: 0.05 * index }}
                    className={`flex items-center gap-4 p-3 rounded-lg border transition-all ${
                      participant.id === currentUser 
                        ? 'bg-primary/5 border-primary/20' 
                        : 'hover:bg-muted/50'
                    }`}
                  >
                    <div className="flex items-center gap-3 flex-1">
                      <Avatar className="h-8 w-8">
                        <AvatarFallback>
                          {participant.name.split(' ').map(n => n[0]).join('')}
                        </AvatarFallback>
                      </Avatar>
                      
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <span className="font-medium">
                            {participant.name}
                            {participant.id === currentUser && (
                              <Badge variant="outline" className="ml-2 text-xs">You</Badge>
                            )}
                          </span>
                          {getStatusIcon(getParticipantStatus(participant))}
                        </div>
                        
                        {participant.progress && (
                          <div className="mt-1">
                            <div className="flex items-center gap-2 text-sm text-muted-foreground">
                              <span>
                                Question {participant.progress.questionIndex + 1} of {totalQuestions}
                              </span>
                              <span>•</span>
                              <span>
                                {Math.floor(participant.progress.timeRemaining / 60)}:
                                {(participant.progress.timeRemaining % 60).toString().padStart(2, '0')} left
                              </span>
                            </div>
                            <Progress 
                              value={getProgressPercentage(participant)} 
                              className="h-1 mt-1" 
                            />
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="text-right">
                      {participant.completed ? (
                        <div>
                          <div className="text-lg font-bold text-green-600">
                            {participant.score}%
                          </div>
                          {participant.rank && (
                            <div className="text-xs text-muted-foreground">
                              Rank #{participant.rank}
                            </div>
                          )}
                        </div>
                      ) : participant.progress ? (
                        <div className="text-sm text-muted-foreground">
                          {Math.round(getProgressPercentage(participant))}%
                        </div>
                      ) : (
                        <div className="text-sm text-muted-foreground">
                          Waiting...
                        </div>
                      )}
                    </div>
                  </motion.div>
                ))}
              </AnimatePresence>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Session Stats */}
      {isJoined && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-2xl font-bold">
                    {participants.filter(p => p.completed).length}
                  </div>
                  <p className="text-sm text-muted-foreground">Completed</p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-2xl font-bold">
                    {participants.filter(p => p.progress && !p.completed).length}
                  </div>
                  <p className="text-sm text-muted-foreground">In Progress</p>
                </div>
                <Play className="h-8 w-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-2xl font-bold">
                    {participants.filter(p => !p.progress).length}
                  </div>
                  <p className="text-sm text-muted-foreground">Waiting</p>
                </div>
                <Clock className="h-8 w-8 text-gray-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-2xl font-bold">
                    {participants.length > 0 
                      ? Math.round(
                          participants
                            .filter(p => p.completed && p.score)
                            .reduce((sum, p) => sum + (p.score || 0), 0) / 
                          participants.filter(p => p.completed).length || 1
                        )
                      : 0}%
                  </div>
                  <p className="text-sm text-muted-foreground">Avg Score</p>
                </div>
                <Trophy className="h-8 w-8 text-yellow-600" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
