import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'

// GET /api/admin/quizzes/popular-tags - Get popular tags
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest) => {
    try {
      // Get all tags from published quizzes
      const quizzes = await prisma.quiz.findMany({
        where: {
          isPublished: true
        },
        select: {
          tags: true
        }
      })

      // Count tag frequency
      const tagFrequency = new Map<string, number>()
      quizzes.forEach(quiz => {
        quiz.tags.forEach(tag => {
          tagFrequency.set(tag, (tagFrequency.get(tag) || 0) + 1)
        })
      })

      // Get most popular tags
      const popularTags = Array.from(tagFrequency.entries())
        .sort(([,a], [,b]) => b - a)
        .slice(0, 20)
        .map(([tag]) => tag)

      return APIResponse.success({
        tags: popularTags,
        totalTags: tagFrequency.size
      }, 'Popular tags retrieved successfully')
    } catch (error) {
      console.error('Error fetching popular tags:', error)
      return APIResponse.error('Failed to fetch popular tags', 500)
    }
  }
)
