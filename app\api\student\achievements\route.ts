import { NextRequest } from 'next/server'
 import { createAPIHandler, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'

// Helper function to get points based on rarity
function getRarityPoints(rarity: string): number {
  switch (rarity) {
    case 'common': return 10
    case 'uncommon': return 25
    case 'rare': return 50
    case 'epic': return 100
    case 'legendary': return 200
    default: return 10
  }
}

// Achievement definitions
const ACHIEVEMENT_DEFINITIONS = [
  {
    id: 'first_quiz',
    title: 'First Steps',
    description: 'Complete your first quiz',
    icon: '🎯',
    rarity: 'common',
    condition: (stats: any) => stats.totalAttempts >= 1
  },
  {
    id: 'perfect_score',
    title: 'Perfect Score',
    description: 'Score 100% on any quiz',
    icon: '⭐',
    rarity: 'epic',
    condition: (stats: any) => stats.bestScore >= 100
  },
  {
    id: 'week_warrior',
    title: 'Week Warrior',
    description: 'Complete 7 quizzes in a week',
    icon: '🔥',
    rarity: 'rare',
    condition: (stats: any) => stats.weeklyAttempts >= 7
  },
  {
    id: 'speed_demon',
    title: 'Speed Demon',
    description: 'Complete a quiz in under 5 minutes',
    icon: '⚡',
    rarity: 'rare',
    condition: (stats: any) => stats.fastestTime <= 5
  },
  {
    id: 'consistent_learner',
    title: 'Consistent Learner',
    description: 'Complete quizzes for 5 consecutive days',
    icon: '📚',
    rarity: 'uncommon',
    condition: (stats: any) => stats.consecutiveDays >= 5
  },
  {
    id: 'high_achiever',
    title: 'High Achiever',
    description: 'Maintain an average score above 90%',
    icon: '🏆',
    rarity: 'epic',
    condition: (stats: any) => stats.averageScore >= 90
  },
  {
    id: 'quiz_master',
    title: 'Quiz Master',
    description: 'Complete 50 quizzes',
    icon: '👑',
    rarity: 'legendary',
    condition: (stats: any) => stats.totalAttempts >= 50
  },
  {
    id: 'subject_expert',
    title: 'Subject Expert',
    description: 'Score above 85% in 10 quizzes of the same subject',
    icon: '🎓',
    rarity: 'rare',
    condition: (stats: any) => stats.subjectExpertise >= 10
  },
  {
    id: 'early_bird',
    title: 'Early Bird',
    description: 'Complete 5 quizzes before 9 AM',
    icon: '🌅',
    rarity: 'uncommon',
    condition: (stats: any) => stats.earlyAttempts >= 5
  },
  {
    id: 'night_owl',
    title: 'Night Owl',
    description: 'Complete 5 quizzes after 10 PM',
    icon: '🦉',
    rarity: 'uncommon',
    condition: (stats: any) => stats.lateAttempts >= 5
  },
  {
    id: 'comeback_kid',
    title: 'Comeback Kid',
    description: 'Improve your score by 30% on a retaken quiz',
    icon: '💪',
    rarity: 'rare',
    condition: (stats: any) => stats.maxImprovement >= 30
  },
  {
    id: 'marathon_runner',
    title: 'Marathon Runner',
    description: 'Spend more than 2 hours studying in a single day',
    icon: '🏃',
    rarity: 'rare',
    condition: (stats: any) => stats.maxDailyTime >= 120
  },
  {
    id: 'perfectionist',
    title: 'Perfectionist',
    description: 'Score 100% on 5 different quizzes',
    icon: '💎',
    rarity: 'legendary',
    condition: (stats: any) => stats.perfectScores >= 5
  },
  {
    id: 'social_learner',
    title: 'Social Learner',
    description: 'Complete 10 quizzes in group study sessions',
    icon: '👥',
    rarity: 'uncommon',
    condition: (stats: any) => stats.groupAttempts >= 10
  },
  {
    id: 'knowledge_seeker',
    title: 'Knowledge Seeker',
    description: 'Complete quizzes in 5 different categories',
    icon: '🔍',
    rarity: 'rare',
    condition: (stats: any) => stats.categoriesExplored >= 5
  },
  {
    id: 'time_saver',
    title: 'Time Saver',
    description: 'Complete 10 quizzes in under half the time limit',
    icon: '⏰',
    rarity: 'epic',
    condition: (stats: any) => stats.quickCompletions >= 10
  },
  {
    id: 'dedicated_student',
    title: 'Dedicated Student',
    description: 'Study for 30 consecutive days',
    icon: '📖',
    rarity: 'legendary',
    condition: (stats: any) => stats.consecutiveDays >= 30
  },
  {
    id: 'challenger',
    title: 'Challenger',
    description: 'Complete 10 hard difficulty quizzes',
    icon: '⚔️',
    rarity: 'epic',
    condition: (stats: any) => stats.hardQuizzes >= 10
  },
  {
    id: 'rookie',
    title: 'Rookie',
    description: 'Complete 5 quizzes',
    icon: '🌱',
    rarity: 'common',
    condition: (stats: any) => stats.totalAttempts >= 5
  },
  {
    id: 'scholar',
    title: 'Scholar',
    description: 'Complete 25 quizzes',
    icon: '📜',
    rarity: 'uncommon',
    condition: (stats: any) => stats.totalAttempts >= 25
  },
  {
    id: 'genius',
    title: 'Genius',
    description: 'Score above 95% average across 20 quizzes',
    icon: '🧠',
    rarity: 'legendary',
    condition: (stats: any) => stats.totalAttempts >= 20 && stats.averageScore >= 95
  }
]

// GET /api/student/achievements - Get student achievements
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT',
     
  },
  async (request: NextRequest, { user }) => {
    try {
      // Get user's quiz attempts
      const attempts = await prisma.quizAttempt.findMany({
        where: {
          userId: user.id,
          completedAt: { not: null }
        },
        include: {
          quiz: {
            select: {
              tags: true,
              difficulty: true
            }
          }
        },
        orderBy: { completedAt: 'desc' }
      })

      // Calculate user statistics for achievement checking
      const now = new Date()
      const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
      
      const weeklyAttempts = attempts.filter(attempt => 
        attempt.completedAt && attempt.completedAt >= oneWeekAgo
      ).length

      const bestScore = attempts.length > 0 
        ? Math.max(...attempts.map(a => a.percentage || 0))
        : 0

      const averageScore = attempts.length > 0
        ? Math.round(attempts.reduce((sum, a) => sum + (a.percentage || 0), 0) / attempts.length)
        : 0

      // Calculate fastest completion time (in minutes)
      const fastestTime = attempts.length > 0
        ? Math.min(...attempts.map(attempt => {
            if (!attempt.completedAt) return Infinity
            return Math.round((attempt.completedAt.getTime() - attempt.startedAt.getTime()) / (1000 * 60))
          }))
        : Infinity

      // Calculate consecutive days
      const attemptDates = attempts
        .map(a => a.completedAt?.toISOString().split('T')[0])
        .filter(Boolean)
        .filter((date, index, arr) => arr.indexOf(date) === index)
        .sort()
        .reverse()

      let consecutiveDays = 0
      let currentDate = now.toISOString().split('T')[0]
      
      for (const date of attemptDates) {
        if (date === currentDate) {
          consecutiveDays++
          const prevDate = new Date(currentDate)
          prevDate.setDate(prevDate.getDate() - 1)
          currentDate = prevDate.toISOString().split('T')[0]
        } else {
          break
        }
      }

      // Calculate subject expertise
      const subjectStats = new Map()
      attempts.forEach(attempt => {
        if ((attempt.percentage || 0) >= 85) {
          const subjects = attempt.quiz.tags || ['General']
          subjects.forEach(subject => {
            subjectStats.set(subject, (subjectStats.get(subject) || 0) + 1)
          })
        }
      })
      
      const subjectExpertise = subjectStats.size > 0 
        ? Math.max(...Array.from(subjectStats.values()))
        : 0

      const userStats = {
        totalAttempts: attempts.length,
        weeklyAttempts,
        bestScore,
        averageScore,
        fastestTime: fastestTime === Infinity ? 0 : fastestTime,
        consecutiveDays,
        subjectExpertise
      }

      // Check which achievements the user has earned
      const earnedAchievements = []
      const availableAchievements = []

      for (const achievement of ACHIEVEMENT_DEFINITIONS) {
        const isEarned = achievement.condition(userStats)
        
        if (isEarned) {
          earnedAchievements.push({
            id: achievement.id,
            title: achievement.title,
            description: achievement.description,
            icon: achievement.icon,
            rarity: achievement.rarity,
            unlockedAt: new Date().toISOString(), // In a real system, this would be stored
            progress: 100
          })
        } else {
          // Calculate progress towards achievement
          let progress = 0
          switch (achievement.id) {
            case 'first_quiz':
              progress = Math.min(100, (userStats.totalAttempts / 1) * 100)
              break
            case 'perfect_score':
              progress = Math.min(100, (userStats.bestScore / 100) * 100)
              break
            case 'week_warrior':
              progress = Math.min(100, (userStats.weeklyAttempts / 7) * 100)
              break
            case 'speed_demon':
              progress = userStats.fastestTime > 0 && userStats.fastestTime <= 5 ? 100 : 0
              break
            case 'consistent_learner':
              progress = Math.min(100, (userStats.consecutiveDays / 5) * 100)
              break
            case 'high_achiever':
              progress = Math.min(100, (userStats.averageScore / 90) * 100)
              break
            case 'quiz_master':
              progress = Math.min(100, (userStats.totalAttempts / 50) * 100)
              break
            case 'subject_expert':
              progress = Math.min(100, (userStats.subjectExpertise / 10) * 100)
              break
          }

          availableAchievements.push({
            id: achievement.id,
            title: achievement.title,
            description: achievement.description,
            icon: achievement.icon,
            rarity: achievement.rarity,
            progress: Math.round(progress)
          })
        }
      }

      // Calculate achievement statistics
      const totalAchievements = ACHIEVEMENT_DEFINITIONS.length
      const earnedCount = earnedAchievements.length
      const completionRate = Math.round((earnedCount / totalAchievements) * 100)

      // Group by rarity
      const byRarity = {
        common: earnedAchievements.filter(a => a.rarity === 'common').length,
        uncommon: earnedAchievements.filter(a => a.rarity === 'uncommon').length,
        rare: earnedAchievements.filter(a => a.rarity === 'rare').length,
        epic: earnedAchievements.filter(a => a.rarity === 'epic').length,
        legendary: earnedAchievements.filter(a => a.rarity === 'legendary').length
      }

      // Recent achievements (last 5)
      const recentAchievements = earnedAchievements.slice(0, 5)

      // Transform achievements to match frontend Achievement interface exactly
      const allAchievements = [...earnedAchievements, ...availableAchievements].map(achievement => ({
        id: achievement.id,
        title: achievement.title,
        description: achievement.description,
        icon: achievement.icon,
        category: achievement.category || 'Learning', // Default category
        rarity: achievement.rarity,
        points: getRarityPoints(achievement.rarity),
        isUnlocked: achievement.progress === 100,
        unlockedAt: achievement.progress === 100 ? achievement.unlockedAt : undefined,
        progress: achievement.progress < 100 ? {
          current: Math.floor(achievement.progress),
          required: 100
        } : undefined,
        requirements: [achievement.description] // Use description as requirement
      }))

      // Calculate total users for ranking
      const totalUsers = await prisma.user.count({ where: { role: 'STUDENT' } })

      // Calculate user's achievement rank based on total points earned
      const currentUserPoints = earnedAchievements.reduce((sum, a) => sum + getRarityPoints(a.rarity), 0)
      const usersWithMorePoints = await prisma.user.count({
        where: {
          role: 'STUDENT',
          totalPoints: { gt: currentUserPoints }
        }
      })
      const achievementRank = usersWithMorePoints + 1

      return APIResponse.success({
        earned: earnedAchievements,
        available: availableAchievements,
        stats: {
          total: totalAchievements,
          earned: earnedCount,
          completionRate: completionRate,
          byRarity,
          recent: recentAchievements,
          rank: achievementRank,
          totalUsers
        }
      }, 'Achievements retrieved successfully')

    } catch (error) {
      console.error('Error fetching achievements:', error)
      return APIResponse.error('Failed to fetch achievements', 500)
    }
  }
)
