import { createOpenAI } from "@ai-sdk/openai";
import { streamObject } from "ai";
import { NextResponse } from "next/server";
import { z } from "zod";

export const maxDuration = 60;

// Simple test schema
const simpleQuestionSchema = z.object({
  question: z.string(),
  options: z.array(z.string()).length(4),
  answer: z.enum(["A", "B", "C", "D"]),
});

export async function POST(req: Request) {
  try {
    console.log("=== TESTING SIMPLE QUIZ GENERATION ===");
    
    const body = await req.json();
    const { textContent = "Data science is a field that uses scientific methods to extract knowledge from data." } = body;
    
    console.log("Text content:", textContent);
    
    // Create custom OpenAI provider
    const openai = createOpenAI({
      apiKey: '********************************************************************************************************************************************************************',
    });

    const result = streamObject({
      model: openai("gpt-4o-mini"),
      schema: simpleQuestionSchema,
      output: "array",
      prompt: `Create exactly 2 multiple choice questions about: ${textContent}
      
Each question should have:
- A clear question
- Exactly 4 options (A, B, C, D)
- One correct answer (A, B, C, or D)

Format as JSON array.`,
      onError: ({ error }) => {
        console.error("Test streamObject error:", error);
      },
      onFinish: ({ object }) => {
        console.log("Test generation completed!");
        console.log("Generated object:", JSON.stringify(object, null, 2));
      },
    });

    return result.toTextStreamResponse();
  } catch (error) {
    console.error("Test API error:", error);
    return NextResponse.json(
      { error: "Test failed" },
      { status: 500 }
    );
  }
}
