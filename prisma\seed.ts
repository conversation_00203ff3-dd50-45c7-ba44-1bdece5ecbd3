import { PrismaClient } from '../lib/generated/prisma';

const prisma = new PrismaClient();

async function main() {
  // Create a student user
  await prisma.user.create({
    data: {
      name: 'Student User',
      email: '<EMAIL>',
      role: 'STUDENT',
      bio: 'A sample student user',
      points: 0,
      level: 1,
    },
  });

  // Create an admin user
  await prisma.user.create({
    data: {
      name: 'Admin User',
      email: '<EMAIL>',
      role: 'ADMIN',
      bio: 'A sample admin user',
      points: 0,
      level: 1,
    },
  });
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  }); 