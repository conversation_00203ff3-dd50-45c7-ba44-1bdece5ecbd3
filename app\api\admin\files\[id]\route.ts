import { NextRequest } from 'next/server'
import { z } from 'zod'
 import { createAP<PERSON><PERSON>and<PERSON>, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'

const updateFileSchema = z.object({
  originalName: z.string().min(1, "Original name is required").optional(),
  uploadType: z.enum(['image', 'document', 'general']).optional(),
  folder: z.string().optional()
})

// GET /api/admin/files/[id] - Get specific file
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
     
  },
  async (request: NextRequest, { params }: { params: Promise<{ id: string } > }) => {
    const { id } = await params
    const fileId = id

    const file = await prisma.file.findUnique({
      where: { id: fileId },
      include: {
        uploadedBy: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    })

    if (!file) {
      return APIResponse.error('File not found', 404, 'FILE_NOT_FOUND')
    }

    const result = {
      id: file.id,
      filename: file.filename,
      originalName: file.originalName,
      mimeType: file.mimeType,
      size: file.size,
      url: file.url,
      uploadType: file.uploadType,
      folder: file.folder,
      createdAt: file.createdAt.toISOString(),
      updatedAt: file.updatedAt.toISOString(),
      uploadedBy: file.uploadedBy,
      formattedSize: formatFileSize(file.size),
      isImage: file.mimeType.startsWith('image/'),
      isDocument: file.mimeType.includes('pdf') || file.mimeType.includes('document') || file.mimeType.includes('text')
    }

    return APIResponse.success(result, 'File retrieved successfully')
  }
)

// PUT /api/admin/files/[id] - Update file metadata
export const PUT = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
     
    validateBody: updateFileSchema
  },
  async (request: NextRequest, { params, validatedBody }: { params: Promise<{ id: string } >, validatedBody: any }) => {
    const { id } = await params
    const fileId = id

    // Check if file exists
    const existingFile = await prisma.file.findUnique({
      where: { id: fileId }
    })

    if (!existingFile) {
      return APIResponse.error('File not found', 404, 'FILE_NOT_FOUND')
    }

    // Update file metadata
    const updatedFile = await prisma.file.update({
      where: { id: fileId },
      data: validatedBody,
      include: {
        uploadedBy: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    })

    return APIResponse.success(
      {
        id: updatedFile.id,
        filename: updatedFile.filename,
        originalName: updatedFile.originalName,
        mimeType: updatedFile.mimeType,
        size: updatedFile.size,
        url: updatedFile.url,
        uploadType: updatedFile.uploadType,
        folder: updatedFile.folder,
        createdAt: updatedFile.createdAt.toISOString(),
        updatedAt: updatedFile.updatedAt.toISOString(),
        uploadedBy: updatedFile.uploadedBy,
        formattedSize: formatFileSize(updatedFile.size),
        isImage: updatedFile.mimeType.startsWith('image/'),
        isDocument: updatedFile.mimeType.includes('pdf') || updatedFile.mimeType.includes('document') || updatedFile.mimeType.includes('text')
      },
      'File updated successfully'
    )
  }
)

// DELETE /api/admin/files/[id] - Delete specific file
export const DELETE = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
     
  },
  async (request: NextRequest, { params }: { params: Promise<{ id: string } > }) => {
    const { id } = await params
    const fileId = id

    // Check if file exists
    const existingFile = await prisma.file.findUnique({
      where: { id: fileId }
    })

    if (!existingFile) {
      return APIResponse.error('File not found', 404, 'FILE_NOT_FOUND')
    }

    // Delete from database
    await prisma.file.delete({
      where: { id: fileId }
    })

    // In a real implementation, you would also delete the actual file from storage
    console.log('File to be deleted from storage:', existingFile.url)

    return APIResponse.success(
      { 
        deletedFileId: fileId,
        filename: existingFile.filename
      },
      'File deleted successfully'
    )
  }
)

// Helper function to format file size
function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
