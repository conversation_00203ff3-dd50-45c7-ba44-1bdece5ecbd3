# Socket.IO Real-Time Features Setup

## Current Status
🚧 **Real-time features are currently disabled** to prevent connection errors.

The notification system works with API polling (every 30 seconds) instead of real-time WebSocket connections.

## Why Disabled?
- Next.js doesn't have a built-in HTTP server for Socket.IO
- Requires custom server setup
- Prevents "Max reconnection attempts reached" errors

## How to Enable Real-Time Features

### Option 1: Custom Next.js Server (Recommended)

1. **Create custom server** (`server.js`):
```javascript
const { createServer } = require('http')
const { parse } = require('url')
const next = require('next')
const { initializeSocketServer } = require('./lib/socket-server')

const dev = process.env.NODE_ENV !== 'production'
const app = next({ dev })
const handle = app.getRequestHandler()

app.prepare().then(() => {
  const server = createServer((req, res) => {
    const parsedUrl = parse(req.url, true)
    handle(req, res, parsedUrl)
  })

  // Initialize Socket.IO server
  initializeSocketServer(server)

  server.listen(3000, (err) => {
    if (err) throw err
    console.log('> Ready on http://localhost:3000')
  })
})
```

2. **Update package.json**:
```json
{
  "scripts": {
    "dev": "node server.js",
    "build": "next build",
    "start": "NODE_ENV=production node server.js"
  }
}
```

3. **Enable socket client** in layouts:
```typescript
// Uncomment in components/student/student-layout.tsx
if (session.user) {
  initializeSocket({
    id: session.user.id,
    name: session.user.name || 'Student',
    email: session.user.email || '',
    role: session.user.role || 'STUDENT',
    token: 'session-token'
  })
}
```

4. **Set environment variable**:
```bash
NEXT_PUBLIC_SOCKET_ENABLED=true
```

### Option 2: Separate Socket Server

1. **Run socket server separately** on different port
2. **Update socket client** to connect to separate server
3. **Handle CORS** for cross-origin connections

### Option 3: Use Vercel/Deployment Platform

1. **Deploy with custom server** support
2. **Configure WebSocket** support in deployment
3. **Update environment** variables

## Features Enabled with Real-Time

- ✅ **Instant notifications** (no 30-second delay)
- ✅ **Live quiz sessions** with real-time updates
- ✅ **Real-time chat** in quiz sessions
- ✅ **Live leaderboards** during quizzes
- ✅ **Collaborative features** for group activities
- ✅ **Connection status** indicators
- ✅ **Toast notifications** for immediate feedback

## Current Fallback Features

- ✅ **API-based notifications** (30-second polling)
- ✅ **Notification center** with full functionality
- ✅ **Quiz enrollment/completion** notifications
- ✅ **Achievement notifications**
- ✅ **All core features** work without real-time

## Testing Real-Time Features

Once enabled, test with:

1. **Multiple browser tabs** - see real-time sync
2. **Quiz enrollment** - instant notifications
3. **Quiz completion** - immediate updates
4. **Admin announcements** - broadcast to all users
5. **Connection status** - green/red indicators

## Troubleshooting

### Common Issues:
- **Port conflicts** - Use different port for socket server
- **CORS errors** - Configure proper origins
- **Authentication** - Ensure proper token handling
- **Reconnection loops** - Check server availability

### Debug Mode:
```bash
DEBUG=socket.io* npm run dev
```

## Production Considerations

- **Load balancing** with sticky sessions
- **Redis adapter** for multiple server instances
- **SSL/TLS** for secure WebSocket connections
- **Rate limiting** for socket events
- **Monitoring** and error tracking
