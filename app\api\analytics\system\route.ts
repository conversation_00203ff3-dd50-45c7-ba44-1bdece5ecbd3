import { NextRequest } from 'next/server'
import { z } from 'zod'
 import { createAPIHandler, APIResponse } from '@/lib/api-middleware'
import { AnalyticsService } from '@/lib/analytics-service'

const systemAnalyticsQuerySchema = z.object({
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
  period: z.enum(['1h', '24h', '7d', '30d']).optional().default('24h')
})

// GET /api/analytics/system - Get system health metrics
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
     
    validateQuery: systemAnalyticsQuerySchema
  },
  async (request: NextRequest, { validatedQuery }) => {
    const { startDate, endDate, period } = validatedQuery

    // Calculate time range
    let timeRange
    if (startDate && endDate) {
      timeRange = {
        start: new Date(startDate),
        end: new Date(endDate)
      }
    } else {
      const end = new Date()
      const start = new Date()
      
      switch (period) {
        case '1h':
          start.setHours(end.getHours() - 1)
          break
        case '24h':
          start.setDate(end.getDate() - 1)
          break
        case '7d':
          start.setDate(end.getDate() - 7)
          break
        case '30d':
          start.setDate(end.getDate() - 30)
          break
      }
      
      timeRange = { start, end }
    }

    const systemMetrics = await AnalyticsService.getSystemHealthMetrics(timeRange)
    
    return APIResponse.success(
      {
        systemMetrics,
        timeRange,
        period
      },
      'System health metrics retrieved successfully'
    )
  }
)
