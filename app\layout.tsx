import "./globals.css";
import { <PERSON>ada<PERSON> } from "next";
import { Toaster } from "sonner";
import { ThemeProvider } from "next-themes";
import { <PERSON><PERSON><PERSON> } from "next/font/google";
import { AuthProvider } from "@/components/providers/session-provider";
import { SettingsProvider } from "@/lib/settings-context";
import { MaintenanceMode } from "@/components/maintenance-mode";
import { Header } from "@/components/header";
import { RoleUpdateHandler } from "@/components/auth/role-update-handler";
import { SkipNav, SkipNavTarget } from "@/components/accessibility/skip-nav";
import { GlobalKeyboardShortcuts } from "@/components/accessibility/keyboard-shortcuts";

const geist = Geist({ subsets: ["latin"] });

export const metadata: Metadata = {
  metadataBase: new URL("https://quizmaster-ai.vercel.app"),
  title: "QuizMaster - AI-Powered Learning Platform",
  description: "Transform your learning with AI-generated quizzes, comprehensive analytics, and gamified progress tracking. Perfect for students, educators, and organizations.",
  keywords: ["quiz", "learning", "AI", "education", "assessment", "study"],
  authors: [{ name: "QuizMaster Team" }],
  openGraph: {
    title: "QuizMaster - AI-Powered Learning Platform",
    description: "Transform your learning with AI-generated quizzes, comprehensive analytics, and gamified progress tracking.",
    type: "website",
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    title: "QuizMaster - AI-Powered Learning Platform",
    description: "Transform your learning with AI-generated quizzes, comprehensive analytics, and gamified progress tracking.",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning className={`${geist.className}`}>
      <body suppressHydrationWarning>
        <AuthProvider>
          <SettingsProvider>
            <ThemeProvider attribute="class" enableSystem defaultTheme="light">
              <SkipNav />
              <RoleUpdateHandler />
              <GlobalKeyboardShortcuts />
              <MaintenanceMode>
                <div className="relative flex min-h-screen flex-col">
                  <Header />
                  <SkipNavTarget>
                    {children}
                  </SkipNavTarget>
                </div>
              </MaintenanceMode>
              <Toaster position="top-center" richColors />
            </ThemeProvider>
          </SettingsProvider>
        </AuthProvider>
      </body>
    </html>
  );
}
