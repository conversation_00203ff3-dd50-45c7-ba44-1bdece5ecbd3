import { NextRequest } from 'next/server'
import { auth } from '@/auth'
import { prisma } from '@/lib/prisma'
import { APIResponse } from '@/lib/api-middleware'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string } > }
) {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return APIResponse.unauthorized('Please sign in to start practice')
    }
    const { id } = await params
    const practiceId = id

    const user = await prisma.user.findUnique({
      where: { id: session.user.id }
    })

    if (!user) {
      return APIResponse.notFound('User not found')
    }

    // Get the practice session
    const practiceQuiz = await prisma.quiz.findUnique({
      where: {
        id: practiceId,
        type: 'DAILY_PRACTICE',
        isPublished: true
      },
      include: {
        questions: {
          select: {
            id: true,
            type: true,
            text: true,
            options: true,
            points: true,
            order: true
          },
          orderBy: {
            order: 'asc'
          }
        }
      }
    })

    if (!practiceQuiz) {
      return APIResponse.notFound('Practice session not found')
    }

    // Check if user has already started this practice session today
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const tomorrow = new Date(today)
    tomorrow.setDate(today.getDate() + 1)

    const existingAttempt = await prisma.quizAttempt.findFirst({
      where: {
        userId: user.id,
        quizId: practiceId,
        startedAt: {
          gte: today,
          lt: tomorrow
        }
      }
    })

    if (existingAttempt && !existingAttempt.completedAt) {
      // Return existing attempt
      return APIResponse.success(
        {
          attemptId: existingAttempt.id,
          quiz: {
            id: practiceQuiz.id,
            title: practiceQuiz.title,
            description: practiceQuiz.description,
            difficulty: practiceQuiz.difficulty,
            timeLimit: practiceQuiz.timeLimit,
            questions: practiceQuiz.questions
          }
        },
        'Practice session resumed'
      )
    }

    // Create new attempt
    const totalPoints = practiceQuiz.questions.reduce((sum, q) => sum + q.points, 0)
    const attempt = await prisma.quizAttempt.create({
      data: {
        userId: user.id,
        quizId: practiceId,
        answers: {},
        score: 0,
        percentage: 0,
        totalQuestions: practiceQuiz.questions.length,
        totalPoints,
        correctAnswers: 0,
        incorrectAnswers: 0,
        unansweredQuestions: practiceQuiz.questions.length
      }
    })

    return APIResponse.success(
      {
        attemptId: attempt.id,
        quiz: {
          id: practiceQuiz.id,
          title: practiceQuiz.title,
          description: practiceQuiz.description,
          difficulty: practiceQuiz.difficulty,
          timeLimit: practiceQuiz.timeLimit,
          questions: practiceQuiz.questions
        }
      },
      'Practice session started successfully'
    )

  } catch (error) {
    console.error('Error starting practice session:', error)
    return APIResponse.error('Failed to start practice session', 500)
  }
}
