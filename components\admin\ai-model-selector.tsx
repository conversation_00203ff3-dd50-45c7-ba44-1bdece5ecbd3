"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  Cpu, 
  Zap, 
  Target, 
  DollarSign, 
  Clock, 
  Star,
  Info,
  TrendingUp,
  Brain,
  Sparkles
} from "lucide-react"
import { motion } from "framer-motion"

interface AIModel {
  id: string
  name: string
  provider: string
  description: string
  capabilities: {
    textGeneration: boolean
    imageInput: boolean
    toolCalling: boolean
    streaming: boolean
    objectGeneration: boolean
    reasoning: boolean
  }
  pricing: {
    input: number
    output: number
  }
  contextWindow: number
  maxOutput: number
  speed: 'fast' | 'medium' | 'slow'
  quality: 'high' | 'medium' | 'low'
  useCase: string[]
}

interface AIModelSelectorProps {
  models: AIModel[]
  selectedModel: string
  onModelSelect: (modelId: string) => void
  task: string
  taskDescription: string
}

const speedIcons = {
  fast: Zap,
  medium: Clock,
  slow: Target
}

const speedColors = {
  fast: 'bg-green-100 text-green-800',
  medium: 'bg-yellow-100 text-yellow-800',
  slow: 'bg-red-100 text-red-800'
}

const qualityColors = {
  high: 'bg-purple-100 text-purple-800',
  medium: 'bg-blue-100 text-blue-800',
  low: 'bg-gray-100 text-gray-800'
}

const providerColors = {
  'OpenAI': 'bg-emerald-100 text-emerald-800',
  'Anthropic': 'bg-orange-100 text-orange-800',
  'Google': 'bg-blue-100 text-blue-800',
  'xAI': 'bg-purple-100 text-purple-800',
  'DeepSeek': 'bg-indigo-100 text-indigo-800',
  'Groq': 'bg-pink-100 text-pink-800'
}

export function AIModelSelector({ 
  models, 
  selectedModel, 
  onModelSelect, 
  task, 
  taskDescription 
}: AIModelSelectorProps) {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [filterBy, setFilterBy] = useState<'all' | 'speed' | 'quality' | 'provider'>('all')
  const [sortBy, setSortBy] = useState<'name' | 'speed' | 'quality' | 'price'>('name')

  const getFilteredAndSortedModels = () => {
    let filtered = models

    // Filter models
    if (filterBy === 'speed') {
      filtered = models.filter(m => m.speed === 'fast')
    } else if (filterBy === 'quality') {
      filtered = models.filter(m => m.quality === 'high')
    } else if (filterBy === 'provider') {
      // Group by provider, could be enhanced
      filtered = models
    }

    // Sort models
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'speed':
          const speedOrder = { fast: 3, medium: 2, slow: 1 }
          return speedOrder[b.speed] - speedOrder[a.speed]
        case 'quality':
          const qualityOrder = { high: 3, medium: 2, low: 1 }
          return qualityOrder[b.quality] - qualityOrder[a.quality]
        case 'price':
          return a.pricing.input - b.pricing.input
        default:
          return a.name.localeCompare(b.name)
      }
    })

    return filtered
  }

  const getRecommendedModel = () => {
    // Simple recommendation logic
    const taskModels = models.filter(m => m.useCase.some(uc => 
      task.toLowerCase().includes(uc.toLowerCase())
    ))
    
    if (taskModels.length > 0) {
      return taskModels.sort((a, b) => {
        const qualityOrder = { high: 3, medium: 2, low: 1 }
        return qualityOrder[b.quality] - qualityOrder[a.quality]
      })[0]
    }
    
    return models[0]
  }

  const selectedModelData = models.find(m => m.id === selectedModel)
  const recommendedModel = getRecommendedModel()

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h3 className="text-lg font-semibold mb-2">Select AI Model for {task}</h3>
        <p className="text-sm text-muted-foreground">{taskDescription}</p>
      </div>

      {/* Recommendation */}
      {recommendedModel && recommendedModel.id !== selectedModel && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gradient-to-r from-purple-50 to-blue-50 border border-purple-200 rounded-lg p-4"
        >
          <div className="flex items-center gap-3">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Sparkles className="h-4 w-4 text-purple-600" />
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium">Recommended for {task}</p>
              <p className="text-xs text-muted-foreground">
                {recommendedModel.name} - {recommendedModel.description}
              </p>
            </div>
            <Button
              size="sm"
              onClick={() => onModelSelect(recommendedModel.id)}
              className="bg-purple-600 hover:bg-purple-700"
            >
              Use Recommended
            </Button>
          </div>
        </motion.div>
      )}

      {/* Current Selection */}
      {selectedModelData && (
        <Card className="border-2 border-purple-200 bg-purple-50/50">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-base">Currently Selected</CardTitle>
              <Badge className="bg-purple-100 text-purple-800">Selected</Badge>
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-4">
              <div className="flex-1">
                <h4 className="font-medium">{selectedModelData.name}</h4>
                <p className="text-sm text-muted-foreground">{selectedModelData.description}</p>
              </div>
              <div className="flex gap-2">
                <Badge className={speedColors[selectedModelData.speed]}>
                  {selectedModelData.speed}
                </Badge>
                <Badge className={qualityColors[selectedModelData.quality]}>
                  {selectedModelData.quality}
                </Badge>
                <Badge className={providerColors[selectedModelData.provider as keyof typeof providerColors] || 'bg-gray-100 text-gray-800'}>
                  {selectedModelData.provider}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Filters and Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Select value={filterBy} onValueChange={(value: any) => setFilterBy(value)}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Models</SelectItem>
              <SelectItem value="speed">Fast Only</SelectItem>
              <SelectItem value="quality">High Quality</SelectItem>
              <SelectItem value="provider">By Provider</SelectItem>
            </SelectContent>
          </Select>

          <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="name">Name</SelectItem>
              <SelectItem value="speed">Speed</SelectItem>
              <SelectItem value="quality">Quality</SelectItem>
              <SelectItem value="price">Price</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant={viewMode === 'grid' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('grid')}
          >
            Grid
          </Button>
          <Button
            variant={viewMode === 'list' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('list')}
          >
            List
          </Button>
        </div>
      </div>

      {/* Model List */}
      <div className={viewMode === 'grid' 
        ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4' 
        : 'space-y-3'
      }>
        {getFilteredAndSortedModels().map((model) => (
          <motion.div
            key={model.id}
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.2 }}
          >
            <Card 
              className={`cursor-pointer transition-all hover:shadow-md ${
                selectedModel === model.id 
                  ? 'ring-2 ring-purple-500 bg-purple-50/50' 
                  : 'hover:border-purple-300'
              }`}
              onClick={() => onModelSelect(model.id)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm font-medium">{model.name}</CardTitle>
                  {selectedModel === model.id && (
                    <Star className="h-4 w-4 text-purple-600 fill-current" />
                  )}
                </div>
                <CardDescription className="text-xs">{model.description}</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                {/* Badges */}
                <div className="flex flex-wrap gap-1">
                  <Badge className={speedColors[model.speed]} variant="secondary">
                    {model.speed}
                  </Badge>
                  <Badge className={qualityColors[model.quality]} variant="secondary">
                    {model.quality}
                  </Badge>
                  <Badge className={providerColors[model.provider as keyof typeof providerColors] || 'bg-gray-100 text-gray-800'} variant="secondary">
                    {model.provider}
                  </Badge>
                </div>

                {/* Capabilities */}
                <div className="flex flex-wrap gap-1">
                  {model.capabilities.reasoning && (
                    <Badge variant="outline" className="text-xs">
                      <Brain className="h-3 w-3 mr-1" />
                      Reasoning
                    </Badge>
                  )}
                  {model.capabilities.imageInput && (
                    <Badge variant="outline" className="text-xs">
                      Vision
                    </Badge>
                  )}
                  {model.capabilities.toolCalling && (
                    <Badge variant="outline" className="text-xs">
                      Tools
                    </Badge>
                  )}
                </div>

                {/* Pricing */}
                <div className="flex items-center justify-between text-xs text-muted-foreground">
                  <span className="flex items-center gap-1">
                    <DollarSign className="h-3 w-3" />
                    ${model.pricing.input}/1M tokens
                  </span>
                  <span>{(model.contextWindow / 1000).toFixed(0)}K context</span>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {getFilteredAndSortedModels().length === 0 && (
        <div className="text-center py-8 text-muted-foreground">
          <Cpu className="h-8 w-8 mx-auto mb-2 opacity-50" />
          <p>No models match the current filters</p>
        </div>
      )}
    </div>
  )
}
