"use client"

import { useEffect, useState } from "react"
import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { toast } from "@/lib/toast-utils"

export function RoleUpdateHandler() {
  const { data: session, status, update } = useSession()
  const router = useRouter()
  const [isUpdating, setIsUpdating] = useState(false)
  const [isClient, setIsClient] = useState(false)

  // Ensure we're on the client side
  useEffect(() => {
    setIsClient(true)
  }, [])

  useEffect(() => {
    const handleRoleUpdate = async () => {
      // Only run if user is authenticated, we're on client side, and we haven't already updated
      if (status !== "authenticated" || !session?.user || isUpdating || !isClient) {
        return
      }

      try {
        // Check if there's a selected role in localStorage
        const selectedRole = localStorage.getItem('selectedRole')

        if (!selectedRole || selectedRole === session.user.role) {
          // No role to update or role already matches
          localStorage.removeItem('selectedRole')
          return
        }

      setIsUpdating(true)

      try {
        const response = await fetch('/api/auth/update-role', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ role: selectedRole }),
        })

        if (!response.ok) {
          throw new Error('Failed to update role')
        }

        // Update the session with the new role
        await update({
          ...session,
          user: {
            ...session.user,
            role: selectedRole as 'STUDENT' | 'ADMIN'
          }
        })

        // Clean up localStorage
        localStorage.removeItem('selectedRole')

        // Show success message
        toast.success(`Role updated to ${selectedRole}`)

        // Redirect to appropriate dashboard
        if (selectedRole === 'ADMIN') {
          router.push('/admin')
        } else {
          router.push('/dashboard')
        }

      } catch (error) {
        console.error('Error updating role:', error)
        toast.error('Failed to update role. Please try again.')
        localStorage.removeItem('selectedRole')
      } finally {
        setIsUpdating(false)
      }
      } catch (error) {
        console.error('Error in role update handler:', error)
      }
    }

    handleRoleUpdate()
  }, [session, status, update, router, isUpdating, isClient])

  // This component doesn't render anything visible
  return null
}
