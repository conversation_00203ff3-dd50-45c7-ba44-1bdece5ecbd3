import { NextRequest } from 'next/server'
import { auth } from '@/auth'
import { prisma } from '@/lib/prisma'
import { APIResponse } from '@/lib/api-middleware'

export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return APIResponse.unauthorized('Please sign in to view practice sessions')
    }

    const { searchParams } = new URL(request.url)
    const category = searchParams.get('category') || 'all'
    const subjectId = searchParams.get('subjectId')
    const chapterId = searchParams.get('chapterId')
    const topicId = searchParams.get('topicId')

    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      include: {
        practiceStats: true
      }
    })

    if (!user) {
      return APIResponse.notFound('User not found')
    }

    // Get or create practice stats
    let practiceStats = user.practiceStats
    if (!practiceStats) {
      practiceStats = await prisma.userPracticeStats.create({
        data: {
          userId: user.id
        }
      })
    }

    // Build where clause for practice sessions
    const whereClause: any = {
      type: 'DAILY_PRACTICE',
      isPublished: true,
      enrollments: {
        some: {
          userId: user.id
        }
      }
    }

    // Add category filters
    if (category !== 'all') {
      whereClause.category = category
    }
    if (subjectId) {
      whereClause.subjectId = subjectId
    }
    if (chapterId) {
      whereClause.chapterId = chapterId
    }
    if (topicId) {
      whereClause.topicId = topicId
    }

    // Get practice sessions (quizzes with type DAILY_PRACTICE that user is enrolled in)
    const practiceQuizzes = await prisma.quiz.findMany({
      where: whereClause,
      include: {
        questions: {
          select: {
            id: true
          }
        },
        attempts: {
          where: {
            userId: user.id
          },
          select: {
            id: true,
            score: true,
            percentage: true,
            completedAt: true
          },
          orderBy: {
            completedAt: 'desc'
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    // Calculate today's completed sessions
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const tomorrow = new Date(today)
    tomorrow.setDate(today.getDate() + 1)

    const todayAttempts = await prisma.quizAttempt.findMany({
      where: {
        userId: user.id,
        completedAt: {
          gte: today,
          lt: tomorrow
        },
        quiz: {
          type: 'DAILY_PRACTICE'
        }
      }
    })

    // Calculate streak
    let currentStreak = practiceStats.currentStreak
    const lastPracticeDate = practiceStats.lastPracticeDate
    
    if (lastPracticeDate) {
      const daysSinceLastPractice = Math.floor(
        (today.getTime() - lastPracticeDate.getTime()) / (1000 * 60 * 60 * 24)
      )
      
      if (daysSinceLastPractice > 1) {
        currentStreak = 0
      }
    }

    // Calculate average score
    const allAttempts = await prisma.quizAttempt.findMany({
      where: {
        userId: user.id,
        quiz: {
          type: 'DAILY_PRACTICE'
        },
        completedAt: { not: null }
      },
      select: {
        percentage: true
      }
    })

    const averageScore = allAttempts.length > 0
      ? Math.round(allAttempts.reduce((sum, attempt) => sum + (attempt.percentage || 0), 0) / allAttempts.length)
      : 0

    // Transform practice sessions
    const transformedSessions = practiceQuizzes.map(quiz => {
      const latestAttempt = quiz.attempts[0]
      const isCompleted = quiz.attempts.length > 0
      
      // Calculate streak for this specific session
      const sessionAttempts = quiz.attempts.filter(attempt => attempt.completedAt)
      let sessionStreak = 0
      
      if (sessionAttempts.length > 0) {
        // Simple streak calculation - consecutive days with attempts
        sessionStreak = Math.min(sessionAttempts.length, 7) // Cap at 7 for display
      }

      return {
        id: quiz.id,
        title: quiz.title,
        description: quiz.description || '',
        category: quiz.category || 'general',
        difficulty: quiz.difficulty,
        questionCount: quiz.questions.length,
        estimatedTime: quiz.estimatedTime || quiz.timeLimit || 15,
        points: quiz.points || 50,
        isCompleted,
        completedAt: latestAttempt?.completedAt?.toISOString(),
        score: latestAttempt?.percentage,
        streak: sessionStreak
      }
    })

    // Prepare stats response
    const stats = {
      currentStreak,
      longestStreak: practiceStats.longestStreak,
      totalSessions: practiceStats.totalSessions,
      totalPoints: practiceStats.totalPoints,
      averageScore,
      todayCompleted: todayAttempts.length,
      todayTarget: practiceStats.todayTarget
    }

    return APIResponse.success(
      {
        sessions: transformedSessions,
        stats
      },
      'Practice sessions retrieved successfully'
    )

  } catch (error) {
    console.error('Error fetching practice sessions:', error)
    return APIResponse.error('Failed to fetch practice sessions', 500)
  }
}
