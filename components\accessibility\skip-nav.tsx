"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"

/**
 * Skip navigation component for keyboard users
 * Allows users to skip to main content
 */
export function SkipNav() {
  return (
    <Button
      asChild
      variant="outline"
      className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 bg-background border-2 border-primary"
    >
      <a href="#main-content">
        Skip to main content
      </a>
    </Button>
  )
}

/**
 * Skip to content anchor for main content areas
 */
export function SkipNavTarget({ children }: { children: React.ReactNode }) {
  return (
    <main id="main-content" tabIndex={-1}>
      {children}
    </main>
  )
}
