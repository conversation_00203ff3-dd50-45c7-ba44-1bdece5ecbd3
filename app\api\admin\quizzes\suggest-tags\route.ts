import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { getModelInstance } from '@/lib/ai-providers'
import { generateObject } from 'ai'
import { z } from 'zod'

const tagSuggestionSchema = z.object({
  content: z.string().min(1, "Content is required for tag suggestions"),
  existingTags: z.array(z.string()).optional().default([])
})

const suggestedTagsSchema = z.object({
  suggestions: z.array(z.object({
    tag: z.string(),
    category: z.enum(['subject', 'topic', 'difficulty', 'skill', 'general']),
    confidence: z.number().min(0).max(1)
  }))
})

// POST /api/admin/quizzes/suggest-tags - Generate AI-powered tag suggestions
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    validateBody: tagSuggestionSchema
  },
  async (request: NextRequest, { validatedBody, user }) => {
    try {
      const { content, existingTags } = validatedBody

      // Get popular tags from the system
      const popularQuizzes = await prisma.quiz.findMany({
        where: {
          isPublished: true
        },
        select: {
          tags: true
        },
        take: 100,
        orderBy: {
          createdAt: 'desc'
        }
      })

      // Calculate tag frequency
      const tagFrequency = new Map<string, number>()
      popularQuizzes.forEach(quiz => {
        quiz.tags.forEach(tag => {
          tagFrequency.set(tag, (tagFrequency.get(tag) || 0) + 1)
        })
      })

      const mostPopularTags = Array.from(tagFrequency.entries())
        .sort(([,a], [,b]) => b - a)
        .slice(0, 20)
        .map(([tag]) => tag)

      // Use the same AI provider instance as the quiz creator
      const model = getModelInstance('gpt-4o-mini')

      // Generate AI-powered tag suggestions
      const result = await generateObject({
        model: model,
        schema: suggestedTagsSchema,
        prompt: `
          Analyze the following quiz content and suggest relevant tags:
          
          Content: "${content}"
          
          Existing tags: ${existingTags.join(', ')}
          Popular tags in system: ${mostPopularTags.join(', ')}
          
          Suggest 8-12 relevant tags categorized as:
          - subject: Academic subjects (e.g., "mathematics", "biology", "history")
          - topic: Specific topics (e.g., "algebra", "photosynthesis", "world-war-2")
          - difficulty: Difficulty indicators (e.g., "beginner", "intermediate", "advanced")
          - skill: Skills being tested (e.g., "problem-solving", "critical-thinking", "memorization")
          - general: General descriptors (e.g., "multiple-choice", "timed", "practice")
          
          For each tag, provide:
          1. The tag name (lowercase, hyphenated if multiple words)
          2. The category it belongs to
          3. A confidence score (0-1) based on how relevant it is to the content
          
          Prioritize:
          - Tags that are highly relevant to the content
          - Tags that match popular tags in the system when appropriate
          - Avoid duplicating existing tags
          - Use clear, searchable terms
          - Include both broad and specific tags
        `
      })

      return APIResponse.success({
        suggestions: result.object.suggestions,
        popularTags: mostPopularTags,
        generatedAt: new Date().toISOString()
      }, 'Tag suggestions generated successfully')
    } catch (error) {
      console.error('Error generating tag suggestions:', error)
      return APIResponse.error('Failed to generate tag suggestions', 500)
    }
  }
)
