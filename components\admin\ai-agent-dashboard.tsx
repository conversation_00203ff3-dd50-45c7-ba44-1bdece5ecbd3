"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  Brain, 
  Cpu, 
  Zap, 
  Target, 
  TrendingUp,
  Activity,
  Clock,
  DollarSign,
  CheckCircle,
  AlertTriangle,
  Info,
  Sparkles,
  BarChart3,
  Settings,
  RefreshCw
} from "lucide-react"
import { motion } from "framer-motion"

interface AgentMetrics {
  totalQuizzesGenerated: number
  averageQualityScore: number
  averageGenerationTime: number
  totalTokensUsed: number
  estimatedCost: number
  successRate: number
  activeAgents: number
}

interface AgentStatus {
  id: string
  name: string
  type: 'orchestrator' | 'analyzer' | 'generator' | 'evaluator'
  status: 'active' | 'idle' | 'busy' | 'error'
  currentTask?: string
  lastUsed: string
  totalUsage: number
  averageResponseTime: number
  model: string
}

export function AIAgentDashboard() {
  const [metrics, setMetrics] = useState<AgentMetrics>({
    totalQuizzesGenerated: 0,
    averageQualityScore: 0,
    averageGenerationTime: 0,
    totalTokensUsed: 0,
    estimatedCost: 0,
    successRate: 0,
    activeAgents: 0
  })

  const [agents, setAgents] = useState<AgentStatus[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchDashboardData()
    const interval = setInterval(fetchDashboardData, 30000) // Refresh every 30 seconds
    return () => clearInterval(interval)
  }, [])

  const fetchDashboardData = async () => {
    try {
      // Simulate API call - replace with actual API
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      setMetrics({
        totalQuizzesGenerated: 247,
        averageQualityScore: 87.3,
        averageGenerationTime: 45.2,
        totalTokensUsed: 1250000,
        estimatedCost: 15.75,
        successRate: 94.2,
        activeAgents: 4
      })

      setAgents([
        {
          id: 'orchestrator-1',
          name: 'Quiz Orchestrator',
          type: 'orchestrator',
          status: 'active',
          currentTask: 'Planning quiz structure',
          lastUsed: '2 minutes ago',
          totalUsage: 156,
          averageResponseTime: 2.3,
          model: 'gpt-4o'
        },
        {
          id: 'analyzer-1',
          name: 'Content Analyzer',
          type: 'analyzer',
          status: 'idle',
          lastUsed: '15 minutes ago',
          totalUsage: 203,
          averageResponseTime: 1.8,
          model: 'claude-3-5-sonnet-20241022'
        },
        {
          id: 'generator-1',
          name: 'Question Generator',
          type: 'generator',
          status: 'busy',
          currentTask: 'Generating MCQ questions',
          lastUsed: '1 minute ago',
          totalUsage: 189,
          averageResponseTime: 3.1,
          model: 'gpt-4o'
        },
        {
          id: 'evaluator-1',
          name: 'Quality Evaluator',
          type: 'evaluator',
          status: 'idle',
          lastUsed: '8 minutes ago',
          totalUsage: 134,
          averageResponseTime: 2.7,
          model: 'claude-3-5-sonnet-20241022'
        }
      ])
    } catch (error) {
      console.error('Error fetching dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800'
      case 'busy': return 'bg-blue-100 text-blue-800'
      case 'idle': return 'bg-gray-100 text-gray-800'
      case 'error': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'orchestrator': return Brain
      case 'analyzer': return BarChart3
      case 'generator': return Sparkles
      case 'evaluator': return Target
      default: return Cpu
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="flex items-center gap-2">
          <RefreshCw className="h-4 w-4 animate-spin" />
          <span>Loading AI Agent Dashboard...</span>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">AI Agent Dashboard</h2>
          <p className="text-muted-foreground">Monitor and manage your AI quiz creation agents</p>
        </div>
        <Button onClick={fetchDashboardData} variant="outline" size="sm">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Metrics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Quizzes Generated</CardTitle>
              <Sparkles className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metrics.totalQuizzesGenerated}</div>
              <p className="text-xs text-muted-foreground">
                +12% from last month
              </p>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Average Quality</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metrics.averageQualityScore}%</div>
              <Progress value={metrics.averageQualityScore} className="mt-2" />
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Generation Time</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metrics.averageGenerationTime}s</div>
              <p className="text-xs text-muted-foreground">
                -8% improvement
              </p>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Estimated Cost</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">${metrics.estimatedCost}</div>
              <p className="text-xs text-muted-foreground">
                {(metrics.totalTokensUsed / 1000000).toFixed(1)}M tokens used
              </p>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Agent Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Agent Status
          </CardTitle>
          <CardDescription>
            Real-time status of your AI agents
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {agents.map((agent, index) => {
              const Icon = getTypeIcon(agent.type)
              return (
                <motion.div
                  key={agent.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="flex items-center justify-between p-4 border rounded-lg"
                >
                  <div className="flex items-center gap-4">
                    <div className="p-2 bg-purple-100 rounded-lg">
                      <Icon className="h-4 w-4 text-purple-600" />
                    </div>
                    <div>
                      <h4 className="font-medium">{agent.name}</h4>
                      <p className="text-sm text-muted-foreground">
                        {agent.currentTask || `Last used ${agent.lastUsed}`}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-4">
                    <div className="text-right">
                      <p className="text-sm font-medium">{agent.model}</p>
                      <p className="text-xs text-muted-foreground">
                        {agent.totalUsage} uses • {agent.averageResponseTime}s avg
                      </p>
                    </div>
                    <Badge className={getStatusColor(agent.status)}>
                      {agent.status}
                    </Badge>
                  </div>
                </motion.div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Performance Insights */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Success Rate
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Overall Success Rate</span>
                <span className="text-2xl font-bold text-green-600">{metrics.successRate}%</span>
              </div>
              <Progress value={metrics.successRate} className="h-2" />
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span>Excellent performance across all agents</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              System Health
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm">Active Agents</span>
                <Badge className="bg-green-100 text-green-800">
                  {metrics.activeAgents}/4 Online
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">System Load</span>
                <Badge className="bg-blue-100 text-blue-800">Normal</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">API Status</span>
                <Badge className="bg-green-100 text-green-800">Healthy</Badge>
              </div>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Info className="h-4 w-4 text-blue-600" />
                <span>All systems operational</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
