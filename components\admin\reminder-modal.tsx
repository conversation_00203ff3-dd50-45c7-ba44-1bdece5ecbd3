"use client"

import { useState } from "react"
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogDescription,
  <PERSON>alog<PERSON>ooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { 
  Bell, 
  Send,
  Loader2,
  Users,
  MessageSquare
} from "lucide-react"
import { toast } from "@/lib/toast-utils"

interface ReminderModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  scheduledQuizId: string
  scheduledQuizTitle: string
}

export function ReminderModal({ 
  open, 
  onOpenChange, 
  scheduledQuizId, 
  scheduledQuizTitle 
}: ReminderModalProps) {
  const [message, setMessage] = useState("")
  const [sendToAll, setSendToAll] = useState(true)
  const [sending, setSending] = useState(false)

  const handleSendReminder = async () => {
    if (!message.trim()) {
      toast.error('Please enter a reminder message')
      return
    }

    try {
      setSending(true)
      
      const response = await fetch(`/api/admin/scheduled-quizzes/${scheduledQuizId}/reminder`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          message: message.trim(),
          sendToAll
        })
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to send reminder')
      }

      const result = await response.json()
      toast.success(result.message || 'Reminder sent successfully!')
      
      // Reset form and close modal
      setMessage("")
      setSendToAll(true)
      onOpenChange(false)
    } catch (error) {
      console.error('Error sending reminder:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to send reminder')
    } finally {
      setSending(false)
    }
  }

  const handleClose = () => {
    if (!sending) {
      setMessage("")
      setSendToAll(true)
      onOpenChange(false)
    }
  }

  // Default reminder message template
  const defaultMessage = `Hi there!

This is a friendly reminder about the upcoming quiz: "${scheduledQuizTitle}"

Please make sure to:
• Review the quiz instructions
• Prepare any necessary materials
• Join on time for the best experience

Good luck with your quiz!

Best regards,
The Admin Team`

  const useDefaultMessage = () => {
    setMessage(defaultMessage)
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Send Reminder
          </DialogTitle>
          <DialogDescription>
            Send a reminder notification to participants of "{scheduledQuizTitle}"
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Recipients */}
          <div className="space-y-3">
            <Label className="text-base font-medium">Recipients</Label>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="send-to-all"
                checked={sendToAll}
                onCheckedChange={(checked) => setSendToAll(checked as boolean)}
              />
              <Label htmlFor="send-to-all" className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                Send to all enrolled participants
              </Label>
            </div>
            <p className="text-sm text-muted-foreground">
              The reminder will be sent to all students who have enrolled in this scheduled quiz.
            </p>
          </div>

          {/* Message */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label htmlFor="message" className="text-base font-medium">
                Reminder Message
              </Label>
              <Button
                variant="outline"
                size="sm"
                onClick={useDefaultMessage}
                disabled={sending}
              >
                <MessageSquare className="h-4 w-4 mr-2" />
                Use Template
              </Button>
            </div>
            <Textarea
              id="message"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="Enter your reminder message..."
              rows={8}
              className="resize-none"
              disabled={sending}
            />
            <p className="text-sm text-muted-foreground">
              This message will be sent as a notification to the selected participants.
            </p>
          </div>

          {/* Preview */}
          {message.trim() && (
            <div className="space-y-2">
              <Label className="text-sm font-medium">Preview</Label>
              <div className="p-3 bg-muted rounded-lg border">
                <div className="text-sm font-medium mb-1">Quiz Reminder: {scheduledQuizTitle}</div>
                <div className="text-sm whitespace-pre-wrap">{message}</div>
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={sending}>
            Cancel
          </Button>
          <Button onClick={handleSendReminder} disabled={sending || !message.trim()}>
            {sending ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Sending...
              </>
            ) : (
              <>
                <Send className="h-4 w-4 mr-2" />
                Send Reminder
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
