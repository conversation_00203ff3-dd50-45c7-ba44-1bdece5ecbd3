import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { notificationService, NotificationTemplates } from '@/lib/notification-service'

// POST /api/test-notification - Send a test notification (for development)
export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { type = 'welcome' } = body

    let notificationData
    
    switch (type) {
      case 'welcome':
        notificationData = NotificationTemplates.WELCOME(session.user.name || 'User')
        break
      
      case 'quiz_completed':
        notificationData = NotificationTemplates.QUIZ_COMPLETED('Sample Quiz', 85, 'sample-quiz-id')
        break
      
      case 'achievement':
        notificationData = NotificationTemplates.ACHIEVEMENT_UNLOCKED('First Quiz', 'Complete your first quiz')
        break
      
      case 'rank_improved':
        notificationData = NotificationTemplates.RANK_CHANGED(5, 10)
        break
      
      case 'streak':
        notificationData = NotificationTemplates.STREAK_MILESTONE(7)
        break
      
      default:
        notificationData = {
          type: 'ANNOUNCEMENT' as any,
          title: 'Test Notification 🧪',
          message: 'This is a test notification to verify the system is working correctly.',
          priority: 'normal' as const,
          category: 'test'
        }
    }

    // Send notification to current user
    const notificationId = await notificationService.sendToUsers(
      [{ userId: session.user.id }],
      notificationData
    )

    return NextResponse.json({
      success: true,
      message: 'Test notification sent successfully',
      notificationId,
      type
    })

  } catch (error) {
    console.error('Error sending test notification:', error)
    return NextResponse.json(
      { error: 'Failed to send test notification' },
      { status: 500 }
    )
  }
}
