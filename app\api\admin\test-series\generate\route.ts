import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { QuizCreationAgent } from '@/lib/ai-agents/quiz-creation-agent'
import { z } from 'zod'

const testSeriesSchema = z.object({
  testType: z.enum(['WEEKLY_TEST', 'MONTHLY_TEST', 'TEST_SERIES', 'DAILY_PRACTICE']),
  examPattern: z.string().optional(),
  syllabusCoverage: z.enum(['CURRENT_WEEK', 'CURRENT_MONTH', 'FULL_SYLLABUS', 'SPECIFIC_TOPICS']),
  subject: z.string(),
  chapter: z.string().optional(),
  topic: z.string().optional(),
  questionCount: z.number().min(5).max(200),
  difficulty: z.enum(['EASY', 'MEDIUM', 'HARD']),
  questionTypes: z.array(z.string()),
  timeLimit: z.number().min(5).max(300),
  institutionName: z.string().optional(),
  testDate: z.string().optional(),
  printFormat: z.boolean().default(false),
  includeAnswerKey: z.boolean().default(true),
  includeInstructions: z.boolean().default(true),
  language: z.enum(['ENGLISH', 'HINDI', 'BILINGUAL']).default('ENGLISH'),
  content: z.string().optional(),
  files: z.array(z.object({
    name: z.string(),
    content: z.string(),
    type: z.string()
  })).optional()
})

// POST /api/admin/test-series/generate - Generate institutional test series
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    validateBody: testSeriesSchema
  },
  async (request: NextRequest, { validatedBody, user }) => {
    try {
      const agent = new QuizCreationAgent()
      
      // Generate the test based on institutional requirements
      const result = await agent.createQuiz({
        content: validatedBody.content || `Generate ${validatedBody.testType} for ${validatedBody.subject}`,
        files: validatedBody.files,
        requirements: {
          questionCount: validatedBody.questionCount,
          difficulty: validatedBody.difficulty,
          questionTypes: validatedBody.questionTypes,
          timeLimit: validatedBody.timeLimit,
          subject: validatedBody.subject,
          chapter: validatedBody.chapter,
          topic: validatedBody.topic,
          examPattern: validatedBody.examPattern,
          testType: validatedBody.testType,
          syllabusCoverage: validatedBody.syllabusCoverage,
          institutionName: validatedBody.institutionName,
          testDate: validatedBody.testDate,
          language: validatedBody.language
        },
        preferences: {
          prioritizeQuality: true,
          prioritizeSpeed: false,
          includeExplanations: true,
          generateTags: true
        }
      })

      if (!result.success) {
        return APIResponse.error('Failed to generate test series', 500)
      }

      // Format the response for institutional use
      const institutionalTest = {
        ...result.quiz,
        testType: validatedBody.testType,
        syllabusCoverage: validatedBody.syllabusCoverage,
        institutionName: validatedBody.institutionName,
        testDate: validatedBody.testDate,
        printFormat: validatedBody.printFormat,
        includeAnswerKey: validatedBody.includeAnswerKey,
        includeInstructions: validatedBody.includeInstructions,
        language: validatedBody.language,
        generatedAt: new Date().toISOString(),
        generatedBy: user.id
      }

      return APIResponse.success({
        test: institutionalTest,
        metadata: result.metadata
      }, 'Test series generated successfully')
    } catch (error) {
      console.error('Error generating test series:', error)
      return APIResponse.error('Failed to generate test series', 500)
    }
  }
)

// GET /api/admin/test-series/generate - Get test generation templates
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest, { user }) => {
    try {
      const templates = {
        testTypes: [
          {
            value: 'DAILY_PRACTICE',
            label: 'Daily Practice',
            description: 'Quick daily revision questions',
            defaultQuestions: 5,
            defaultTime: 10,
            suggestedDifficulty: 'EASY'
          },
          {
            value: 'WEEKLY_TEST',
            label: 'Weekly Test',
            description: 'Comprehensive weekly assessment',
            defaultQuestions: 20,
            defaultTime: 40,
            suggestedDifficulty: 'MEDIUM'
          },
          {
            value: 'MONTHLY_TEST',
            label: 'Monthly Test',
            description: 'Monthly comprehensive evaluation',
            defaultQuestions: 50,
            defaultTime: 90,
            suggestedDifficulty: 'MEDIUM'
          },
          {
            value: 'TEST_SERIES',
            label: 'Test Series',
            description: 'Full syllabus exam simulation',
            defaultQuestions: 100,
            defaultTime: 180,
            suggestedDifficulty: 'HARD'
          }
        ],
        syllabusCoverage: [
          {
            value: 'CURRENT_WEEK',
            label: 'Current Week Topics',
            description: 'Focus on recently taught material'
          },
          {
            value: 'CURRENT_MONTH',
            label: 'Current Month Topics',
            description: 'Cover all topics from current month'
          },
          {
            value: 'FULL_SYLLABUS',
            label: 'Full Syllabus',
            description: 'Comprehensive coverage of entire syllabus'
          },
          {
            value: 'SPECIFIC_TOPICS',
            label: 'Specific Topics',
            description: 'Focus on selected topics only'
          }
        ],
        examPatterns: [
          'JEE_MAIN', 'JEE_ADVANCED', 'NEET', 'UPSC_PRELIMS', 'SSC_CGL',
          'GATE', 'CAT', 'CLAT', 'CBSE_BOARD', 'ICSE_BOARD',
          'AIIMS_NURSING', 'PGIMER_NURSING', 'CMC_LUDHIANA', 'MILITARY_NURSING',
          'BHU_NURSING', 'KGMU_NURSING', 'RUHS_NURSING', 'JENPAS_UG',
          'JEPBN_POST_BASIC', 'ANM_GNM_DIPLOMA', 'GNM_ELIGIBILITY',
          'RRB_STAFF_NURSE', 'AIIMS_NORCET', 'CHO_RECRUITMENT'
        ]
      }

      return APIResponse.success(templates, 'Test generation templates retrieved successfully')
    } catch (error) {
      console.error('Error fetching test templates:', error)
      return APIResponse.error('Failed to fetch test templates', 500)
    }
  }
)
