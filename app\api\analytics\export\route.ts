import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
 import { createAPIHandler, APIResponse } from '@/lib/api-middleware'
import { AnalyticsService } from '@/lib/analytics-service'

const exportQuerySchema = z.object({
  format: z.enum(['csv', 'json', 'pdf']).default('json'),
  period: z.enum(['7d', '30d', '90d', '1y']).default('30d'),
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional()
})

// GET /api/analytics/export - Export analytics data
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
  
    validateQuery: exportQuerySchema
  },
  async (request: NextRequest, { validatedQuery }) => {
    const { format, period, startDate, endDate } = validatedQuery

    // Calculate time range
    let timeRange
    if (startDate && endDate) {
      timeRange = {
        start: new Date(startDate),
        end: new Date(endDate)
      }
    } else {
      const end = new Date()
      const start = new Date()
      
      switch (period) {
        case '7d':
          start.setDate(end.getDate() - 7)
          break
        case '30d':
          start.setDate(end.getDate() - 30)
          break
        case '90d':
          start.setDate(end.getDate() - 90)
          break
        case '1y':
          start.setFullYear(end.getFullYear() - 1)
          break
      }
      
      timeRange = { start, end }
    }

    // Gather all analytics data with error handling
    let overview, engagement, quizMetrics, systemMetrics

    try {
      [overview, engagement, quizMetrics, systemMetrics] = await Promise.all([
        AnalyticsService.getPlatformOverview(timeRange),
        AnalyticsService.getUserEngagementMetrics(timeRange),
        AnalyticsService.getQuizPerformanceMetrics(timeRange),
        AnalyticsService.getSystemHealthMetrics(timeRange)
      ])
    } catch (error) {
      console.error('Analytics data gathering error:', error)

      // Provide fallback data if analytics service fails
      overview = {
        totalUsers: 0,
        activeUsers: 0,
        totalQuizzes: 0,
        totalAttempts: 0,
        totalQuestions: 0,
        averageScore: 0,
        completionRate: 0,
        totalNotifications: 0,
        totalFiles: 0,
        totalApiKeys: 0
      }

      engagement = {
        dailyActiveUsers: [],
        weeklyActiveUsers: [],
        monthlyActiveUsers: [],
        userRetention: { day1: 0, day7: 0, day30: 0 },
        sessionDuration: { average: 0, median: 0, distribution: [] }
      }

      quizMetrics = {
        topPerformingQuizzes: [],
        quizDifficultyAnalysis: [],
        questionAnalysis: { totalQuestions: 0, averageDifficulty: 0, topCategories: [] }
      }

      systemMetrics = {
        apiUsage: { totalRequests: 0, averageResponseTime: 0, errorRate: 0, topEndpoints: [] },
        fileStorage: { totalFiles: 0, totalSize: 0, uploadTrends: [] },
        notifications: { totalSent: 0, deliveryRate: 0, readRate: 0, clickRate: 0, topCategories: [] }
      }
    }

    const analyticsData = {
      exportInfo: {
        generatedAt: new Date().toISOString(),
        period,
        timeRange,
        format
      },
      overview,
      engagement,
      quizMetrics,
      systemMetrics
    }

    switch (format) {
      case 'json':
        return new NextResponse(JSON.stringify(analyticsData, null, 2), {
          headers: {
            'Content-Type': 'application/json',
            'Content-Disposition': `attachment; filename="analytics-${period}-${new Date().toISOString().split('T')[0]}.json"`
          }
        })

      case 'csv':
        const csv = generateCSV(analyticsData)
        return new NextResponse(csv, {
          headers: {
            'Content-Type': 'text/csv',
            'Content-Disposition': `attachment; filename="analytics-${period}-${new Date().toISOString().split('T')[0]}.csv"`
          }
        })

      case 'pdf':
        const pdfBlob = await generatePDF(analyticsData)
        const pdfBuffer = Buffer.from(await pdfBlob.arrayBuffer())
        return new NextResponse(pdfBuffer, {
          headers: {
            'Content-Type': 'application/pdf',
            'Content-Disposition': `attachment; filename="analytics-report-${period}-${new Date().toISOString().split('T')[0]}.pdf"`
          }
        })

      default:
        return APIResponse.error('Invalid export format', 400, 'INVALID_FORMAT')
    }
  }
)

function generateCSV(data: any): string {
  const lines: string[] = []
  
  // Header
  lines.push('Analytics Export Report')
  lines.push(`Generated: ${data.exportInfo.generatedAt}`)
  lines.push(`Period: ${data.exportInfo.period}`)
  lines.push('')

  // Overview metrics
  lines.push('OVERVIEW METRICS')
  lines.push('Metric,Value')
  lines.push(`Total Users,${data.overview.totalUsers}`)
  lines.push(`Active Users,${data.overview.activeUsers}`)
  lines.push(`Total Quizzes,${data.overview.totalQuizzes}`)
  lines.push(`Total Attempts,${data.overview.totalAttempts}`)
  lines.push(`Total Questions,${data.overview.totalQuestions}`)
  lines.push(`Average Score,${data.overview.averageScore.toFixed(2)}%`)
  lines.push(`Completion Rate,${data.overview.completionRate.toFixed(2)}%`)
  lines.push(`Total Notifications,${data.overview.totalNotifications}`)
  lines.push(`Total Files,${data.overview.totalFiles}`)
  lines.push(`Total API Keys,${data.overview.totalApiKeys}`)
  lines.push('')

  // Daily active users
  if (data.engagement.dailyActiveUsers.length > 0) {
    lines.push('DAILY ACTIVE USERS')
    lines.push('Date,Active Users')
    data.engagement.dailyActiveUsers.forEach((item: any) => {
      lines.push(`${item.date},${item.count}`)
    })
    lines.push('')
  }

  // Top performing quizzes
  if (data.quizMetrics.topPerformingQuizzes.length > 0) {
    lines.push('TOP PERFORMING QUIZZES')
    lines.push('Title,Attempts,Average Score,Completion Rate,Difficulty,Type')
    data.quizMetrics.topPerformingQuizzes.forEach((quiz: any) => {
      lines.push(`"${quiz.title}",${quiz.attempts},${quiz.averageScore.toFixed(2)},${quiz.completionRate.toFixed(2)},${quiz.difficulty},${quiz.type}`)
    })
    lines.push('')
  }

  // API usage
  lines.push('API USAGE')
  lines.push('Metric,Value')
  lines.push(`Total Requests,${data.systemMetrics.apiUsage.totalRequests}`)
  lines.push(`Average Response Time,${data.systemMetrics.apiUsage.averageResponseTime.toFixed(2)}ms`)
  lines.push(`Error Rate,${data.systemMetrics.apiUsage.errorRate.toFixed(2)}%`)
  lines.push('')

  // Top API endpoints
  if (data.systemMetrics.apiUsage.topEndpoints.length > 0) {
    lines.push('TOP API ENDPOINTS')
    lines.push('Endpoint,Requests,Average Time (ms)')
    data.systemMetrics.apiUsage.topEndpoints.forEach((endpoint: any) => {
      lines.push(`${endpoint.endpoint},${endpoint.requests},${endpoint.avgTime}`)
    })
    lines.push('')
  }

  // File storage
  lines.push('FILE STORAGE')
  lines.push('Metric,Value')
  lines.push(`Total Files,${data.systemMetrics.fileStorage.totalFiles}`)
  lines.push(`Total Size (bytes),${data.systemMetrics.fileStorage.totalSize}`)
  lines.push('')

  // Notifications
  lines.push('NOTIFICATIONS')
  lines.push('Metric,Value')
  lines.push(`Total Sent,${data.systemMetrics.notifications.totalSent}`)
  lines.push(`Delivery Rate,${data.systemMetrics.notifications.deliveryRate.toFixed(2)}%`)
  lines.push(`Read Rate,${data.systemMetrics.notifications.readRate.toFixed(2)}%`)
  lines.push(`Click Rate,${data.systemMetrics.notifications.clickRate.toFixed(2)}%`)

  return lines.join('\n')
}

async function generatePDF(data: any): Promise<Blob> {
  // Import the working PDF generator
  const { generateAnalyticsReportPDF } = await import('@/lib/pdf-generator')

  // Transform the data to match the expected AnalyticsData format
  const analyticsData = {
    overview: {
      totalAttempts: data.overview.totalAttempts || 0,
      averageScore: data.overview.averageScore || 0,
      totalTimeSpent: data.engagement?.sessionDuration?.total || 0,
      improvementRate: 0 // This would need to be calculated from historical data
    },
    performanceByCategory: data.quizMetrics?.categoryPerformance || [],
    performanceByDifficulty: data.quizMetrics?.difficultyBreakdown || [],
    weeklyProgress: data.engagement?.weeklyProgress || []
  }

  // Generate the PDF using the working PDF generator
  const pdfBlob = await generateAnalyticsReportPDF(analyticsData, 'Platform Analytics')
  return pdfBlob
}


