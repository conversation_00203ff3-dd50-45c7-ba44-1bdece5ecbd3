# AI Quiz Creator Setup Guide

## Quick Start

The AI Quiz Creator is **optimized for OpenAI** and uses GPT-4o models for the best experience. You only need an OpenAI API key to get started.

### Step 1: Get OpenAI API Key (Required)

The system is configured to use OpenAI models for all tasks:

1. Go to [OpenAI API Keys](https://platform.openai.com/api-keys)
2. Create an account or sign in
3. Click "Create new secret key"
4. Give it a name (e.g., "Quiz Creator")
5. Copy the key (starts with `sk-`)

**That's it!** You only need OpenAI for the full experience.

### Optional: Additional Providers

If you want to experiment with other AI models, you can also configure:

- **Anthropic**: [Console](https://console.anthropic.com/) - For Claude models
- **Google AI**: [AI Studio](https://aistudio.google.com/app/apikey) - For Gemini models
- **Groq**: [Console](https://console.groq.com/keys) - For ultra-fast inference

But **OpenAI alone is sufficient** for all quiz creation tasks.

### Step 2: Configure Environment Variables

1. Create a `.env.local` file in your project root
2. Add your OpenAI API key:

```env
# Required: OpenAI API Key
OPENAI_API_KEY=sk-your-actual-openai-key-here

# Optional: Additional providers (if you want to experiment)
# ANTHROPIC_API_KEY=your_anthropic_api_key_here
# GOOGLE_AI_API_KEY=your_google_ai_api_key_here
# GROQ_API_KEY=your_groq_api_key_here
```

**Important:** Only the OpenAI key is required. The system will automatically use:
- **GPT-4o** for complex reasoning and question generation
- **GPT-4o Mini** for fast content analysis

### Step 3: Restart Your Development Server

After adding the API keys, restart your development server:

```bash
npm run dev
```

## Usage

### Creating a Quiz with AI

1. Go to **Admin Panel** → **Quiz Management** → **Create Quiz**
2. Click the **"AI Quiz Creator"** button (purple gradient button)
3. Follow the 3-step process:
   - **Step 1**: Upload content or paste text
   - **Step 2**: Configure settings and AI models
   - **Step 3**: Generate your quiz

### AI Settings Management

Visit **Admin Panel** → **AI Management** to:
- Configure default AI models for different tasks
- Monitor AI agent performance
- Set cost limits and preferences
- View real-time AI usage statistics

## Default OpenAI Configuration

The system automatically uses the optimal OpenAI configuration:

### Automatic Configuration (Recommended)
```
Orchestrator: GPT-4o          # Complex reasoning and planning
Content Analyzer: GPT-4o Mini # Fast content analysis
Question Generator: GPT-4o    # High-quality question creation
Quality Evaluator: GPT-4o     # Thorough quality assessment
```

This provides the **best balance of quality, speed, and cost** using only OpenAI models.

### Why This Configuration?
- **GPT-4o**: Excellent for creative tasks like question generation and quality evaluation
- **GPT-4o Mini**: 60% cheaper and 2x faster for analysis tasks
- **Consistent**: All models from the same provider ensure compatibility
- **Simple**: Only one API key needed

## Troubleshooting

### "Analysis failed" Error
- **Cause**: Missing or invalid API keys
- **Solution**: Check your `.env.local` file and ensure API keys are correct
- **Check**: Restart your development server after adding keys

### "No AI models available" Error
- **Cause**: No valid API keys configured
- **Solution**: Add at least one API key to your `.env.local` file

### API Key Not Working
- **Check**: API key format (OpenAI keys start with `sk-`)
- **Check**: API key has sufficient credits/quota
- **Check**: No extra spaces or quotes around the key in `.env.local`

### Slow Generation
- **Solution**: Use faster models like Groq or Claude Haiku
- **Solution**: Enable "Prioritize Speed" in preferences

## Cost Management

### Estimated Costs (per quiz with 10 questions)
- **GPT-4o**: ~$0.10-0.20
- **GPT-4o Mini**: ~$0.02-0.05
- **Claude 3.5 Sonnet**: ~$0.15-0.25
- **Claude 3.5 Haiku**: ~$0.03-0.06
- **Gemini 2.0 Flash**: ~$0.01-0.03

### Cost Optimization Tips
1. Use cheaper models for content analysis
2. Use premium models only for question generation
3. Set daily spending limits in AI Settings
4. Monitor usage in the AI Dashboard

## Support

If you encounter issues:
1. Check this setup guide
2. Verify your API keys are correct
3. Check the browser console for detailed error messages
4. Ensure your development server is restarted after adding keys

## Security Notes

- Never commit `.env.local` to version control
- Keep your API keys secure and private
- Regularly rotate your API keys
- Monitor your API usage and costs
