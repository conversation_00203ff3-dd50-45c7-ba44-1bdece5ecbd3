# AI-Powered Quiz Creation System

## Overview

The AI-Powered Quiz Creation System is a comprehensive, multi-agent AI solution that revolutionizes how educational quizzes are created. Built with the Vercel AI SDK, it leverages multiple AI models working together to create high-quality, educational quizzes from any content.

## 🚀 Key Features

### Multi-Agent Architecture
- **Quiz Orchestrator**: Plans and coordinates the entire quiz creation process
- **Content Analyzer**: Analyzes and understands input content structure
- **Question Generator**: Creates diverse, high-quality quiz questions
- **Quality Evaluator**: Reviews and improves question quality

### Multiple AI Model Support
- **OpenAI**: GPT-4o, GPT-4o Mini, o3 Mini
- **Anthropic**: Claude 3.5 Sonnet, Claude 3.5 Haiku
- **Google**: Gemini 2.0 Flash
- **xAI**: Grok 3
- **DeepSeek**: DeepSeek Reasoner
- **Groq**: Llama 3.3 70B (Ultra-fast inference)

### Advanced Capabilities
- **Content Analysis**: Automatic topic detection, difficulty assessment, learning objectives
- **Smart Tag Generation**: AI-powered tag suggestions with categorization
- **Quality Assessment**: Real-time question quality analysis and improvement
- **Difficulty Calibration**: Automatic difficulty level assessment and adjustment
- **Multi-format Support**: Text, PDF, DOC, MD file processing

## 🏗️ Architecture

### Agent System
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Quiz            │    │ Content         │    │ Question        │
│ Orchestrator    │───▶│ Analyzer        │───▶│ Generator       │
│ (GPT-4o)        │    │ (Claude Haiku)  │    │ (GPT-4o)        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                                              │
         ▼                                              ▼
┌─────────────────┐                          ┌─────────────────┐
│ Quality         │◀─────────────────────────│ Generated       │
│ Evaluator       │                          │ Questions       │
│ (Claude Sonnet) │                          │                 │
└─────────────────┘                          └─────────────────┘
```

### Model Selection Strategy
- **Reasoning Tasks**: o3 Mini, GPT-4o, Claude 3.5 Sonnet
- **Fast Analysis**: Claude 3.5 Haiku, Gemini 2.0 Flash
- **Content Generation**: GPT-4o, Claude 3.5 Sonnet
- **Real-time Tasks**: Groq Llama 3.3 70B

## 🛠️ Implementation

### Core Components

#### 1. AI Provider Manager (`lib/ai-providers.ts`)
```typescript
export const AI_MODELS: AIModel[] = [
  {
    id: 'gpt-4o',
    name: 'GPT-4o',
    provider: 'OpenAI',
    capabilities: {
      reasoning: true,
      multimodal: true,
      toolCalling: true
    },
    useCase: ['complex-analysis', 'reasoning']
  }
  // ... more models
]

export function getBestModelForTask(task: {
  useCase: string
  prioritizeSpeed?: boolean
  prioritizeQuality?: boolean
}) {
  // Intelligent model selection logic
}
```

#### 2. Quiz Creation Agent (`lib/ai-agents/quiz-creation-agent.ts`)
```typescript
export class QuizCreationAgent {
  async createQuiz(input: {
    content?: string
    files?: Array<{ name: string; content: string; type: string }>
    requirements?: QuizRequirements
    preferences?: GenerationPreferences
  }) {
    // Step 1: Analyze content
    const analysis = await this.analyzeContent(input)
    
    // Step 2: Plan quiz structure
    const structure = await this.planQuizStructure(analysis)
    
    // Step 3: Generate questions in parallel
    const questions = await this.generateQuestions(structure)
    
    // Step 4: Evaluate and improve quality
    const evaluatedQuestions = await this.evaluateAndImproveQuestions(questions)
    
    // Step 5: Finalize quiz
    return await this.finalizeQuiz(structure, evaluatedQuestions)
  }
}
```

#### 3. Modern UI Components

##### AI Quiz Creator (`components/admin/ai-quiz-creator.tsx`)
- **Step 1**: Content Input (Text/File Upload)
- **Step 2**: Configuration (Models, Settings, Preferences)
- **Step 3**: Generation (Real-time Progress, Multi-agent Coordination)

##### AI Model Selector (`components/admin/ai-model-selector.tsx`)
- Model comparison and selection
- Performance metrics display
- Cost analysis
- Recommendation engine

##### AI Agent Dashboard (`components/admin/ai-agent-dashboard.tsx`)
- Real-time agent status monitoring
- Performance metrics
- Cost tracking
- System health indicators

### API Endpoints

#### Quiz Creation
```
POST /api/ai/quiz-creation
- Creates complete quiz using AI agents
- Supports multiple input formats
- Configurable model selection
- Real-time progress tracking

GET /api/ai/quiz-creation
- Returns available AI models
- Provides model recommendations
- Shows pricing and capabilities

POST /api/ai/quiz-creation/analyze
- Analyzes content before quiz creation
- Provides recommendations
- Estimates generation time and cost
```

## 🎯 Usage Examples

### Basic Quiz Creation
```typescript
const agent = new QuizCreationAgent()

const result = await agent.createQuiz({
  content: "Your educational content here...",
  requirements: {
    questionCount: 10,
    difficulty: 'MEDIUM',
    questionTypes: ['MCQ', 'TRUE_FALSE']
  },
  preferences: {
    prioritizeQuality: true,
    includeExplanations: true
  }
})
```

### Advanced Configuration
```typescript
const agent = new QuizCreationAgent({
  orchestratorModel: 'o3-mini',
  contentAnalyzer: 'claude-3-5-haiku-20241022',
  questionGenerator: 'gpt-4o',
  qualityEvaluator: 'claude-3-5-sonnet-20241022'
})
```

### File-based Quiz Creation
```typescript
const result = await agent.createQuiz({
  files: [
    { name: 'chapter1.pdf', content: pdfContent, type: 'application/pdf' },
    { name: 'notes.md', content: markdownContent, type: 'text/markdown' }
  ],
  requirements: {
    questionCount: 15,
    targetAudience: 'High school students'
  }
})
```

## 📊 Quality Metrics

### Question Quality Assessment
- **Clarity Score**: Language clarity and ambiguity detection
- **Educational Value**: Alignment with learning objectives
- **Difficulty Calibration**: Appropriate challenge level
- **Bias Detection**: Cultural and demographic bias analysis
- **Grammar Check**: Language correctness validation

### Performance Metrics
- **Generation Speed**: Average time per question
- **Quality Score**: Overall quiz quality rating
- **Success Rate**: Percentage of successful generations
- **Cost Efficiency**: Cost per high-quality question

## 🔧 Configuration

### Environment Variables
```env
# AI Model API Keys
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_anthropic_key
GOOGLE_AI_API_KEY=your_google_key
XAI_API_KEY=your_xai_key
GROQ_API_KEY=your_groq_key
DEEPSEEK_API_KEY=your_deepseek_key

# Optional: Model-specific configurations
AI_DEFAULT_ORCHESTRATOR=gpt-4o
AI_DEFAULT_ANALYZER=claude-3-5-haiku-20241022
AI_DEFAULT_GENERATOR=gpt-4o
AI_DEFAULT_EVALUATOR=claude-3-5-sonnet-20241022
```

### Model Selection Guidelines

#### For High Quality (Prioritize Accuracy)
- **Orchestrator**: o3 Mini or GPT-4o
- **Content Analyzer**: Claude 3.5 Sonnet
- **Question Generator**: GPT-4o or Claude 3.5 Sonnet
- **Quality Evaluator**: Claude 3.5 Sonnet

#### For Speed (Prioritize Performance)
- **Orchestrator**: GPT-4o Mini
- **Content Analyzer**: Claude 3.5 Haiku or Gemini 2.0 Flash
- **Question Generator**: GPT-4o Mini
- **Quality Evaluator**: Claude 3.5 Haiku

#### For Cost Efficiency
- **Orchestrator**: Gemini 2.0 Flash
- **Content Analyzer**: Gemini 2.0 Flash
- **Question Generator**: GPT-4o Mini
- **Quality Evaluator**: Claude 3.5 Haiku

## 🚀 Getting Started

1. **Install Dependencies**
   ```bash
   npm install @ai-sdk/openai @ai-sdk/anthropic @ai-sdk/google @ai-sdk/xai @ai-sdk/groq @ai-sdk/deepseek ai
   ```

2. **Configure API Keys**
   Add your AI provider API keys to `.env.local`

3. **Initialize AI Quiz Creator**
   ```typescript
   import { AIQuizCreator } from '@/components/admin/ai-quiz-creator'
   
   // Use in your React component
   <AIQuizCreator
     onQuizGenerated={handleQuizGenerated}
     onClose={handleClose}
   />
   ```

4. **Access AI Settings**
   Navigate to `/admin/ai-settings` for comprehensive configuration

## 🔮 Future Enhancements

- **Adaptive Learning**: Personalized question difficulty based on student performance
- **Multi-language Support**: Quiz generation in multiple languages
- **Image Generation**: AI-generated diagrams and illustrations
- **Voice Integration**: Audio question generation
- **Collaborative Agents**: Multiple agents working on different quiz sections simultaneously
- **Real-time Optimization**: Dynamic model switching based on performance metrics

## 📈 Performance Optimization

### Parallel Processing
- Questions generated in parallel batches
- Concurrent model inference
- Asynchronous quality evaluation

### Caching Strategy
- Model response caching
- Content analysis caching
- Frequently used prompt templates

### Cost Optimization
- Intelligent model routing
- Token usage optimization
- Batch processing for similar tasks

## 🛡️ Best Practices

1. **Model Selection**: Choose models based on task requirements
2. **Quality Thresholds**: Set minimum quality scores for acceptance
3. **Cost Monitoring**: Implement daily/monthly spending limits
4. **Error Handling**: Graceful fallbacks for model failures
5. **Content Validation**: Verify input content quality before processing
6. **User Feedback**: Collect feedback to improve model selection

This AI-powered quiz creation system represents the cutting edge of educational technology, combining multiple AI models in a sophisticated agent architecture to create the highest quality educational content possible.
