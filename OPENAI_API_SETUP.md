# OpenAI API Key Setup Guide

## The Change
I've switched the quiz generation from Google Gemini to OpenAI GPT-4o-mini for better reliability and performance.

## How to Get an OpenAI API Key

### Step 1: Go to OpenAI Platform
1. Visit [OpenAI Platform](https://platform.openai.com/)
2. Sign in with your OpenAI account (or create one)

### Step 2: Create an API Key
1. Go to the [API Keys page](https://platform.openai.com/api-keys)
2. Click "Create new secret key"
3. Give it a name (e.g., "Quiz Generator")
4. Copy the generated API key (it starts with "sk-...")

### Step 3: Update Your Environment File
Add this to your `.env.local` file:
```
OPENAI_API_KEY=sk-your-actual-api-key-here
```

### Step 4: Restart Your Development Server
After updating the `.env.local` file, restart your Next.js development server:
```bash
npm run dev
```

## Benefits of Using OpenAI

### 1. **Better Reliability**
- More consistent API responses
- Better error handling
- More stable service

### 2. **Improved Question Quality**
- GPT-4o-mini generates higher quality questions
- Better understanding of context
- More natural language processing

### 3. **Cost Effective**
- GPT-4o-mini is very cost-effective
- Good balance of quality and price
- Suitable for production use

## Testing the Setup

### Option 1: Use the Debug Script
```bash
node debug-env.js
```

### Option 2: Test in Admin Panel
1. Go to your admin panel
2. Try generating a quiz using the AI generator
3. Check both terminal and browser console for logs

## Expected Behavior
After setting up the OpenAI API key correctly, you should see:

**Terminal Console:**
```
OpenAI API Key status: Present (sk-proj-Ab...)
Request body: {...}
Processing text content, length: X
Starting quiz generation stream...
Using model: gpt-4o-mini
StreamObject created successfully
Text stream response created
Quiz generation completed successfully with X questions
```

**Browser Console:**
```
Loading state changed: true
Loading state changed: false
onFinish called with object: [array of questions]
Generated questions: [questions array]
```

## Troubleshooting

### Common Issues:
1. **"OpenAI API key not configured"** - Make sure you added the key to `.env.local`
2. **"Invalid API key"** - Check that you copied the entire key correctly
3. **"Insufficient quota"** - You may need to add billing to your OpenAI account

### API Key Format:
- OpenAI API keys start with `sk-`
- They are much longer than Google API keys
- Make sure there are no extra spaces or characters

## Security Note
- Never commit your actual API key to version control
- The `.env.local` file should be in your `.gitignore`
- Keep your API key secure and don't share it publicly
