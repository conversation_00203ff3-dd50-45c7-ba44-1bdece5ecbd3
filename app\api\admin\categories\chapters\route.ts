import { NextRequest, NextResponse } from 'next/server'

import { prisma } from '@/lib/prisma'
import { z } from 'zod'
import { auth } from '@/auth'

const createChapterSchema = z.object({
  name: z.string().min(1, "Chapter name is required"),
  description: z.string().optional(),
  subjectId: z.string().min(1, "Subject ID is required"),
  order: z.number().optional(),
})

// GET /api/admin/categories/chapters - Get chapters by subject
export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const subjectId = searchParams.get('subjectId')

    const whereClause = {
      isActive: true,
      ...(subjectId && { subjectId })
    }

    const chapters = await prisma.chapter.findMany({
      where: whereClause,
      include: {
        subject: true,
        topics: {
          where: { isActive: true },
          orderBy: { order: 'asc' }
        },
        _count: {
          select: {
            quizzes: true,
            topics: true
          }
        }
      },
      orderBy: { order: 'asc' }
    })

    return NextResponse.json({
      chapters,
      success: true
    })
  } catch (error) {
    console.error('Error fetching chapters:', error)
    return NextResponse.json(
      { error: 'Failed to fetch chapters' },
      { status: 500 }
    )
  }
}

// POST /api/admin/categories/chapters - Create new chapter
export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const validatedData = createChapterSchema.parse(body)

    // Check if chapter already exists in this subject
    const existingChapter = await prisma.chapter.findUnique({
      where: {
        subjectId_name: {
          subjectId: validatedData.subjectId,
          name: validatedData.name
        }
      }
    })

    if (existingChapter) {
      return NextResponse.json(
        { error: 'Chapter with this name already exists in this subject' },
        { status: 400 }
      )
    }

    // Get the next order number if not provided
    let order = validatedData.order
    if (order === undefined) {
      const lastChapter = await prisma.chapter.findFirst({
        where: { subjectId: validatedData.subjectId },
        orderBy: { order: 'desc' }
      })
      order = (lastChapter?.order || 0) + 1
    }

    const chapter = await prisma.chapter.create({
      data: {
        ...validatedData,
        order
      },
      include: {
        subject: true,
        topics: true,
        _count: {
          select: {
            quizzes: true,
            topics: true
          }
        }
      }
    })

    return NextResponse.json({
      chapter,
      success: true
    })
  } catch (error) {
    console.error('Error creating chapter:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Failed to create chapter' },
      { status: 500 }
    )
  }
}
