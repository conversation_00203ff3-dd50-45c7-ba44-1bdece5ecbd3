'use client'

import { useState } from 'react'
import { Share2, Facebook, Twitter, Linkedin, MessageCircle, Mail, Copy, Check } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { toast } from 'sonner'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'

interface QuizShareButtonProps {
  quizId: string
  className?: string
  variant?: 'default' | 'ghost' | 'outline'
  size?: 'default' | 'sm' | 'lg' | 'icon'
  showText?: boolean
}

interface ShareData {
  quiz: {
    id: string
    title: string
    description?: string
    type: string
    difficulty: string
    questionCount: number
    averageRating: number
    reviewCount: number
    creator: {
      name: string
    }
  }
  sharing: {
    url: string
    message: string
    platforms: string[]
  }
}

export function QuizShareButton({ 
  quizId, 
  className,
  variant = 'outline',
  size = 'default',
  showText = true
}: QuizShareButtonProps) {
  const [shareData, setShareData] = useState<ShareData | null>(null)
  const [loading, setLoading] = useState(false)
  const [showCustomDialog, setShowCustomDialog] = useState(false)
  const [customMessage, setCustomMessage] = useState('')
  const [copied, setCopied] = useState(false)

  const fetchShareData = async () => {
    if (shareData) return shareData

    setLoading(true)
    try {
      const response = await fetch(`/api/student/quizzes/${quizId}/share`)
      if (response.ok) {
        const data = await response.json()
        setShareData(data)
        setCustomMessage(data.sharing.message)
        return data
      } else {
        toast.error('Failed to load sharing information')
        return null
      }
    } catch (error) {
      console.error('Error fetching share data:', error)
      toast.error('Failed to load sharing information')
      return null
    } finally {
      setLoading(false)
    }
  }

  const handleShare = async (platform: string, customMsg?: string) => {
    try {
      const response = await fetch(`/api/student/quizzes/${quizId}/share`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          platform,
          message: customMsg
        })
      })

      if (response.ok) {
        const data = await response.json()

        if (platform === 'copy') {
          const urlToCopy = data.sharing?.url || data.sharing?.directUrl || `${window.location.origin}/student/browse/${quizId}`
          await navigator.clipboard.writeText(urlToCopy)
          setCopied(true)
          toast.success('Link copied to clipboard!')
          setTimeout(() => setCopied(false), 2000)
        } else {
          // Open sharing URL in new window
          const shareUrl = data.sharing?.url || `${window.location.origin}/student/browse/${quizId}`
          window.open(shareUrl, '_blank', 'width=600,height=400')
          toast.success(`Shared on ${platform}!`)
        }
      } else {
        const errorData = await response.json().catch(() => ({}))
        console.error('Share API Error:', errorData)
        toast.error(errorData.message || 'Failed to generate sharing link')
      }
    } catch (error) {
      console.error('Error sharing:', error)
      toast.error('Failed to share quiz')
    }
  }

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopied(true)
      toast.success('Copied to clipboard!')
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error('Error copying to clipboard:', error)
      toast.error('Failed to copy to clipboard')
    }
  }

  const shareOptions = [
    {
      platform: 'facebook',
      label: 'Facebook',
      icon: Facebook,
      color: 'text-blue-600'
    },
    {
      platform: 'twitter',
      label: 'Twitter',
      icon: Twitter,
      color: 'text-blue-400'
    },
    {
      platform: 'linkedin',
      label: 'LinkedIn',
      icon: Linkedin,
      color: 'text-blue-700'
    },
    {
      platform: 'whatsapp',
      label: 'WhatsApp',
      icon: MessageCircle,
      color: 'text-green-600'
    },
    {
      platform: 'email',
      label: 'Email',
      icon: Mail,
      color: 'text-gray-600'
    },
    {
      platform: 'copy',
      label: 'Copy Link',
      icon: copied ? Check : Copy,
      color: copied ? 'text-green-600' : 'text-gray-600'
    }
  ]

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant={variant}
            size={size}
            onClick={fetchShareData}
            disabled={loading}
            className={className}
          >
            <Share2 className="h-4 w-4" />
            {showText && size !== 'icon' && (
              <span className="ml-2">
                {loading ? 'Loading...' : 'Share'}
              </span>
            )}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-48">
          <DropdownMenuLabel>Share this quiz</DropdownMenuLabel>
          <DropdownMenuSeparator />
          
          {shareOptions.map((option) => {
            const IconComponent = option.icon
            return (
              <DropdownMenuItem
                key={option.platform}
                onClick={() => handleShare(option.platform)}
                className="cursor-pointer"
              >
                <IconComponent className={`h-4 w-4 mr-2 ${option.color}`} />
                {option.label}
              </DropdownMenuItem>
            )
          })}
          
          <DropdownMenuSeparator />
          <DropdownMenuItem
            onClick={() => setShowCustomDialog(true)}
            className="cursor-pointer"
          >
            <Share2 className="h-4 w-4 mr-2" />
            Custom Message
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Custom Share Dialog */}
      <Dialog open={showCustomDialog} onOpenChange={setShowCustomDialog}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Share Quiz</DialogTitle>
            <DialogDescription>
              Customize your sharing message and choose how to share this quiz.
            </DialogDescription>
          </DialogHeader>
          
          {shareData && (
            <div className="space-y-4">
              {/* Quiz Info */}
              <div className="bg-muted p-4 rounded-lg">
                <h4 className="font-medium">{shareData.quiz.title}</h4>
                <p className="text-sm text-muted-foreground">
                  {shareData.quiz.type} • {shareData.quiz.difficulty} • {shareData.quiz.questionCount} questions
                </p>
                {shareData.quiz.averageRating > 0 && (
                  <p className="text-sm text-muted-foreground">
                    ⭐ {shareData.quiz.averageRating} ({shareData.quiz.reviewCount} reviews)
                  </p>
                )}
              </div>

              {/* Custom Message */}
              <div>
                <Label htmlFor="customMessage">Message</Label>
                <Textarea
                  id="customMessage"
                  value={customMessage}
                  onChange={(e) => setCustomMessage(e.target.value)}
                  placeholder="Write a custom message..."
                  rows={3}
                  className="mt-2"
                />
              </div>

              {/* Share URL */}
              <div>
                <Label htmlFor="shareUrl">Share URL</Label>
                <div className="flex gap-2 mt-2">
                  <Input
                    id="shareUrl"
                    value={shareData.sharing.url}
                    readOnly
                    className="flex-1"
                  />
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => copyToClipboard(shareData.sharing.url)}
                  >
                    {copied ? (
                      <Check className="h-4 w-4 text-green-600" />
                    ) : (
                      <Copy className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>

              {/* Share Options */}
              <div>
                <Label>Share on</Label>
                <div className="grid grid-cols-3 gap-2 mt-2">
                  {shareOptions.slice(0, -1).map((option) => {
                    const IconComponent = option.icon
                    return (
                      <Button
                        key={option.platform}
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          handleShare(option.platform, customMessage)
                          setShowCustomDialog(false)
                        }}
                        className="flex items-center gap-2"
                      >
                        <IconComponent className={`h-4 w-4 ${option.color}`} />
                        {option.label}
                      </Button>
                    )
                  })}
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  )
}
