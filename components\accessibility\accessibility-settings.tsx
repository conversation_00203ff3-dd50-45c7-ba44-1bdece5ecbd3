"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { <PERSON>lider } from "@/components/ui/slider"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Eye, Keyboard, Volume2, MousePointer, Palette } from "lucide-react"

interface AccessibilitySettings {
  reducedMotion: boolean
  highContrast: boolean
  largeText: boolean
  fontSize: number
  keyboardNavigation: boolean
  screenReaderOptimized: boolean
  focusIndicators: boolean
  colorBlindFriendly: boolean
}

export function AccessibilitySettings() {
  const [settings, setSettings] = useState<AccessibilitySettings>({
    reducedMotion: false,
    highContrast: false,
    largeText: false,
    fontSize: 16,
    keyboardNavigation: true,
    screenReaderOptimized: false,
    focusIndicators: true,
    colorBlindFriendly: false
  })
  const [isClient, setIsClient] = useState(false)

  // Ensure we're on the client side
  useEffect(() => {
    setIsClient(true)
  }, [])

  // Load settings from localStorage on mount (client-side only)
  useEffect(() => {
    if (!isClient) return

    try {
      const savedSettings = localStorage.getItem('accessibility-settings')
      if (savedSettings) {
        setSettings(JSON.parse(savedSettings))
      }
    } catch (error) {
      console.warn('Failed to load accessibility settings:', error)
    }
  }, [isClient])

  // Save settings to localStorage and apply them (client-side only)
  useEffect(() => {
    if (!isClient) return

    try {
      localStorage.setItem('accessibility-settings', JSON.stringify(settings))
      applySettings(settings)
    } catch (error) {
      console.warn('Failed to save accessibility settings:', error)
    }
  }, [settings, isClient])

  const applySettings = (settings: AccessibilitySettings) => {
    const root = document.documentElement

    // Apply reduced motion
    if (settings.reducedMotion) {
      root.style.setProperty('--motion-reduce', '1')
      document.body.classList.add('reduce-motion')
    } else {
      root.style.removeProperty('--motion-reduce')
      document.body.classList.remove('reduce-motion')
    }

    // Apply high contrast
    if (settings.highContrast) {
      document.body.classList.add('high-contrast')
    } else {
      document.body.classList.remove('high-contrast')
    }

    // Apply large text
    if (settings.largeText) {
      document.body.classList.add('large-text')
    } else {
      document.body.classList.remove('large-text')
    }

    // Apply font size
    root.style.setProperty('--base-font-size', `${settings.fontSize}px`)

    // Apply focus indicators
    if (settings.focusIndicators) {
      document.body.classList.add('enhanced-focus')
    } else {
      document.body.classList.remove('enhanced-focus')
    }

    // Apply color blind friendly mode
    if (settings.colorBlindFriendly) {
      document.body.classList.add('colorblind-friendly')
    } else {
      document.body.classList.remove('colorblind-friendly')
    }

    // Apply screen reader optimizations
    if (settings.screenReaderOptimized) {
      document.body.classList.add('screen-reader-optimized')
    } else {
      document.body.classList.remove('screen-reader-optimized')
    }
  }

  const updateSetting = <K extends keyof AccessibilitySettings>(
    key: K,
    value: AccessibilitySettings[K]
  ) => {
    setSettings(prev => ({ ...prev, [key]: value }))
  }

  const resetSettings = () => {
    const defaultSettings: AccessibilitySettings = {
      reducedMotion: false,
      highContrast: false,
      largeText: false,
      fontSize: 16,
      keyboardNavigation: true,
      screenReaderOptimized: false,
      focusIndicators: true,
      colorBlindFriendly: false
    }
    setSettings(defaultSettings)
  }

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Eye className="h-5 w-5" />
          Accessibility Settings
        </CardTitle>
        <CardDescription>
          Customize the interface to meet your accessibility needs
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Visual Settings */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <Palette className="h-4 w-4" />
            Visual Settings
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="high-contrast" className="text-sm font-medium">
                High Contrast Mode
              </Label>
              <Switch
                id="high-contrast"
                checked={settings.highContrast}
                onCheckedChange={(checked) => updateSetting('highContrast', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="large-text" className="text-sm font-medium">
                Large Text
              </Label>
              <Switch
                id="large-text"
                checked={settings.largeText}
                onCheckedChange={(checked) => updateSetting('largeText', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="colorblind-friendly" className="text-sm font-medium">
                Color Blind Friendly
              </Label>
              <Switch
                id="colorblind-friendly"
                checked={settings.colorBlindFriendly}
                onCheckedChange={(checked) => updateSetting('colorBlindFriendly', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="reduced-motion" className="text-sm font-medium">
                Reduce Motion
              </Label>
              <Switch
                id="reduced-motion"
                checked={settings.reducedMotion}
                onCheckedChange={(checked) => updateSetting('reducedMotion', checked)}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label className="text-sm font-medium">
              Font Size: {settings.fontSize}px
            </Label>
            <Slider
              value={[settings.fontSize]}
              onValueChange={([value]) => updateSetting('fontSize', value)}
              min={12}
              max={24}
              step={1}
              className="w-full"
            />
          </div>
        </div>

        {/* Navigation Settings */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <Keyboard className="h-4 w-4" />
            Navigation Settings
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="keyboard-nav" className="text-sm font-medium">
                Enhanced Keyboard Navigation
              </Label>
              <Switch
                id="keyboard-nav"
                checked={settings.keyboardNavigation}
                onCheckedChange={(checked) => updateSetting('keyboardNavigation', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="focus-indicators" className="text-sm font-medium">
                Enhanced Focus Indicators
              </Label>
              <Switch
                id="focus-indicators"
                checked={settings.focusIndicators}
                onCheckedChange={(checked) => updateSetting('focusIndicators', checked)}
              />
            </div>
          </div>
        </div>

        {/* Screen Reader Settings */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <Volume2 className="h-4 w-4" />
            Screen Reader Settings
          </h3>
          
          <div className="flex items-center justify-between">
            <Label htmlFor="screen-reader" className="text-sm font-medium">
              Screen Reader Optimizations
            </Label>
            <Switch
              id="screen-reader"
              checked={settings.screenReaderOptimized}
              onCheckedChange={(checked) => updateSetting('screenReaderOptimized', checked)}
            />
          </div>
        </div>

        {/* Quick Presets */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Quick Presets</h3>
          <div className="flex flex-wrap gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setSettings({
                ...settings,
                highContrast: true,
                largeText: true,
                fontSize: 20,
                focusIndicators: true
              })}
            >
              Low Vision
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setSettings({
                ...settings,
                reducedMotion: true,
                keyboardNavigation: true,
                focusIndicators: true,
                screenReaderOptimized: true
              })}
            >
              Motor Impairment
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setSettings({
                ...settings,
                colorBlindFriendly: true,
                highContrast: true
              })}
            >
              Color Blind
            </Button>
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-between pt-4 border-t">
          <Button variant="outline" onClick={resetSettings}>
            Reset to Defaults
          </Button>
          <Badge variant="secondary">
            Settings saved automatically
          </Badge>
        </div>
      </CardContent>
    </Card>
  )
}
