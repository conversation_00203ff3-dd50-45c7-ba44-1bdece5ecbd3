// Debug script to check environment variables
// Run with: node debug-env.js

console.log('Environment Variables Check:');
console.log('==========================');

// Load environment variables
require('dotenv').config();

const envVars = [
  'OPENAI_API_KEY',
  'GOOGLE_API_KEY',
  'ANTHROPIC_API_KEY',
  'DATABASE_URL',
  'AUTH_SECRET'
];

envVars.forEach(varName => {
  const value = process.env[varName];
  if (value) {
    // Mask sensitive values for security
    const maskedValue = value.length > 10 
      ? value.substring(0, 10) + '...' + value.substring(value.length - 4)
      : value;
    console.log(`✅ ${varName}: ${maskedValue}`);
  } else {
    console.log(`❌ ${varName}: NOT SET`);
  }
});

console.log('\nOpenAI API Key Status:');
console.log('=====================');
const openaiKey = process.env.OPENAI_API_KEY;
if (!openaiKey) {
  console.log('❌ OPENAI_API_KEY is not set');
} else if (openaiKey === 'your-openai-api-key') {
  console.log('⚠️  OPENAI_API_KEY is set to placeholder value');
  console.log('   Please replace with actual API key from OpenAI Platform');
} else if (openaiKey.startsWith('sk-')) {
  console.log('✅ OPENAI_API_KEY appears to be valid (starts with sk-)');
} else {
  console.log('⚠️  OPENAI_API_KEY format may be incorrect');
  console.log('   OpenAI API keys typically start with "sk-"');
}

console.log('\nNext Steps:');
console.log('==========');
if (!openaiKey || openaiKey === 'your-openai-api-key') {
  console.log('1. Get an OpenAI API key from https://platform.openai.com/api-keys');
  console.log('2. Update your .env.local file with: OPENAI_API_KEY="sk-your-actual-key"');
  console.log('3. Restart your development server');
}
