const { io } = require('socket.io-client');

// Test if we can connect with the same user ID as the browser
async function testBrowserSession() {
  console.log('🧪 Testing browser session compatibility...');

  // Create socket connection with same user ID as browser
  const socket = io('http://localhost:3001', {
    transports: ['websocket', 'polling']
  });

  // Wait for connection
  await new Promise((resolve) => {
    socket.on('connect', () => {
      console.log('🔌 Connected:', socket.id);
      resolve();
    });
  });

  // Authenticate with same user ID as browser (from console logs)
  let authenticated = false;
  socket.on('authenticated', () => {
    console.log('✅ Authenticated');
    authenticated = true;
  });

  socket.emit('authenticate', {
    userId: 'cmdk6e3l60000kxpc1gjd2r6y', // Same as browser
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'STUDENT'
  });

  // Wait for authentication
  while (!authenticated) {
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  // Set up message listener
  const receivedMessages = [];
  socket.on('chat:message_received', (message) => {
    console.log('📥 Received message:', message);
    receivedMessages.push(message);
  });

  // Join room
  console.log('🏠 Joining room: student-general');
  socket.emit('chat:join', { roomId: 'student-general' });

  // Wait a moment for room join
  await new Promise(resolve => setTimeout(resolve, 1000));

  // Send message
  console.log('📤 Sending message...');
  socket.emit('chat:message', {
    roomId: 'student-general',
    message: 'Test from browser session test',
    type: 'text'
  });

  // Wait for message processing
  await new Promise(resolve => setTimeout(resolve, 3000));

  console.log('📊 Results:');
  console.log('Messages received:', receivedMessages.length);
  
  if (receivedMessages.length > 0) {
    console.log('✅ Browser session test PASSED');
    receivedMessages.forEach((msg, index) => {
      console.log(`Message ${index + 1}:`, msg.message);
    });
  } else {
    console.log('❌ Browser session test FAILED - no messages received');
  }

  socket.disconnect();
}

// Run the test
testBrowserSession().catch(console.error);
