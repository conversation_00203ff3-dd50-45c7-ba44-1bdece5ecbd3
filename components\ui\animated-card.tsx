'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface AnimatedCardProps {
  children: React.ReactNode;
  className?: string;
  variant?: 'glass' | 'solid' | 'outline' | 'gradient';
  hover?: 'lift' | 'glow' | 'scale' | 'tilt' | 'none';
  delay?: number;
  duration?: number;
}

export function AnimatedCard({
  children,
  className,
  variant = 'glass',
  hover = 'lift',
  delay = 0,
  duration = 0.6,
}: AnimatedCardProps) {
  const baseClasses = 'rounded-2xl p-6 transition-all duration-300';
  
  const variantClasses = {
    glass: 'glass backdrop-blur-xl border border-white/20 shadow-glass',
    solid: 'bg-white dark:bg-gray-900 shadow-xl border border-gray-200 dark:border-gray-700',
    outline: 'border-2 border-violet-200 dark:border-violet-800 bg-transparent',
    gradient: 'bg-gradient-primary text-white shadow-glow'
  };

  const hoverVariants = {
    lift: {
      hover: { y: -8, scale: 1.02, transition: { type: 'spring', stiffness: 400, damping: 17 } }
    },
    glow: {
      hover: { boxShadow: '0 0 40px rgba(139, 92, 246, 0.6)', transition: { duration: 0.3 } }
    },
    scale: {
      hover: { scale: 1.05, transition: { type: 'spring', stiffness: 400, damping: 17 } }
    },
    tilt: {
      hover: { rotateY: 5, rotateX: 5, transition: { type: 'spring', stiffness: 400, damping: 17 } }
    },
    none: {}
  };

  return (
    <motion.div
      className={cn(baseClasses, variantClasses[variant], className)}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration, delay, ease: 'easeOut' }}
      variants={hoverVariants[hover]}
      whileHover="hover"
      style={{ perspective: '1000px' }}
    >
      {children}
    </motion.div>
  );
}

// Flip Card Component
interface FlipCardProps {
  front: React.ReactNode;
  back: React.ReactNode;
  className?: string;
  trigger?: 'hover' | 'click';
}

export function FlipCard({ front, back, className, trigger = 'hover' }: FlipCardProps) {
  const [isFlipped, setIsFlipped] = React.useState(false);

  const handleInteraction = () => {
    if (trigger === 'click') {
      setIsFlipped(!isFlipped);
    }
  };

  const hoverProps = trigger === 'hover' ? {
    onMouseEnter: () => setIsFlipped(true),
    onMouseLeave: () => setIsFlipped(false)
  } : {};

  return (
    <div 
      className={cn('perspective-1000 w-full h-full cursor-pointer', className)}
      onClick={handleInteraction}
      {...hoverProps}
    >
      <motion.div
        className="preserve-3d w-full h-full relative"
        animate={{ rotateY: isFlipped ? 180 : 0 }}
        transition={{ duration: 0.6, ease: 'easeInOut' }}
      >
        {/* Front */}
        <div className="backface-hidden absolute inset-0 w-full h-full">
          {front}
        </div>
        
        {/* Back */}
        <div className="backface-hidden absolute inset-0 w-full h-full rotate-y-180">
          {back}
        </div>
      </motion.div>
    </div>
  );
}

// Staggered Cards Container
interface StaggeredCardsProps {
  children: React.ReactNode[];
  className?: string;
  staggerDelay?: number;
  direction?: 'up' | 'down' | 'left' | 'right';
}

export function StaggeredCards({
  children,
  className,
  staggerDelay = 0.1,
  direction = 'up'
}: StaggeredCardsProps) {
  const directionVariants = {
    up: { y: 20 },
    down: { y: -20 },
    left: { x: -20 },
    right: { x: 20 }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: staggerDelay,
        delayChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { 
      opacity: 0, 
      ...directionVariants[direction]
    },
    visible: {
      opacity: 1,
      x: 0,
      y: 0,
      transition: {
        type: 'spring',
        stiffness: 100,
        damping: 12
      }
    }
  };

  return (
    <motion.div
      className={cn('grid gap-6', className)}
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {children.map((child, index) => (
        <motion.div key={index} variants={itemVariants}>
          {child}
        </motion.div>
      ))}
    </motion.div>
  );
}

// Parallax Card
interface ParallaxCardProps {
  children: React.ReactNode;
  className?: string;
  intensity?: number;
}

export function ParallaxCard({ children, className, intensity = 0.5 }: ParallaxCardProps) {
  const [mousePosition, setMousePosition] = React.useState({ x: 0, y: 0 });
  const [isHovered, setIsHovered] = React.useState(false);

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    const rect = e.currentTarget.getBoundingClientRect();
    const x = (e.clientX - rect.left - rect.width / 2) * intensity;
    const y = (e.clientY - rect.top - rect.height / 2) * intensity;
    setMousePosition({ x, y });
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
    setMousePosition({ x: 0, y: 0 });
  };

  return (
    <motion.div
      className={cn('relative', className)}
      onMouseMove={handleMouseMove}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={handleMouseLeave}
      animate={{
        rotateX: mousePosition.y * 0.1,
        rotateY: mousePosition.x * 0.1,
      }}
      transition={{ type: 'spring', stiffness: 300, damping: 30 }}
      style={{ perspective: '1000px' }}
    >
      <motion.div
        animate={{
          x: mousePosition.x * 0.1,
          y: mousePosition.y * 0.1,
        }}
        transition={{ type: 'spring', stiffness: 300, damping: 30 }}
      >
        {children}
      </motion.div>
    </motion.div>
  );
}
