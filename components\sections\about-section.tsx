'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { 
  Users, 
  Trophy, 
  BookOpen, 
  Clock, 
  Target, 
  Award,
  TrendingUp,
  Heart,
  Zap
} from 'lucide-react';
import { useIntersectionObserver } from '@/hooks/use-intersection-observer';
import { useStaggeredCounters } from '@/hooks/use-counter-animation';
import { AnimatedCard, StaggeredCards } from '@/components/ui/animated-card';
import { cn } from '@/lib/utils';

export function AboutSection() {
  const { elementRef, isVisible } = useIntersectionObserver({
    threshold: 0.3,
    freezeOnceVisible: true,
  });

  const mainStats = [
    { end: 500, suffix: 'K+', label: 'Active Students', icon: Users, color: 'violet' },
    { end: 98, suffix: '%', label: 'Satisfaction Rate', icon: Heart, color: 'pink' },
    { end: 99, prefix: '₹', label: 'Starting Price', icon: BookOpen, color: 'blue' },
  ];

  const additionalStats = [
    { end: 50, suffix: '+', label: 'Expert Teachers', icon: Award },
    { end: 1000, suffix: '+', label: 'Hours of Content', icon: Clock },
    { end: 95, suffix: '%', label: 'Success Rate', icon: Trophy },
    { end: 24, suffix: '/7', label: 'Support Available', icon: Zap },
    { end: 15, suffix: '+', label: 'Exam Categories', icon: Target },
  ];

  const animatedMainStats = useStaggeredCounters(
    mainStats.map(stat => ({
      end: stat.end,
      prefix: stat.prefix,
      suffix: stat.suffix,
      duration: 2500,
    })),
    isVisible,
    300
  );

  const features = [
    {
      title: 'AI-Powered Learning',
      description: 'Personalized study plans adapted to your learning style and pace',
      icon: Zap,
      gradient: 'from-violet-500 to-purple-600'
    },
    {
      title: 'Expert Faculty',
      description: 'Learn from India\'s top educators with proven track records',
      icon: Award,
      gradient: 'from-blue-500 to-cyan-600'
    },
    {
      title: 'Live Interactive Classes',
      description: 'Real-time doubt solving and interactive learning sessions',
      icon: Users,
      gradient: 'from-pink-500 to-rose-600'
    },
    {
      title: 'Progress Tracking',
      description: 'Detailed analytics to monitor your preparation journey',
      icon: TrendingUp,
      gradient: 'from-green-500 to-emerald-600'
    }
  ];

  return (
    <section className="py-20 bg-gradient-to-b from-white to-violet-50 dark:from-gray-900 dark:to-gray-800 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 opacity-30">
        <div className="absolute top-20 left-10 w-72 h-72 bg-violet-300 rounded-full mix-blend-multiply filter blur-xl animate-float" />
        <div className="absolute top-40 right-10 w-72 h-72 bg-cyan-300 rounded-full mix-blend-multiply filter blur-xl animate-float-delayed" />
        <div className="absolute bottom-20 left-1/2 w-72 h-72 bg-pink-300 rounded-full mix-blend-multiply filter blur-xl animate-float" />
      </div>

      <div ref={elementRef} className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isVisible ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, ease: 'easeOut' }}
          className="text-center mb-16"
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={isVisible ? { opacity: 1, scale: 1 } : {}}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="inline-flex items-center gap-2 px-4 py-2 rounded-full glass text-sm font-medium text-violet-700 dark:text-violet-300 mb-6"
          >
            <Trophy className="w-4 h-4 text-yellow-500" />
            Trusted by Millions
          </motion.div>

          <h2 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 dark:text-white mb-6">
            Why Students{' '}
            <span className="text-gradient bg-gradient-primary">Choose Us</span>
          </h2>
          
          <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
            We've revolutionized online education with cutting-edge technology, 
            expert guidance, and a proven track record of success.
          </p>
        </motion.div>

        {/* Main Stats */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isVisible ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.3 }}
          className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-20"
        >
          {mainStats.map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={isVisible ? { opacity: 1, scale: 1 } : {}}
              transition={{ duration: 0.6, delay: 0.4 + index * 0.1 }}
              className="relative"
            >
              <AnimatedCard
                variant="glass"
                hover="lift"
                className="text-center p-8 h-full"
              >
                <div className={cn(
                  'w-16 h-16 mx-auto mb-6 rounded-2xl flex items-center justify-center',
                  stat.color === 'violet' && 'bg-gradient-to-br from-violet-500 to-purple-600',
                  stat.color === 'pink' && 'bg-gradient-to-br from-pink-500 to-rose-600',
                  stat.color === 'blue' && 'bg-gradient-to-br from-blue-500 to-cyan-600'
                )}>
                  <stat.icon className="w-8 h-8 text-white" />
                </div>
                
                <div className="text-4xl sm:text-5xl font-bold text-gray-900 dark:text-white mb-2">
                  {animatedMainStats[index]}
                </div>
                
                <div className="text-lg text-gray-600 dark:text-gray-400 font-medium">
                  {stat.label}
                </div>
              </AnimatedCard>
            </motion.div>
          ))}
        </motion.div>

        {/* Features Grid */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isVisible ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="mb-20"
        >
          <StaggeredCards
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
            staggerDelay={0.1}
          >
            {features.map((feature, index) => (
              <AnimatedCard
                key={feature.title}
                variant="glass"
                hover="lift"
                className="p-6 text-center group"
              >
                <div className={cn(
                  'w-12 h-12 mx-auto mb-4 rounded-xl bg-gradient-to-br flex items-center justify-center group-hover:scale-110 transition-transform duration-300',
                  feature.gradient
                )}>
                  <feature.icon className="w-6 h-6 text-white" />
                </div>
                
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  {feature.title}
                </h3>
                
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {feature.description}
                </p>
              </AnimatedCard>
            ))}
          </StaggeredCards>
        </motion.div>

        {/* Additional Stats */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isVisible ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.8 }}
          className="glass rounded-3xl p-8"
        >
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-5 gap-6">
            {additionalStats.map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, y: 20 }}
                animate={isVisible ? { opacity: 1, y: 0 } : {}}
                transition={{ duration: 0.6, delay: 1 + index * 0.1 }}
                className="text-center group"
              >
                <div className="w-10 h-10 mx-auto mb-3 rounded-lg bg-gradient-primary flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                  <stat.icon className="w-5 h-5 text-white" />
                </div>
                
                <div className="text-2xl font-bold text-gray-900 dark:text-white mb-1">
                  {stat.end}{stat.suffix}
                </div>
                
                <div className="text-xs text-gray-600 dark:text-gray-400">
                  {stat.label}
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
}
