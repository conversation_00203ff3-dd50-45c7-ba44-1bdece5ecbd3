import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'

// GET /api/student/quizzes/[id]/analytics - Get detailed analytics for a specific quiz
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT'
  },
  async (request: NextRequest, { user, params }) => {
    const resolvedParams = await params
    const quizId = resolvedParams?.id as string

    if (!quizId) {
      return APIResponse.error('Quiz ID is required', 400)
    }

    // Check if quiz exists and is published
    const quiz = await prisma.quiz.findFirst({
      where: {
        id: quizId,
        isPublished: true
      },
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            image: true
          }
        },
        subject: {
          select: {
            id: true,
            name: true
          }
        },
        chapter: {
          select: {
            id: true,
            name: true
          }
        },
        topic: {
          select: {
            id: true,
            name: true
          }
        },
        _count: {
          select: {
            questions: true,
            attempts: true,
            reviews: true
          }
        }
      }
    })

    if (!quiz) {
      return APIResponse.error('Quiz not found', 404)
    }

    // Get user's attempts for this quiz
    const userAttempts = await prisma.quizAttempt.findMany({
      where: {
        quizId,
        userId: user.id,
        isCompleted: true
      },
      orderBy: {
        completedAt: 'desc'
      }
    })

    // Get overall quiz statistics
    const overallStats = await prisma.quizAttempt.aggregate({
      where: {
        quizId,
        isCompleted: true
      },
      _count: {
        id: true
      },
      _avg: {
        percentage: true,
        timeSpent: true
      },
      _max: {
        percentage: true
      },
      _min: {
        percentage: true
      }
    })

    // Get unique users count
    const uniqueUsersCount = await prisma.quizAttempt.groupBy({
      by: ['userId'],
      where: {
        quizId,
        isCompleted: true
      }
    })

    // Get score distribution using a subquery approach
    const scoreDistribution = await prisma.$queryRaw`
      SELECT
        score_range,
        COUNT(*) as count
      FROM (
        SELECT
          CASE
            WHEN percentage >= 90 THEN '90-100%'
            WHEN percentage >= 80 THEN '80-89%'
            WHEN percentage >= 70 THEN '70-79%'
            WHEN percentage >= 60 THEN '60-69%'
            WHEN percentage >= 50 THEN '50-59%'
            ELSE 'Below 50%'
          END as score_range
        FROM quiz_attempts
        WHERE quiz_id = ${quizId} AND is_completed = true
      ) as score_ranges
      GROUP BY score_range
      ORDER BY
        CASE score_range
          WHEN '90-100%' THEN 1
          WHEN '80-89%' THEN 2
          WHEN '70-79%' THEN 3
          WHEN '60-69%' THEN 4
          WHEN '50-59%' THEN 5
          ELSE 6
        END
    ` as Array<{ score_range: string; count: bigint }>

    // Get top performers (excluding current user for privacy)
    const topPerformers = await prisma.quizAttempt.findMany({
      where: {
        quizId,
        isCompleted: true,
        userId: {
          not: user.id
        }
      },
      include: {
        user: {
          select: {
            name: true,
            image: true
          }
        }
      },
      orderBy: [
        { percentage: 'desc' },
        { timeSpent: 'asc' }
      ],
      take: 10
    })

    // Get review statistics
    const reviewStats = await prisma.quizReview.aggregate({
      where: {
        quizId,
        isPublic: true
      },
      _avg: {
        rating: true
      },
      _count: {
        rating: true
      }
    })

    // Calculate user's performance insights
    const userBestScore = userAttempts.length > 0 ? Math.max(...userAttempts.map(a => a.percentage)) : 0
    const userAverageScore = userAttempts.length > 0 
      ? userAttempts.reduce((sum, a) => sum + a.percentage, 0) / userAttempts.length 
      : 0
    const userTotalTime = userAttempts.reduce((sum, a) => sum + (a.timeSpent || 0), 0)

    // Generate insights
    const insights = []
    
    if (userBestScore > (overallStats._avg.percentage || 0)) {
      insights.push({
        type: 'performance',
        title: 'Above Average Performance',
        value: `${userBestScore.toFixed(1)}%`,
        trend: 'positive' as const,
        description: `Your best score is ${(userBestScore - (overallStats._avg.percentage || 0)).toFixed(1)}% above the quiz average`
      })
    }

    if (userAttempts.length > 1) {
      const improvement = userAttempts[0].percentage - userAttempts[userAttempts.length - 1].percentage
      if (improvement > 0) {
        insights.push({
          type: 'improvement',
          title: 'Score Improvement',
          value: `+${improvement.toFixed(1)}%`,
          trend: 'positive' as const,
          description: `You've improved by ${improvement.toFixed(1)}% since your first attempt`
        })
      }
    }

    const completionRate = (overallStats._count.id / uniqueUsersCount.length) * 100
    insights.push({
      type: 'completion',
      title: 'Quiz Completion Rate',
      value: `${completionRate.toFixed(1)}%`,
      trend: completionRate > 80 ? 'positive' : completionRate > 60 ? 'neutral' : 'negative',
      description: `${completionRate.toFixed(1)}% of students who started this quiz completed it`
    })

    return APIResponse.success({
      quiz: {
        id: quiz.id,
        title: quiz.title,
        description: quiz.description,
        type: quiz.type,
        difficulty: quiz.difficulty,
        questionCount: quiz._count.questions,
        creator: quiz.creator,
        subject: quiz.subject,
        chapter: quiz.chapter,
        topic: quiz.topic
      },
      userPerformance: {
        totalAttempts: userAttempts.length,
        bestScore: userBestScore,
        averageScore: Math.round(userAverageScore * 10) / 10,
        totalTimeSpent: userTotalTime,
        attempts: userAttempts.map(attempt => ({
          id: attempt.id,
          score: attempt.percentage,
          timeSpent: attempt.timeSpent,
          completedAt: attempt.completedAt
        }))
      },
      overview: {
        totalAttempts: overallStats._count.id,
        uniqueUsers: uniqueUsersCount.length,
        averageScore: Math.round((overallStats._avg.percentage || 0) * 10) / 10,
        averageTime: Math.round((overallStats._avg.timeSpent || 0) / 60), // Convert to minutes
        completionRate: Math.round(completionRate * 10) / 10,
        highestScore: overallStats._max.percentage || 0,
        lowestScore: overallStats._min.percentage || 0
      },
      scoreDistribution: scoreDistribution.map(item => ({
        range: item.score_range,
        count: Number(item.count)
      })),
      topPerformers: topPerformers.map(performer => ({
        user: {
          name: performer.user.name,
          image: performer.user.image
        },
        score: performer.percentage,
        timeSpent: performer.timeSpent,
        completedAt: performer.completedAt
      })),
      reviews: {
        averageRating: Math.round((reviewStats._avg.rating || 0) * 10) / 10,
        totalReviews: reviewStats._count.rating || 0
      },
      insights
    })
  }
)
