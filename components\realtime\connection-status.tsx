"use client"

import { useState, useEffect } from "react"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { 
  Wifi, 
  WifiOff, 
  <PERSON><PERSON>resh<PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>gle,
  CheckCircle,
  Clock,
  Users
} from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import { getSocketClient } from "@/lib/socket-client"
import { toast } from "sonner"

interface ConnectionStatusProps {
  showDetails?: boolean
  className?: string
}

interface ConnectionStats {
  connectedAt?: Date
  reconnectAttempts: number
  lastPing?: number
  connectedUsers: number
}

export function ConnectionStatus({ 
  showDetails = false, 
  className = "" 
}: ConnectionStatusProps) {
  const [isConnected, setIsConnected] = useState(false)
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected' | 'error'>('connecting')
  const [stats, setStats] = useState<ConnectionStats>({
    reconnectAttempts: 0,
    connectedUsers: 0
  })
  const [showReconnectButton, setShowReconnectButton] = useState(false)

  useEffect(() => {
    const socketClient = getSocketClient()

    // Connection events
    const handleConnectionEstablished = () => {
      setIsConnected(true)
      setConnectionStatus('connected')
      setStats(prev => ({
        ...prev,
        connectedAt: new Date(),
        reconnectAttempts: 0
      }))
      setShowReconnectButton(false)
      // Request initial metrics when connection is established
      socketClient.requestMetrics()
    }

    const handleConnectionSuccess = (data: { connectedUsers: number }) => {
      setStats(prev => ({
        ...prev,
        connectedUsers: data.connectedUsers
      }))
    }

    const handleConnectionLost = (reason: string) => {
      setIsConnected(false)
      setConnectionStatus('disconnected')
      setShowReconnectButton(true)
      console.log('Connection lost:', reason)
    }

    const handleConnectionError = (error: any) => {
      setIsConnected(false)
      setConnectionStatus('error')
      setStats(prev => ({
        ...prev,
        reconnectAttempts: prev.reconnectAttempts + 1
      }))
      setShowReconnectButton(true)
      console.error('Connection error:', error)
    }

    const handleConnectionFailed = () => {
      setIsConnected(false)
      setConnectionStatus('error')
      setShowReconnectButton(true)
      toast.error('Failed to connect to real-time services')
    }

    // User presence events
    const handleUserOnline = () => {
      setStats(prev => ({
        ...prev,
        connectedUsers: prev.connectedUsers + 1
      }))
    }

    const handleUserOffline = () => {
      setStats(prev => ({
        ...prev,
        connectedUsers: Math.max(0, prev.connectedUsers - 1)
      }))
    }

    const handleMetricsUpdate = (metrics: { connectedUsers: number; activeRooms: number; timestamp: Date }) => {
      setStats(prev => ({
        ...prev,
        connectedUsers: metrics.connectedUsers
      }))
    }

    // Set up event listeners
    socketClient.on('connection:established', handleConnectionEstablished)
    socketClient.on('connection:success', handleConnectionSuccess)
    socketClient.on('connection:lost', handleConnectionLost)
    socketClient.on('connection:error', handleConnectionError)
    socketClient.on('connection:failed', handleConnectionFailed)
    socketClient.on('user:online', handleUserOnline)
    socketClient.on('user:offline', handleUserOffline)
    socketClient.on('metrics_update', handleMetricsUpdate)

    // Initial connection status
    setIsConnected(socketClient.isConnected())
    setConnectionStatus(socketClient.isConnected() ? 'connected' : 'connecting')

    // Request initial metrics if connected
    if (socketClient.isConnected()) {
      socketClient.requestMetrics()
    }

    // Set up periodic metrics updates (every 30 seconds)
    const metricsInterval = setInterval(() => {
      if (socketClient.isConnected()) {
        socketClient.requestMetrics()
      }
    }, 30000)

    // Cleanup
    return () => {
      clearInterval(metricsInterval)
      socketClient.off('connection:established', handleConnectionEstablished)
      socketClient.off('connection:success', handleConnectionSuccess)
      socketClient.off('connection:lost', handleConnectionLost)
      socketClient.off('connection:error', handleConnectionError)
      socketClient.off('connection:failed', handleConnectionFailed)
      socketClient.off('user:online', handleUserOnline)
      socketClient.off('user:offline', handleUserOffline)
      socketClient.off('metrics_update', handleMetricsUpdate)
    }
  }, [])

  const getStatusIcon = () => {
    switch (connectionStatus) {
      case 'connected':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'connecting':
        return <RefreshCw className="h-4 w-4 text-blue-600 animate-spin" />
      case 'disconnected':
        return <WifiOff className="h-4 w-4 text-yellow-600" />
      case 'error':
        return <AlertTriangle className="h-4 w-4 text-red-600" />
      default:
        return <Wifi className="h-4 w-4 text-gray-600" />
    }
  }

  const getStatusColor = () => {
    switch (connectionStatus) {
      case 'connected':
        return 'bg-green-500 border-green-600 text-green-50'
      case 'connecting':
        return 'bg-blue-500 border-blue-600 text-blue-50'
      case 'disconnected':
        return 'bg-yellow-500 border-yellow-600 text-yellow-50'
      case 'error':
        return 'bg-red-500 border-red-600 text-red-50'
      default:
        return 'bg-gray-500 border-gray-600 text-gray-50'
    }
  }

  const getStatusText = () => {
    switch (connectionStatus) {
      case 'connected':
        return 'Connected'
      case 'connecting':
        return 'Connecting...'
      case 'disconnected':
        return 'Disconnected'
      case 'error':
        return 'Connection Error'
      default:
        return 'Unknown'
    }
  }

  const handleReconnect = () => {
    const socketClient = getSocketClient()
    setConnectionStatus('connecting')
    setShowReconnectButton(false)
    
    // Disconnect and reconnect
    socketClient.disconnect()
    
    // Reinitialize connection
    setTimeout(() => {
      window.location.reload() // Simple reconnection by reloading
    }, 1000)
  }

  const formatUptime = () => {
    if (!stats.connectedAt) return 'N/A'
    
    const now = new Date()
    const diff = now.getTime() - stats.connectedAt.getTime()
    const minutes = Math.floor(diff / 60000)
    const hours = Math.floor(minutes / 60)
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`
    }
    return `${minutes}m`
  }

  if (!showDetails) {
    // Simple status indicator
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <motion.div
          animate={{ scale: isConnected ? [1, 1.1, 1] : 1 }}
          transition={{ duration: 2, repeat: isConnected ? Infinity : 0 }}
        >
          {getStatusIcon()}
        </motion.div>
        <Badge variant="outline" className={getStatusColor()}>
          {getStatusText()}
        </Badge>
      </div>
    )
  }

  // Detailed status card
  return (
    <div className={className}>
      <Card>
        <CardContent className="pt-6">
          <div className="space-y-4">
            {/* Status Header */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <motion.div
                  animate={{ scale: isConnected ? [1, 1.1, 1] : 1 }}
                  transition={{ duration: 2, repeat: isConnected ? Infinity : 0 }}
                >
                  {getStatusIcon()}
                </motion.div>
                <div>
                  <h3 className="font-semibold">Real-time Connection</h3>
                  <p className="text-sm text-muted-foreground">{getStatusText()}</p>
                </div>
              </div>
              
              <AnimatePresence>
                {showReconnectButton && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.8 }}
                  >
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleReconnect}
                      disabled={connectionStatus === 'connecting'}
                    >
                      <RefreshCw className={`h-4 w-4 mr-2 ${
                        connectionStatus === 'connecting' ? 'animate-spin' : ''
                      }`} />
                      Reconnect
                    </Button>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Connection Stats */}
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-3 bg-muted/50 rounded-lg">
                <div className="flex items-center justify-center gap-2 mb-1">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Uptime</span>
                </div>
                <div className="text-lg font-bold">
                  {formatUptime()}
                </div>
              </div>
              
              <div className="text-center p-3 bg-muted/50 rounded-lg">
                <div className="flex items-center justify-center gap-2 mb-1">
                  <Users className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Online</span>
                </div>
                <div className="text-lg font-bold">
                  {stats.connectedUsers}
                </div>
              </div>
            </div>

            {/* Error Information */}
            {connectionStatus === 'error' && stats.reconnectAttempts > 0 && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg"
              >
                <div className="flex items-center gap-2 text-red-800 dark:text-red-200">
                  <AlertTriangle className="h-4 w-4" />
                  <span className="text-sm font-medium">Connection Issues</span>
                </div>
                <p className="text-sm text-red-700 dark:text-red-300 mt-1">
                  Failed to connect after {stats.reconnectAttempts} attempt{stats.reconnectAttempts !== 1 ? 's' : ''}. 
                  Some real-time features may not work properly.
                </p>
              </motion.div>
            )}

            {/* Features Status */}
            <div className="space-y-2">
              <h4 className="text-sm font-medium">Real-time Features</h4>
              <div className="grid grid-cols-2 gap-2 text-xs">
                <div className="flex items-center gap-2">
                  <div className={`w-2 h-2 rounded-full ${
                    isConnected ? 'bg-green-500' : 'bg-gray-400'
                  }`} />
                  <span>Notifications</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className={`w-2 h-2 rounded-full ${
                    isConnected ? 'bg-green-500' : 'bg-gray-400'
                  }`} />
                  <span>Live Quiz</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className={`w-2 h-2 rounded-full ${
                    isConnected ? 'bg-green-500' : 'bg-gray-400'
                  }`} />
                  <span>Chat</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className={`w-2 h-2 rounded-full ${
                    isConnected ? 'bg-green-500' : 'bg-gray-400'
                  }`} />
                  <span>Collaboration</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
