"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Upload, 
  FileSpreadsheet, 
  Download, 
  CheckCircle,
  AlertCircle,
  X,
  Info
} from "lucide-react"
import { motion } from "framer-motion"
import { toast } from "sonner"

interface BulkUploadProps {
  onQuestionsImported: (questions: any[]) => void
  onClose: () => void
}

export function BulkUpload({ onQuestionsImported, onClose }: BulkUploadProps) {
  const [file, setFile] = useState<File | null>(null)
  const [isProcessing, setIsProcessing] = useState(false)
  const [previewData, setPreviewData] = useState<any[]>([])
  const [errors, setErrors] = useState<string[]>([])

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0]
    if (!selectedFile) return

    // Validate file type
    const validTypes = [
      'text/csv',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ]

    if (!validTypes.includes(selectedFile.type)) {
      toast.error("Please upload a CSV or Excel file")
      return
    }

    if (selectedFile.size > 5 * 1024 * 1024) { // 5MB limit
      toast.error("File size must be less than 5MB")
      return
    }

    setFile(selectedFile)
    setErrors([])
    setPreviewData([])
  }

  const processFile = async () => {
    if (!file) return

    setIsProcessing(true)
    setErrors([])

    try {
      // For demo purposes, we'll simulate processing
      // In a real implementation, you'd parse CSV/Excel here
      setTimeout(() => {
        const sampleData = [
          {
            question: "What is the capital of France?",
            type: "MCQ",
            optionA: "London",
            optionB: "Berlin", 
            optionC: "Paris",
            optionD: "Madrid",
            correctAnswer: "C",
            explanation: "Paris is the capital of France",
            points: 1
          },
          {
            question: "JavaScript is a compiled language.",
            type: "TRUE_FALSE",
            optionA: "True",
            optionB: "False",
            correctAnswer: "B",
            explanation: "JavaScript is an interpreted language",
            points: 1
          }
        ]

        setPreviewData(sampleData)
        setIsProcessing(false)
      }, 2000)
    } catch (error) {
      setErrors(["Failed to process file. Please check the format."])
      setIsProcessing(false)
    }
  }

  const importQuestions = () => {
    const formattedQuestions = previewData.map((item, index) => ({
      id: Date.now().toString() + index,
      type: item.type === 'TRUE_FALSE' ? 'TRUE_FALSE' : 'MCQ',
      text: item.question,
      options: item.type === 'TRUE_FALSE' 
        ? ['True', 'False']
        : [item.optionA, item.optionB, item.optionC, item.optionD].filter(Boolean),
      correctAnswer: item.type === 'TRUE_FALSE'
        ? (item.correctAnswer === 'A' ? 'True' : 'False')
        : item.correctAnswer === 'A' ? item.optionA
        : item.correctAnswer === 'B' ? item.optionB
        : item.correctAnswer === 'C' ? item.optionC
        : item.optionD,
      explanation: item.explanation || '',
      points: item.points || 1
    }))

    onQuestionsImported(formattedQuestions)
    toast.success(`Imported ${formattedQuestions.length} questions successfully!`)
  }

  const downloadTemplate = () => {
    const csvContent = `question,type,optionA,optionB,optionC,optionD,correctAnswer,explanation,points
"What is the capital of France?",MCQ,London,Berlin,Paris,Madrid,C,"Paris is the capital of France",1
"JavaScript is a compiled language.",TRUE_FALSE,True,False,,,B,"JavaScript is an interpreted language",1
"What does HTML stand for?",MCQ,"Hyper Text Markup Language","High Tech Modern Language","Home Tool Markup Language","Hyperlink and Text Markup Language",A,"HTML stands for Hyper Text Markup Language",1`

    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'quiz_questions_template.csv'
    a.click()
    window.URL.revokeObjectURL(url)
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className="bg-background rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-auto"
      >
        <div className="flex items-center justify-between p-6 border-b">
          <div>
            <h2 className="text-2xl font-bold flex items-center gap-2">
              <Upload className="h-6 w-6 text-primary" />
              Bulk Upload Questions
            </h2>
            <p className="text-muted-foreground">Import questions from CSV or Excel files</p>
          </div>
          <Button variant="outline" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        <div className="p-6 space-y-6">
          {/* Instructions */}
          <Card className="border-blue-200 bg-blue-50 dark:bg-blue-900/20 dark:border-blue-800">
            <CardContent className="pt-6">
              <div className="flex items-start gap-3">
                <Info className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5" />
                <div>
                  <h4 className="font-semibold text-blue-800 dark:text-blue-200 mb-2">
                    Upload Instructions
                  </h4>
                  <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                    <li>• Use CSV or Excel format (.csv, .xls, .xlsx)</li>
                    <li>• Include columns: question, type, optionA, optionB, optionC, optionD, correctAnswer, explanation, points</li>
                    <li>• For True/False questions, use optionA and optionB only</li>
                    <li>• correctAnswer should be A, B, C, or D</li>
                    <li>• Maximum file size: 5MB</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Template Download */}
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div className="flex items-center gap-3">
              <FileSpreadsheet className="h-8 w-8 text-green-600" />
              <div>
                <h4 className="font-semibold">Download Template</h4>
                <p className="text-sm text-muted-foreground">
                  Get a sample CSV file with the correct format
                </p>
              </div>
            </div>
            <Button variant="outline" onClick={downloadTemplate}>
              <Download className="h-4 w-4 mr-2" />
              Download Template
            </Button>
          </div>

          {/* File Upload */}
          <div>
            <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center hover:border-muted-foreground/50 transition-colors">
              <input
                type="file"
                accept=".csv,.xls,.xlsx"
                onChange={handleFileChange}
                className="hidden"
                id="bulk-upload"
              />
              <label htmlFor="bulk-upload" className="cursor-pointer">
                <Upload className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <h3 className="text-lg font-semibold mb-2">
                  {file ? file.name : "Choose file to upload"}
                </h3>
                <p className="text-muted-foreground">
                  Drop your CSV or Excel file here, or click to browse
                </p>
              </label>
            </div>

            {file && (
              <div className="mt-4 flex items-center justify-between p-3 bg-muted rounded-lg">
                <div className="flex items-center gap-2">
                  <FileSpreadsheet className="h-5 w-5" />
                  <span className="font-medium">{file.name}</span>
                  <Badge variant="outline">
                    {(file.size / 1024).toFixed(1)} KB
                  </Badge>
                </div>
                <Button onClick={processFile} disabled={isProcessing}>
                  {isProcessing ? "Processing..." : "Process File"}
                </Button>
              </div>
            )}
          </div>

          {/* Processing State */}
          {isProcessing && (
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mr-3"></div>
                  <span>Processing file and validating questions...</span>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Errors */}
          {errors.length > 0 && (
            <Card className="border-destructive">
              <CardContent className="pt-6">
                <div className="flex items-start gap-3">
                  <AlertCircle className="h-5 w-5 text-destructive mt-0.5" />
                  <div>
                    <h4 className="font-semibold text-destructive mb-2">
                      Processing Errors
                    </h4>
                    <ul className="text-sm space-y-1">
                      {errors.map((error, index) => (
                        <li key={index} className="text-destructive">• {error}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Preview */}
          {previewData.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  Preview Questions ({previewData.length})
                </CardTitle>
                <CardDescription>
                  Review the questions before importing
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4 max-h-60 overflow-y-auto">
                  {previewData.slice(0, 3).map((item, index) => (
                    <div key={index} className="p-3 border rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <Badge variant="outline">{item.type}</Badge>
                        <span className="text-sm text-muted-foreground">
                          {item.points} point{item.points !== 1 ? 's' : ''}
                        </span>
                      </div>
                      <p className="font-medium mb-2">{item.question}</p>
                      {item.type === 'MCQ' && (
                        <div className="text-sm space-y-1">
                          <div>A. {item.optionA}</div>
                          <div>B. {item.optionB}</div>
                          <div>C. {item.optionC}</div>
                          <div>D. {item.optionD}</div>
                          <div className="text-green-600 font-medium">
                            Correct: {item.correctAnswer}
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                  {previewData.length > 3 && (
                    <p className="text-center text-muted-foreground">
                      ... and {previewData.length - 3} more questions
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Actions */}
        <div className="flex justify-between items-center p-6 border-t bg-muted/50">
          <div className="text-sm text-muted-foreground">
            {previewData.length > 0 && (
              <span>{previewData.length} questions ready to import</span>
            )}
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            {previewData.length > 0 && (
              <Button onClick={importQuestions}>
                <Upload className="h-4 w-4 mr-2" />
                Import Questions
              </Button>
            )}
          </div>
        </div>
      </motion.div>
    </div>
  )
}
