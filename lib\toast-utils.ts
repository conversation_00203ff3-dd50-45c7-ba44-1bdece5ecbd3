import { toast as sonnerToast } from "sonner"

// Track recent toasts to prevent duplicates
const recentToasts = new Set<string>()

/**
 * Safely convert any value to a string for toast display
 */
function safeToastMessage(message: any): string {
  if (message instanceof Error) {
    return message.message || 'An error occurred'
  }
  if (typeof message === 'string') {
    return message
  }
  if (message && typeof message === 'object') {
    if (typeof message.message === 'string') {
      return message.message
    }
    if (typeof message.error === 'string') {
      return message.error
    }
    if (typeof message.msg === 'string') {
      return message.msg
    }
    if (message.error && typeof message.error === 'object' && message.error.message) {
      return String(message.error.message)
    }
    return 'An error occurred'
  }
  const stringified = String(message)
  return stringified === '[object Object]' ? 'An error occurred' : stringified
}

function dedupKey(type: string, message: any, description?: any) {
  return `${type}-${safeToastMessage(message)}-${description ? safeToastMessage(description) : ''}`
}

function withDedup(type: string, fn: (msg: any, opts?: any) => any) {
  return (message: any, options?: any) => {
    const { description, duration = 4000, deduplicationTime = 3000 } = options || {}
    const key = dedupKey(type, message, description)
    if (recentToasts.has(key)) return
    recentToasts.add(key)
    setTimeout(() => recentToasts.delete(key), deduplicationTime)
    const safeMessage = safeToastMessage(message)
    const safeOptions = options ? {
      ...options,
      description: description ? safeToastMessage(description) : undefined,
      duration
    } : { duration }
    return fn(safeMessage, safeOptions)
  }
}

export const toast = {
  success: withDedup('success', sonnerToast.success),
  error: withDedup('error', sonnerToast.error),
  warning: withDedup('warning', sonnerToast.warning),
  info: withDedup('info', sonnerToast.info),
  default: withDedup('default', sonnerToast),
  loading: withDedup('loading', sonnerToast.loading),
  dismiss: (toastId?: string | number) => sonnerToast.dismiss(toastId),
  promise: <T>(promise: Promise<T>, options: { loading: any; success: any; error: any }) => {
    const safeOptions = {
      loading: safeToastMessage(options.loading),
      success: safeToastMessage(options.success),
      error: safeToastMessage(options.error)
    }
    return sonnerToast.promise(promise, safeOptions)
  }
}

export default toast

/**
 * Clear all recent toast tracking
 * Useful for testing or when you want to reset the deduplication
 */
export function clearToastHistory() {
  recentToasts.clear()
}
