"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { X, Plus, Sparkles, Loader2, Hash, TrendingUp } from "lucide-react"
import { toast } from "@/lib/toast-utils"

interface EnhancedTagInputProps {
  tags: string[]
  onTagsChange: (tags: string[]) => void
  quizContent?: string
  placeholder?: string
  maxTags?: number
  autoGenerateFromContent?: boolean
}

interface TagSuggestion {
  tag: string
  category: 'subject' | 'topic' | 'difficulty' | 'skill' | 'general'
  confidence: number
}

interface TagSuggestions {
  suggestions: TagSuggestion[]
}

export function EnhancedTagInput({ 
  tags, 
  onTagsChange, 
  quizContent = '',
  placeholder = "Add a tag",
  maxTags = 15,
  autoGenerateFromContent = true
}: EnhancedTagInputProps) {
  const [newTag, setNewTag] = useState("")
  const [suggestions, setSuggestions] = useState<TagSuggestion[]>([])
  const [popularTags, setPopularTags] = useState<string[]>([])
  const [loadingSuggestions, setLoadingSuggestions] = useState(false)
  const [loadingPopular, setLoadingPopular] = useState(false)

  // Auto-generate tags when content changes
  useEffect(() => {
    if (autoGenerateFromContent && quizContent.trim() && tags.length === 0) {
      generateAISuggestions()
    }
  }, [quizContent, autoGenerateFromContent])

  // Load popular tags on mount
  useEffect(() => {
    loadPopularTags()
  }, [])

  const addTag = (tag: string = newTag) => {
    const trimmedTag = tag.trim().toLowerCase()
    if (trimmedTag && !tags.map(t => t.toLowerCase()).includes(trimmedTag) && tags.length < maxTags) {
      onTagsChange([...tags, trimmedTag])
      setNewTag("")
    }
  }

  const removeTag = (tagToRemove: string) => {
    onTagsChange(tags.filter(tag => tag !== tagToRemove))
  }

  const loadPopularTags = async () => {
    try {
      setLoadingPopular(true)
      const response = await fetch('/api/admin/quizzes/popular-tags')
      if (response.ok) {
        const data = await response.json()
        setPopularTags(data.data.tags || [])
      }
    } catch (error) {
      console.error('Error loading popular tags:', error)
    } finally {
      setLoadingPopular(false)
    }
  }

  const generateAISuggestions = async () => {
    if (!quizContent.trim()) {
      toast.error('Please add some quiz content first to generate AI suggestions')
      return
    }

    try {
      setLoadingSuggestions(true)
      const response = await fetch('/api/admin/quizzes/suggest-tags', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          content: quizContent,
          existingTags: tags
        })
      })

      if (response.ok) {
        const data = await response.json()
        setSuggestions(data.data.suggestions || [])
        
        // Auto-add high-confidence suggestions if no tags exist
        if (tags.length === 0 && data.data.suggestions) {
          const highConfidenceTags = data.data.suggestions
            .filter((s: TagSuggestion) => s.confidence > 0.8)
            .slice(0, 5)
            .map((s: TagSuggestion) => s.tag)
          
          if (highConfidenceTags.length > 0) {
            onTagsChange(highConfidenceTags)
            toast.success(`Auto-added ${highConfidenceTags.length} AI-suggested tags`)
          }
        }
        
        toast.success(`Generated ${data.data.suggestions?.length || 0} AI tag suggestions`)
      } else {
        const errorData = await response.json().catch(() => ({}))
        const errorMessage = errorData.error || errorData.message || `Tag suggestion failed with status ${response.status}`
        
        if (response.status === 503) {
          toast.error('OpenAI API key not configured. Please add OPENAI_API_KEY to your environment variables.')
        } else {
          const safeErrorMessage = typeof errorMessage === 'string' ? errorMessage : 'Failed to generate AI tag suggestions'
          toast.error(safeErrorMessage)
        }
        throw new Error(errorMessage)
      }
    } catch (error) {
      console.error('Error generating AI suggestions:', error)
      toast.error('Failed to generate AI tag suggestions')
    } finally {
      setLoadingSuggestions(false)
    }
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'subject': return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'topic': return 'bg-green-100 text-green-800 border-green-200'
      case 'difficulty': return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'skill': return 'bg-purple-100 text-purple-800 border-purple-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getConfidenceIcon = (confidence: number) => {
    if (confidence > 0.8) return '🎯'
    if (confidence > 0.6) return '✨'
    return '💡'
  }

  return (
    <div className="space-y-4">
      <div>
        <Label className="text-sm font-medium">Tags</Label>
        <p className="text-xs text-muted-foreground mb-3">
          Add relevant tags to help categorize and discover your quiz
        </p>
        
        {/* Current Tags */}
        {tags.length > 0 && (
          <div className="flex flex-wrap gap-2 mb-3 p-3 bg-muted/30 rounded-lg">
            {tags.map(tag => (
              <Badge 
                key={tag} 
                variant="secondary" 
                className="cursor-pointer hover:bg-destructive hover:text-destructive-foreground transition-colors"
                onClick={() => removeTag(tag)}
              >
                {tag} <X className="h-3 w-3 ml-1" />
              </Badge>
            ))}
          </div>
        )}

        {/* Add New Tag */}
        <div className="flex gap-2 mb-4">
          <Input
            value={newTag}
            onChange={(e) => setNewTag(e.target.value)}
            placeholder={placeholder}
            onKeyPress={(e) => e.key === 'Enter' && addTag()}
            className="flex-1"
            disabled={tags.length >= maxTags}
          />
          <Button 
            type="button" 
            onClick={() => addTag()} 
            variant="outline"
            disabled={!newTag.trim() || tags.length >= maxTags}
          >
            <Plus className="h-4 w-4" />
          </Button>
          <Button 
            type="button" 
            onClick={generateAISuggestions} 
            variant="outline"
            disabled={loadingSuggestions || !quizContent.trim()}
            className="bg-gradient-to-r from-purple-50 to-blue-50 hover:from-purple-100 hover:to-blue-100"
          >
            {loadingSuggestions ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Sparkles className="h-4 w-4" />
            )}
          </Button>
        </div>

        <div className="text-xs text-muted-foreground mb-4">
          {tags.length}/{maxTags} tags used
        </div>
      </div>

      {/* AI Suggestions */}
      {suggestions.length > 0 && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm flex items-center gap-2">
              <Sparkles className="h-4 w-4 text-purple-600" />
              AI Suggestions
            </CardTitle>
            <CardDescription className="text-xs">
              Click to add suggested tags based on your content
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="flex flex-wrap gap-2">
              {suggestions
                .filter(s => !tags.map(t => t.toLowerCase()).includes(s.tag.toLowerCase()))
                .slice(0, 12)
                .map((suggestion, index) => (
                  <Badge
                    key={index}
                    variant="outline"
                    className={`cursor-pointer hover:bg-primary hover:text-primary-foreground transition-colors ${getCategoryColor(suggestion.category)}`}
                    onClick={() => addTag(suggestion.tag)}
                  >
                    {getConfidenceIcon(suggestion.confidence)} {suggestion.tag}
                  </Badge>
                ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Popular Tags */}
      {popularTags.length > 0 && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm flex items-center gap-2">
              <TrendingUp className="h-4 w-4 text-green-600" />
              Popular Tags
            </CardTitle>
            <CardDescription className="text-xs">
              Commonly used tags in the system
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-0">
            {loadingPopular ? (
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Loader2 className="h-4 w-4 animate-spin" />
                Loading popular tags...
              </div>
            ) : (
              <div className="flex flex-wrap gap-2">
                {popularTags
                  .filter(tag => !tags.map(t => t.toLowerCase()).includes(tag.toLowerCase()))
                  .slice(0, 10)
                  .map((tag, index) => (
                    <Badge
                      key={index}
                      variant="outline"
                      className="cursor-pointer hover:bg-secondary transition-colors"
                      onClick={() => addTag(tag)}
                    >
                      <Hash className="h-3 w-3 mr-1" />
                      {tag}
                    </Badge>
                  ))}
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}
