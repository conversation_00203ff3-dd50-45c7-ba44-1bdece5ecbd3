import { NextRequest } from 'next/server'
import { z } from 'zod'
import { createAPIHandler, APIResponse, commonSchemas } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'

const querySchema = commonSchemas.pagination.extend({
  search: z.string().optional(),
  type: z.enum(['QUIZ', 'TEST_SERIES', 'DAILY_PRACTICE']).optional(),
  difficulty: z.enum(['EASY', 'MEDIUM', 'HARD']).optional(),
  sortBy: z.enum(['createdAt', 'title']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc')
})

// GET /api/student/favorites - Get user's favorite quizzes
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT',
    validateQuery: querySchema
  },
  async (request: NextRequest, { user, validatedQuery }) => {
    const { page, limit, search, type, difficulty, sortBy, sortOrder } = validatedQuery

    // Build where clause
    const whereClause: any = {
      userId: user.id,
      quiz: {
        isPublished: true,
        ...(search && {
          OR: [
            { title: { contains: search, mode: 'insensitive' } },
            { description: { contains: search, mode: 'insensitive' } },
            { tags: { has: search } }
          ]
        }),
        ...(type && { type }),
        ...(difficulty && { difficulty })
      }
    }

    // Get favorites with pagination
    const [favorites, totalCount] = await Promise.all([
      prisma.quizFavorite.findMany({
        where: whereClause,
        include: {
          quiz: {
            include: {
              creator: {
                select: {
                  id: true,
                  name: true,
                  image: true
                }
              },
              subject: {
                select: {
                  id: true,
                  name: true
                }
              },
              chapter: {
                select: {
                  id: true,
                  name: true
                }
              },
              topic: {
                select: {
                  id: true,
                  name: true
                }
              },
              _count: {
                select: {
                  questions: true,
                  attempts: true,
                  reviews: true
                }
              },
              reviews: {
                select: {
                  rating: true
                },
                where: {
                  isPublic: true
                }
              }
            }
          }
        },
        orderBy: sortBy === 'title' ? {
          quiz: { title: sortOrder }
        } : {
          createdAt: sortOrder
        },
        skip: (page - 1) * limit,
        take: limit
      }),
      prisma.quizFavorite.count({
        where: whereClause
      })
    ])

    // Calculate average ratings for each quiz
    const favoritesWithRatings = favorites.map(favorite => {
      const reviews = favorite.quiz.reviews
      const averageRating = reviews.length > 0 
        ? reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length
        : 0

      return {
        ...favorite,
        quiz: {
          ...favorite.quiz,
          averageRating: Math.round(averageRating * 10) / 10,
          reviews: undefined // Remove reviews from response
        }
      }
    })

    return APIResponse.success({
      favorites: favoritesWithRatings,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit)
      }
    })
  }
)
