"use client"

import { useState, useEffect } from "react"
import { useSearchParams } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import {
  ArrowLeft,
  ArrowRight,
  Save,
  Wand2,
  Upload,
  FileText,
  Image,
  Calendar,
  Clock,
  Users,
  Settings,
  Eye,
  Plus,
  X,
  Edit,
  Trash2,
  Brain,
  GraduationCap
} from "lucide-react"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { motion, AnimatePresence } from "framer-motion"
import Link from "next/link"
import { AIQuizGenerator } from "@/components/admin/ai-quiz-generator"
import { AIQuizCreator } from "@/components/admin/ai-quiz-creator"
import { QuestionEditor } from "@/components/admin/question-editor"
import { BulkUpload } from "@/components/admin/bulk-upload"
import { FileUpload } from "@/components/ui/file-upload"
import { CategorySelector } from "@/components/admin/category-selector"
import { EnhancedTagInput } from "@/components/admin/enhanced-tag-input"
import { PrintQuizFormat } from "@/components/admin/print-quiz-format"
import { TestSeriesGenerator } from "@/components/admin/test-series-generator"
import { toast } from "sonner"

type QuizType = 'QUIZ' | 'TEST_SERIES' | 'DAILY_PRACTICE'
type DifficultyLevel = 'EASY' | 'MEDIUM' | 'HARD'
type QuestionType = 'MCQ' | 'TRUE_FALSE' | 'SHORT_ANSWER' | 'MATCHING'

interface QuizData {
  title: string
  description: string
  type: QuizType
  difficulty: DifficultyLevel
  tags: string[]
  timeLimit: number
  maxAttempts: number
  passingScore: number
  instructions: string
  thumbnail: string
  startTime: string
  endTime: string
  isPublished: boolean
  // Category fields
  subjectId?: string
  chapterId?: string
  topicId?: string
  // Institutional fields
  testType?: string
  institutionName?: string
  testDate?: string
  syllabusCoverage?: string
  printFormat?: boolean
  includeAnswerKey?: boolean
  includeInstructions?: boolean
  language?: string
}

interface Question {
  id: string
  type: QuestionType
  text: string
  options: string[]
  correctAnswer: string
  explanation: string
  points: number
  order?: number
  image?: string
}

const steps = [
  { id: 1, title: "Basic Info", description: "Quiz details and settings" },
  { id: 2, title: "Content", description: "Add questions and content" },
  { id: 3, title: "Settings", description: "Configure timing and access" },
  { id: 4, title: "Preview", description: "Review and publish" }
]

export default function CreateQuiz() {
  const searchParams = useSearchParams()
  const [currentStep, setCurrentStep] = useState(1)
  const [quizData, setQuizData] = useState<QuizData>({
    title: "",
    description: "",
    type: "QUIZ",
    difficulty: "MEDIUM",
    tags: [],
    timeLimit: 30,
    maxAttempts: 1,
    passingScore: 70,
    instructions: "",
    thumbnail: "",
    startTime: "",
    endTime: "",
    isPublished: false,
    testType: "QUIZ",
    institutionName: "",
    testDate: "",
    syllabusCoverage: "SPECIFIC_TOPICS",
    printFormat: false,
    includeAnswerKey: true,
    includeInstructions: true,
    language: "ENGLISH"
  })
  const [questions, setQuestions] = useState<Question[]>([])

  // Ensure questions is always an array
  const safeQuestions = questions || []

  const [showAIGenerator, setShowAIGenerator] = useState(false)
  const [showAICreator, setShowAICreator] = useState(false)
  const [showTestSeriesGenerator, setShowTestSeriesGenerator] = useState(false)
  const [showQuestionEditor, setShowQuestionEditor] = useState(false)
  const [showBulkUpload, setShowBulkUpload] = useState(false)
  const [editingQuestion, setEditingQuestion] = useState<Question | undefined>()
  const [isSaving, setIsSaving] = useState(false)


  // Handle URL parameters for pre-selecting quiz type
  useEffect(() => {
    const type = searchParams.get('type')
    if (type && ['QUIZ', 'TEST_SERIES', 'DAILY_PRACTICE'].includes(type)) {
      setQuizData(prev => ({ ...prev, type: type as QuizType }))
    }
  }, [searchParams])



  const handleThumbnailUpload = async (uploadedFiles: any[]) => {
    if (uploadedFiles && uploadedFiles.length > 0) {
      const file = uploadedFiles[0]
      setQuizData(prev => ({
        ...prev,
        thumbnail: file.url
      }))
      toast.success('Thumbnail uploaded successfully!')
    }
  }

  const removeThumbnail = () => {
    setQuizData(prev => ({
      ...prev,
      thumbnail: ""
    }))
  }

  const nextStep = () => {
    if (steps && currentStep < steps.length) {
      setCurrentStep(currentStep + 1)
    }
  }

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleQuestionsGenerated = (generatedQuestions: any[]) => {
    // Ensure generatedQuestions is an array
    if (!Array.isArray(generatedQuestions)) {
      console.error('Generated questions is not an array:', generatedQuestions)
      toast.error('Invalid questions format received')
      return
    }

    const formattedQuestions: Question[] = generatedQuestions.map((q, index) => {
      // Handle different question types and their specific formats
      let options: string[] = []
      let correctAnswer: string = ''

      switch (q.type) {
        case 'MCQ':
          options = q.options || []
          correctAnswer = q.answer || q.correctAnswer || ''
          break

        case 'TRUE_FALSE':
          options = ['True', 'False']
          correctAnswer = q.answer === true ? 'True' : 'False'
          break

        case 'SHORT_ANSWER':
          options = []
          correctAnswer = q.answer || q.correctAnswer || ''
          break

        case 'MATCHING':
          // For matching questions, store pairs as options and create a structured answer
          options = q.pairs ? q.pairs.map((pair: any) => `${pair.left}|${pair.right}`) : []
          correctAnswer = q.pairs ? JSON.stringify(q.pairs) : ''
          break

        default:
          // Legacy format or fallback
          options = q.options || []
          correctAnswer = q.correctAnswer || q.answer || ''
      }

      return {
        id: Date.now().toString() + index,
        type: q.type || 'MCQ',
        text: q.question || q.text || '',
        options,
        correctAnswer,
        explanation: q.explanation || '',
        points: q.points || 1
      }
    })

    setQuestions(prev => [...prev, ...formattedQuestions])
    setShowAIGenerator(false)
  }

  const handleQuestionSave = (question: Question) => {
    if (editingQuestion) {
      // Update existing question - preserve order
      const updatedQuestion = {
        ...question,
        order: editingQuestion.order || 1
      }
      setQuestions(prev => prev.map(q => q.id === question.id ? updatedQuestion : q))
    } else {
      // Add new question with order
      const newQuestion = {
        ...question,
        order: safeQuestions.length + 1
      }
      setQuestions(prev => [...prev, newQuestion])
    }
    setShowQuestionEditor(false)
    setEditingQuestion(undefined)
  }

  const handleEditQuestion = (question: Question) => {
    setEditingQuestion(question)
    setShowQuestionEditor(true)
  }

  const handleDeleteQuestion = (questionId: string) => {
    setQuestions(prev => prev.filter(q => q.id !== questionId))
  }

  const saveQuiz = async (publish: boolean = false) => {
    if (!quizData.title.trim()) {
      toast.error("Please enter a quiz title")
      return
    }

    if (safeQuestions.length === 0) {
      toast.error("Please add at least one question")
      return
    }

    setIsSaving(true)

    try {
      const payload = {
        ...quizData,
        isPublished: publish,
        questions: safeQuestions.map((q, index) => ({
          ...q,
          order: index + 1
        }))
      }

      const response = await fetch('/api/admin/quizzes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to save quiz')
      }

      await response.json()
      toast.success(publish ? 'Quiz published successfully!' : 'Quiz saved as draft!')

      // Redirect to quiz management page
      window.location.href = '/admin/quizzes'
    } catch (error) {
      console.error('Error saving quiz:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to save quiz')
    } finally {
      setIsSaving(false)
    }
  }

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="title">Quiz Title *</Label>
                  <Input
                    id="title"
                    value={quizData.title}
                    onChange={(e) => setQuizData(prev => ({ ...prev, title: e.target.value }))}
                    placeholder="Enter quiz title"
                    className="mt-1"
                  />
                </div>

                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={quizData.description}
                    onChange={(e) => setQuizData(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Brief description of the quiz"
                    className="mt-1"
                    rows={3}
                  />
                </div>

                <div>
                  <Label>Quiz Type</Label>
                  <RadioGroup
                    value={quizData.type}
                    onValueChange={(value) => setQuizData(prev => ({ ...prev, type: value as QuizType }))}
                    className="mt-2"
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="QUIZ" id="quiz" />
                      <Label htmlFor="quiz">Regular Quiz</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="TEST_SERIES" id="test-series" />
                      <Label htmlFor="test-series">Test Series</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="DAILY_PRACTICE" id="daily-practice" />
                      <Label htmlFor="daily-practice">Daily Practice</Label>
                    </div>
                  </RadioGroup>
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <Label>Difficulty Level</Label>
                  <RadioGroup
                    value={quizData.difficulty}
                    onValueChange={(value) => setQuizData(prev => ({ ...prev, difficulty: value as DifficultyLevel }))}
                    className="mt-2"
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="EASY" id="easy" />
                      <Label htmlFor="easy">Easy</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="MEDIUM" id="medium" />
                      <Label htmlFor="medium">Medium</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="HARD" id="hard" />
                      <Label htmlFor="hard">Hard</Label>
                    </div>
                  </RadioGroup>
                </div>

                <EnhancedTagInput
                  tags={quizData.tags}
                  onTagsChange={(tags) => setQuizData(prev => ({ ...prev, tags }))}
                  quizContent={`${quizData.title} ${quizData.description} ${quizData.instructions}`.trim()}
                  placeholder="Add a tag"
                  maxTags={15}
                  autoGenerateFromContent={true}
                />



                {/* Category Selection */}
                <CategorySelector
                  selectedSubjectId={quizData.subjectId}
                  selectedChapterId={quizData.chapterId}
                  selectedTopicId={quizData.topicId}
                  onSelectionChange={(selection) => {
                    setQuizData(prev => ({
                      ...prev,
                      subjectId: selection.subjectId,
                      chapterId: selection.chapterId,
                      topicId: selection.topicId
                    }))
                  }}
                />

                <div>
                  <Label htmlFor="instructions">Instructions</Label>
                  <Textarea
                    id="instructions"
                    value={quizData.instructions}
                    onChange={(e) => setQuizData(prev => ({ ...prev, instructions: e.target.value }))}
                    placeholder="Special instructions for students"
                    className="mt-1"
                    rows={3}
                  />
                </div>

                <div>
                  <Label>Quiz Thumbnail (Optional)</Label>
                  <div className="mt-2">
                    {quizData.thumbnail ? (
                      <div className="relative inline-block">
                        <img
                          src={quizData.thumbnail}
                          alt="Quiz thumbnail"
                          className="w-32 h-24 object-cover rounded-lg border"
                        />
                        <Button
                          type="button"
                          variant="destructive"
                          size="sm"
                          className="absolute -top-2 -right-2"
                          onClick={removeThumbnail}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    ) : (
                      <FileUpload
                        onUpload={handleThumbnailUpload}
                        acceptedTypes={['image/*']}
                        maxFiles={1}
                        maxSize={2 * 1024 * 1024} // 2MB
                        uploadType="image"
                        folder="thumbnails"
                        multiple={false}
                        className="max-w-sm"
                      />
                    )}
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    Upload an image to represent your quiz (max 2MB)
                  </p>
                </div>
              </div>
            </div>
          </div>
        )

      case 2:
        return (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Quiz Content</h3>
              <div className="flex gap-2">
                <Button variant="outline" onClick={() => setShowAIGenerator(true)}>
                  <Wand2 className="h-4 w-4 mr-2" />
                  Legacy AI Generate
                </Button>
                <Button variant="outline" onClick={() => setShowBulkUpload(true)}>
                  <Upload className="h-4 w-4 mr-2" />
                  Bulk Upload
                </Button>
                <Button onClick={() => setShowQuestionEditor(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Question
                </Button>
              </div>
            </div>

            {safeQuestions.length > 0 && (
              <div className="space-y-4">
                {safeQuestions.map((question, index) => (
                  <Card key={question.id} className="hover:shadow-md transition-shadow">
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <CardTitle className="text-base">Question {index + 1}</CardTitle>
                          <Badge variant="outline">{question.type}</Badge>
                          <Badge variant="secondary">{question.points} pt{question.points !== 1 ? 's' : ''}</Badge>
                        </div>
                        <div className="flex gap-2">
                          <Button variant="outline" size="sm" onClick={() => handleEditQuestion(question)}>
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="outline" size="sm" onClick={() => handleDeleteQuestion(question.id)}>
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <p className="mb-3 font-medium">{question.text}</p>

                      {question.type === 'MCQ' && (
                        <div className="space-y-2">
                          {question.options.map((option, optionIndex) => (
                            <div
                              key={optionIndex}
                              className={`p-3 rounded-lg border transition-colors ${
                                option === question.correctAnswer
                                  ? 'bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800'
                                  : 'bg-muted/50 border-muted'
                              }`}
                            >
                              <div className="flex items-center justify-between">
                                <span>{String.fromCharCode(65 + optionIndex)}. {option}</span>
                                {option === question.correctAnswer && (
                                  <Badge className="bg-green-500 hover:bg-green-600">
                                    Correct
                                  </Badge>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      )}

                      {question.type === 'TRUE_FALSE' && (
                        <div className="space-y-2">
                          {question.options.map((option, optionIndex) => (
                            <div
                              key={optionIndex}
                              className={`p-3 rounded-lg border transition-colors ${
                                option === question.correctAnswer
                                  ? 'bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800'
                                  : 'bg-muted/50 border-muted'
                              }`}
                            >
                              <div className="flex items-center justify-between">
                                <span>{option}</span>
                                {option === question.correctAnswer && (
                                  <Badge className="bg-green-500 hover:bg-green-600">
                                    Correct
                                  </Badge>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      )}

                      {question.type === 'SHORT_ANSWER' && (
                        <div className="p-3 bg-blue-50 rounded-lg border border-blue-200 dark:bg-blue-900/20 dark:border-blue-800">
                          <p className="text-sm font-medium text-blue-800 dark:text-blue-200">
                            Expected Answer: {question.correctAnswer}
                          </p>
                        </div>
                      )}

                      {question.explanation && (
                        <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200 dark:bg-blue-900/20 dark:border-blue-800">
                          <p className="text-sm">
                            <strong className="text-blue-800 dark:text-blue-200">Explanation:</strong>{' '}
                            <span className="text-blue-700 dark:text-blue-300">{question.explanation}</span>
                          </p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}

            {safeQuestions.length === 0 && (
              <Card>
                <CardContent className="pt-6">
                  <div className="text-center py-12">
                    <FileText className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-xl font-semibold mb-2">No questions yet</h3>
                    <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                      Start building your quiz by adding questions manually or use the <strong>AI Quiz Creator</strong> button in the header to generate them automatically from your content.
                    </p>
                    <div className="flex gap-3 justify-center">
                      <Button onClick={() => setShowAIGenerator(true)} variant="outline" size="lg">
                        <Wand2 className="h-5 w-5 mr-2" />
                        Legacy AI Generate
                      </Button>
                      <Button variant="outline" onClick={() => setShowBulkUpload(true)} size="lg">
                        <Upload className="h-5 w-5 mr-2" />
                        Bulk Upload
                      </Button>
                      <Button variant="outline" onClick={() => setShowQuestionEditor(true)} size="lg">
                        <Plus className="h-5 w-5 mr-2" />
                        Add Manually
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}


          </div>
        )

      case 3:
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Clock className="h-5 w-5" />
                    Timing Settings
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="timeLimit">Time Limit (minutes)</Label>
                    <Input
                      id="timeLimit"
                      type="number"
                      value={quizData.timeLimit}
                      onChange={(e) => setQuizData(prev => ({ ...prev, timeLimit: parseInt(e.target.value) || 0 }))}
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <Label htmlFor="startTime">Start Time (optional)</Label>
                    <Input
                      id="startTime"
                      type="datetime-local"
                      value={quizData.startTime}
                      onChange={(e) => setQuizData(prev => ({ ...prev, startTime: e.target.value }))}
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <Label htmlFor="endTime">End Time (optional)</Label>
                    <Input
                      id="endTime"
                      type="datetime-local"
                      value={quizData.endTime}
                      onChange={(e) => setQuizData(prev => ({ ...prev, endTime: e.target.value }))}
                      className="mt-1"
                    />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="h-5 w-5" />
                    Access Settings
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="maxAttempts">Max Attempts</Label>
                    <Input
                      id="maxAttempts"
                      type="number"
                      value={quizData.maxAttempts}
                      onChange={(e) => setQuizData(prev => ({ ...prev, maxAttempts: parseInt(e.target.value) || 1 }))}
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <Label htmlFor="passingScore">Passing Score (%)</Label>
                    <Input
                      id="passingScore"
                      type="number"
                      value={quizData.passingScore}
                      onChange={(e) => setQuizData(prev => ({ ...prev, passingScore: parseInt(e.target.value) || 0 }))}
                      className="mt-1"
                    />
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        )

      case 4:
        return (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Eye className="h-5 w-5" />
                  Quiz Preview
                </CardTitle>
                <CardDescription>
                  Review your quiz before publishing
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h3 className="text-xl font-bold">{quizData.title}</h3>
                    <p className="text-muted-foreground">{quizData.description}</p>
                  </div>
                  
                  <div className="flex flex-wrap gap-2">
                    <Badge>{quizData.type}</Badge>
                    <Badge variant="outline">{quizData.difficulty}</Badge>
                    {quizData.tags.map(tag => (
                      <Badge key={tag} variant="secondary">{tag}</Badge>
                    ))}
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="font-medium">Questions:</span> {safeQuestions.length}
                    </div>
                    <div>
                      <span className="font-medium">Time Limit:</span> {quizData.timeLimit} min
                    </div>
                    <div>
                      <span className="font-medium">Max Attempts:</span> {quizData.maxAttempts}
                    </div>
                    <div>
                      <span className="font-medium">Passing Score:</span> {quizData.passingScore}%
                    </div>
                  </div>

                  {quizData.instructions && (
                    <div className="p-4 bg-blue-50 rounded border border-blue-200">
                      <h4 className="font-medium mb-2">Instructions:</h4>
                      <p className="text-sm">{quizData.instructions}</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Print Format Preview */}
            {safeQuestions.length > 0 && (
              <PrintQuizFormat
                quiz={{
                  title: quizData.title,
                  description: quizData.description,
                  instructions: quizData.instructions,
                  questions: safeQuestions.map((q, index) => ({
                    ...q,
                    order: index + 1
                  })),
                  metadata: {
                    totalPoints: safeQuestions.reduce((sum, q) => sum + (q.points || 1), 0),
                    estimatedDuration: quizData.timeLimit,
                    tags: quizData.tags
                  }
                }}
                institutionName={quizData.institutionName}
                testDate={quizData.testDate}
                testType={quizData.testType}
                includeAnswerKey={quizData.includeAnswerKey}
                includeInstructions={quizData.includeInstructions}
              />
            )}
          </div>
        )

      default:
        return null
    }
  }

  return (
    <div className="p-6 max-w-6xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/admin/quizzes">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Quizzes
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold">Create New Quiz</h1>
            <p className="text-muted-foreground">Build engaging quizzes with AI assistance</p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button onClick={() => setShowAICreator(true)} className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700">
            <Brain className="h-4 w-4 mr-2" />
            AI Quiz Creator
          </Button>
          <Button onClick={() => setShowTestSeriesGenerator(true)} className="bg-gradient-to-r from-green-600 to-teal-600 hover:from-green-700 hover:to-teal-700">
            <GraduationCap className="h-4 w-4 mr-2" />
            Test Series Generator
          </Button>
          <Button variant="outline" onClick={() => saveQuiz(false)} disabled={isSaving}>
            <Save className="h-4 w-4 mr-2" />
            {isSaving ? 'Saving...' : 'Save Draft'}
          </Button>
        </div>
      </div>

      {/* Progress Steps */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          {steps.map((step, index) => (
            <div key={step.id} className="flex items-center">
              <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                currentStep >= step.id 
                  ? 'bg-primary border-primary text-primary-foreground' 
                  : 'border-muted-foreground text-muted-foreground'
              }`}>
                {step.id}
              </div>
              <div className="ml-3">
                <div className={`text-sm font-medium ${
                  currentStep >= step.id ? 'text-foreground' : 'text-muted-foreground'
                }`}>
                  {step.title}
                </div>
                <div className="text-xs text-muted-foreground">{step.description}</div>
              </div>
              {index < steps.length - 1 && (
                <div className={`flex-1 h-0.5 mx-4 ${
                  currentStep > step.id ? 'bg-primary' : 'bg-muted'
                }`} />
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Step Content */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>{steps[currentStep - 1].title}</CardTitle>
          <CardDescription>{steps[currentStep - 1].description}</CardDescription>
        </CardHeader>
        <CardContent className="max-h-[calc(100vh-300px)] overflow-y-auto">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentStep}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
            >
              {renderStepContent()}
            </motion.div>
          </AnimatePresence>
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={prevStep}
          disabled={currentStep === 1}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Previous
        </Button>
        
        {steps && currentStep < steps.length ? (
          <Button onClick={nextStep}>
            Next
            <ArrowRight className="h-4 w-4 ml-2" />
          </Button>
        ) : (
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => saveQuiz(false)} disabled={isSaving}>
              {isSaving ? 'Saving...' : 'Save as Draft'}
            </Button>
            <Button onClick={() => saveQuiz(true)} disabled={isSaving}>
              {isSaving ? 'Publishing...' : 'Publish Quiz'}
            </Button>
          </div>
        )}
      </div>

      {/* Test Series Generator Modal */}
      {showTestSeriesGenerator && (
        <TestSeriesGenerator
          onTestGenerated={(test) => {
            // Handle the complete test generated by AI
            console.log('Generated test:', test)

            if (test) {
              setQuizData(prev => ({
                ...prev,
                title: test.title || prev.title,
                description: test.description || prev.description,
                instructions: test.instructions || prev.instructions,
                testType: test.testType || prev.testType,
                institutionName: test.institutionName || prev.institutionName,
                testDate: test.testDate || prev.testDate,
                syllabusCoverage: test.syllabusCoverage || prev.syllabusCoverage,
                printFormat: test.printFormat !== undefined ? test.printFormat : prev.printFormat,
                includeAnswerKey: test.includeAnswerKey !== undefined ? test.includeAnswerKey : prev.includeAnswerKey,
                includeInstructions: test.includeInstructions !== undefined ? test.includeInstructions : prev.includeInstructions,
                language: test.language || prev.language,
                tags: [
                  ...prev.tags,
                  ...(test.metadata?.tags && Array.isArray(test.metadata.tags) ? test.metadata.tags : [])
                ].filter((tag, index, arr) => arr.indexOf(tag) === index)
              }))

              // Handle questions
              if (test.questions && Array.isArray(test.questions)) {
                const formattedQuestions = test.questions.map((q: any, index: number) => ({
                  id: q.id || `test-${Date.now()}-${index}`,
                  type: q.type || 'MCQ',
                  text: q.text || '',
                  options: Array.isArray(q.options) ? q.options : [],
                  correctAnswer: q.correctAnswer || '',
                  explanation: q.explanation || '',
                  points: q.points || 1,
                  order: index + 1
                }))
                setQuestions(formattedQuestions)
              }

              // Move to review step
              setCurrentStep(4)
              toast.success('Test series generated successfully!')
            }
          }}
          onClose={() => setShowTestSeriesGenerator(false)}
        />
      )}

      {/* AI Quiz Creator Modal */}
      {showAICreator && (
        <AIQuizCreator
          onQuizGenerated={(quiz) => {
            // Handle the complete quiz generated by AI
            console.log('Generated quiz:', quiz) // Debug log

            if (quiz) {
              setQuizData(prev => ({
                ...prev,
                title: quiz.title || prev.title,
                description: quiz.description || prev.description,
                instructions: quiz.instructions || prev.instructions,
                testType: quiz.testType || prev.testType,
                institutionName: quiz.institutionName || prev.institutionName,
                testDate: quiz.testDate || prev.testDate,
                syllabusCoverage: quiz.syllabusCoverage || prev.syllabusCoverage,
                printFormat: quiz.printFormat !== undefined ? quiz.printFormat : prev.printFormat,
                includeAnswerKey: quiz.includeAnswerKey !== undefined ? quiz.includeAnswerKey : prev.includeAnswerKey,
                includeInstructions: quiz.includeInstructions !== undefined ? quiz.includeInstructions : prev.includeInstructions,
                language: quiz.language || prev.language,
                tags: [
                  ...prev.tags,
                  ...(quiz.metadata?.tags && Array.isArray(quiz.metadata.tags) ? quiz.metadata.tags : [])
                ].filter((tag, index, arr) => arr.indexOf(tag) === index) // Remove duplicates
              }))

              // Safely handle questions array
              if (quiz.questions && Array.isArray(quiz.questions)) {
                const formattedQuestions = quiz.questions.map((q: any, index: number) => ({
                  id: q.id || `ai-${Date.now()}-${index}`,
                  type: q.type || 'MCQ',
                  text: q.text || '',
                  options: Array.isArray(q.options) ? q.options : [],
                  correctAnswer: q.correctAnswer || '',
                  explanation: q.explanation || '',
                  points: q.points || 1
                }))
                setQuestions(formattedQuestions)
              }
            }

            setShowAICreator(false)
            toast.success('AI Quiz created successfully!')
          }}
          onClose={() => setShowAICreator(false)}
        />
      )}

      {/* AI Quiz Generator Modal */}
      {showAIGenerator && (
        <AIQuizGenerator
          onQuestionsGenerated={handleQuestionsGenerated}
          onClose={() => setShowAIGenerator(false)}
        />
      )}

      {/* Question Editor Modal */}
      {showQuestionEditor && (
        <QuestionEditor
          question={editingQuestion}
          onSave={handleQuestionSave}
          onCancel={() => {
            setShowQuestionEditor(false)
            setEditingQuestion(undefined)
          }}
        />
      )}

      {/* Bulk Upload Modal */}
      {showBulkUpload && (
        <BulkUpload
          onQuestionsImported={handleQuestionsGenerated}
          onClose={() => setShowBulkUpload(false)}
        />
      )}
    </div>
  )
}
