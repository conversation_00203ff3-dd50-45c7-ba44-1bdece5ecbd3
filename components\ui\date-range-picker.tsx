"use client"

import { useState } from "react"
import { Calendar as CalendarIcon, X } from "lucide-react"
import { format } from "date-fns"
import { DateRange } from "react-day-picker"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

interface DateRangePickerProps {
  value?: DateRange
  onChange?: (range: DateRange | undefined) => void
  placeholder?: string
  className?: string
  disabled?: boolean
}

export function DateRangePicker({
  value,
  onChange,
  placeholder = "Pick a date range",
  className,
  disabled = false
}: DateRangePickerProps) {
  const [isOpen, setIsOpen] = useState(false)

  const handleSelect = (range: DateRange | undefined) => {
    onChange?.(range)
    if (range?.from && range?.to) {
      setIsOpen(false)
    }
  }

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation()
    onChange?.(undefined)
  }

  const formatDateRange = (range: DateRange | undefined) => {
    if (!range?.from) return placeholder
    if (!range.to) return format(range.from, "LLL dd, y")
    return `${format(range.from, "LLL dd, y")} - ${format(range.to, "LLL dd, y")}`
  }

  return (
    <div className={cn("grid gap-2", className)}>
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant="outline"
            className={cn(
              "w-full justify-start text-left font-normal",
              !value && "text-muted-foreground",
              disabled && "opacity-50 cursor-not-allowed"
            )}
            disabled={disabled}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {formatDateRange(value)}
            {value?.from && (
              <X 
                className="ml-auto h-4 w-4 hover:text-destructive" 
                onClick={handleClear}
              />
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <Calendar
            initialFocus
            mode="range"
            defaultMonth={value?.from}
            selected={value}
            onSelect={handleSelect}
            numberOfMonths={2}
          />
        </PopoverContent>
      </Popover>
    </div>
  )
}

// Preset date ranges
export const dateRangePresets = [
  {
    label: "Last 7 days",
    value: "7d",
    getRange: () => {
      const end = new Date()
      const start = new Date()
      start.setDate(end.getDate() - 7)
      return { from: start, to: end }
    }
  },
  {
    label: "Last 30 days", 
    value: "30d",
    getRange: () => {
      const end = new Date()
      const start = new Date()
      start.setDate(end.getDate() - 30)
      return { from: start, to: end }
    }
  },
  {
    label: "Last 90 days",
    value: "90d", 
    getRange: () => {
      const end = new Date()
      const start = new Date()
      start.setDate(end.getDate() - 90)
      return { from: start, to: end }
    }
  },
  {
    label: "Last year",
    value: "1y",
    getRange: () => {
      const end = new Date()
      const start = new Date()
      start.setFullYear(end.getFullYear() - 1)
      return { from: start, to: end }
    }
  },
  {
    label: "This month",
    value: "month",
    getRange: () => {
      const now = new Date()
      const start = new Date(now.getFullYear(), now.getMonth(), 1)
      const end = new Date(now.getFullYear(), now.getMonth() + 1, 0)
      return { from: start, to: end }
    }
  },
  {
    label: "This year",
    value: "year",
    getRange: () => {
      const now = new Date()
      const start = new Date(now.getFullYear(), 0, 1)
      const end = new Date(now.getFullYear(), 11, 31)
      return { from: start, to: end }
    }
  }
]

interface DateRangePickerWithPresetsProps extends DateRangePickerProps {
  showPresets?: boolean
  presets?: typeof dateRangePresets
}

export function DateRangePickerWithPresets({
  value,
  onChange,
  placeholder = "Pick a date range",
  className,
  disabled = false,
  showPresets = true,
  presets = dateRangePresets
}: DateRangePickerWithPresetsProps) {
  const [isOpen, setIsOpen] = useState(false)

  const handlePresetSelect = (preset: typeof dateRangePresets[0]) => {
    const range = preset.getRange()
    onChange?.(range)
    setIsOpen(false)
  }

  const handleSelect = (range: DateRange | undefined) => {
    onChange?.(range)
    if (range?.from && range?.to) {
      setIsOpen(false)
    }
  }

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation()
    onChange?.(undefined)
  }

  const formatDateRange = (range: DateRange | undefined) => {
    if (!range?.from) return placeholder
    if (!range.to) return format(range.from, "LLL dd, y")
    return `${format(range.from, "LLL dd, y")} - ${format(range.to, "LLL dd, y")}`
  }

  return (
    <div className={cn("grid gap-2", className)}>
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant="outline"
            className={cn(
              "w-full justify-start text-left font-normal",
              !value && "text-muted-foreground",
              disabled && "opacity-50 cursor-not-allowed"
            )}
            disabled={disabled}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {formatDateRange(value)}
            {value?.from && (
              <X 
                className="ml-auto h-4 w-4 hover:text-destructive" 
                onClick={handleClear}
              />
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <div className="flex">
            {showPresets && (
              <div className="border-r p-3">
                <div className="text-sm font-medium mb-2">Quick Select</div>
                <div className="space-y-1">
                  {presets.map((preset) => (
                    <Button
                      key={preset.value}
                      variant="ghost"
                      size="sm"
                      className="w-full justify-start text-sm"
                      onClick={() => handlePresetSelect(preset)}
                    >
                      {preset.label}
                    </Button>
                  ))}
                </div>
              </div>
            )}
            <Calendar
              initialFocus
              mode="range"
              defaultMonth={value?.from}
              selected={value}
              onSelect={handleSelect}
              numberOfMonths={2}
            />
          </div>
        </PopoverContent>
      </Popover>
    </div>
  )
}
