import { toast as sonnerToast } from "sonner"

/**
 * Safely convert any value to a string for toast display
 */
function safeToastMessage(message: any): string {
  // Handle Error instances (including custom errors like StudentAPIError)
  if (message instanceof Error) {
    return message.message || 'An error occurred'
  }

  if (typeof message === 'string') {
    return message
  }

  if (message && typeof message === 'object') {
    // Try common message properties
    if (typeof message.message === 'string') {
      return message.message
    }
    if (typeof message.error === 'string') {
      return message.error
    }
    if (typeof message.msg === 'string') {
      return message.msg
    }

    // For API error responses, try to extract the actual error message
    if (message.error && typeof message.error === 'object' && message.error.message) {
      return String(message.error.message)
    }

    // Don't stringify complex objects for user display
    return 'An error occurred'
  }

  // Fallback to string conversion, but avoid [object Object]
  const stringified = String(message)
  return stringified === '[object Object]' ? 'An error occurred' : stringified
}

/**
 * Safe toast wrapper that prevents [object Object] messages
 */
export const toast = {
  success: (message: any, options?: any) => {
    const safeMessage = safeToastMessage(message)
    const safeOptions = options ? {
      ...options,
      description: options.description ? safeToastMessage(options.description) : undefined
    } : undefined
    return sonnerToast.success(safeMessage, safeOptions)
  },
  
  error: (message: any, options?: any) => {
    const safeMessage = safeToastMessage(message)
    const safeOptions = options ? {
      ...options,
      description: options.description ? safeToastMessage(options.description) : undefined
    } : undefined
    return sonnerToast.error(safeMessage, safeOptions)
  },
  
  warning: (message: any, options?: any) => {
    const safeMessage = safeToastMessage(message)
    const safeOptions = options ? {
      ...options,
      description: options.description ? safeToastMessage(options.description) : undefined
    } : undefined
    return sonnerToast.warning(safeMessage, safeOptions)
  },
  
  info: (message: any, options?: any) => {
    const safeMessage = safeToastMessage(message)
    const safeOptions = options ? {
      ...options,
      description: options.description ? safeToastMessage(options.description) : undefined
    } : undefined
    return sonnerToast.info(safeMessage, safeOptions)
  },
  
  // Default toast function
  default: (message: any, options?: any) => {
    const safeMessage = safeToastMessage(message)
    const safeOptions = options ? {
      ...options,
      description: options.description ? safeToastMessage(options.description) : undefined
    } : undefined
    return sonnerToast(safeMessage, safeOptions)
  },
  
  // Loading toast
  loading: (message: any, options?: any) => {
    const safeMessage = safeToastMessage(message)
    const safeOptions = options ? {
      ...options,
      description: options.description ? safeToastMessage(options.description) : undefined
    } : undefined
    return sonnerToast.loading(safeMessage, safeOptions)
  },
  
  // Dismiss function
  dismiss: (toastId?: string | number) => {
    return sonnerToast.dismiss(toastId)
  },
  
  // Promise toast
  promise: <T>(promise: Promise<T>, options: {
    loading: any
    success: any
    error: any
  }) => {
    const safeOptions = {
      loading: safeToastMessage(options.loading),
      success: safeToastMessage(options.success),
      error: safeToastMessage(options.error)
    }
    return sonnerToast.promise(promise, safeOptions)
  }
}

// Export the safe toast as default
export default toast
