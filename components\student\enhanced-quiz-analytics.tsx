'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line
} from 'recharts'
import { TrendingUp, TrendingDown, Clock, Users, Target, Award, Star, Trophy, Activity } from 'lucide-react'

interface QuizAnalytics {
  quiz: {
    id: string
    title: string
    description?: string
    type: string
    difficulty: string
    questionCount: number
    creator: {
      id: string
      name: string
      image?: string
    }
    subject?: {
      id: string
      name: string
    }
    chapter?: {
      id: string
      name: string
    }
    topic?: {
      id: string
      name: string
    }
  }
  userPerformance: {
    totalAttempts: number
    bestScore: number
    averageScore: number
    totalTimeSpent: number
    attempts: Array<{
      id: string
      score: number
      timeSpent?: number
      completedAt: string
    }>
  }
  overview: {
    totalAttempts: number
    uniqueUsers: number
    averageScore: number
    averageTime: number
    completionRate: number
    highestScore: number
    lowestScore: number
  }
  scoreDistribution: Array<{
    range: string
    count: number
  }>
  topPerformers: Array<{
    user: {
      name: string
      image?: string
    }
    score: number
    timeSpent?: number
    completedAt: string
  }>
  reviews: {
    averageRating: number
    totalReviews: number
  }
  insights: Array<{
    type: string
    title: string
    value: string
    trend: 'positive' | 'neutral' | 'negative'
    description: string
  }>
}

interface EnhancedQuizAnalyticsProps {
  quizId: string
}



export function EnhancedQuizAnalytics({ quizId }: EnhancedQuizAnalyticsProps) {
  const [analytics, setAnalytics] = useState<QuizAnalytics | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchAnalytics()
  }, [quizId])

  const fetchAnalytics = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await fetch(`/api/student/quizzes/${quizId}/analytics`)

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        console.error('Analytics API Error:', errorData)
        throw new Error(errorData.message || 'Failed to fetch analytics')
      }

      const response_data = await response.json()
      const data = response_data.data || response_data // Handle both wrapped and unwrapped responses
      setAnalytics(data)
    } catch (error) {
      console.error('Error fetching analytics:', error)
      setError(error instanceof Error ? error.message : 'Failed to load analytics data')
    } finally {
      setLoading(false)
    }
  }

  const formatTime = (seconds: number | null | undefined) => {
    if (!seconds || seconds === 0) return '0m 0s'
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}m ${remainingSeconds}s`
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    })
  }

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'positive':
        return <TrendingUp className="h-4 w-4 text-green-500" />
      case 'negative':
        return <TrendingDown className="h-4 w-4 text-red-500" />
      default:
        return <Activity className="h-4 w-4 text-blue-500" />
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        {[1, 2, 3].map((i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="animate-pulse space-y-4">
                <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                <div className="space-y-2">
                  <div className="h-4 bg-gray-200 rounded"></div>
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (error || !analytics) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <p className="text-muted-foreground">{error || 'No analytics data available'}</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Quiz Information */}
      <Card>
        <CardHeader>
          <div className="flex items-start justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                {analytics.quiz.title}
              </CardTitle>
              <CardDescription className="mt-2">
                {analytics.quiz.description}
              </CardDescription>
              <div className="flex items-center gap-4 mt-4">
                <Badge variant="secondary">{analytics.quiz.type}</Badge>
                <Badge variant="outline">{analytics.quiz.difficulty}</Badge>
                <span className="text-sm text-muted-foreground">
                  {analytics.quiz.questionCount} questions
                </span>
                {analytics.reviews?.averageRating > 0 && (
                  <div className="flex items-center gap-1">
                    <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                    <span className="text-sm">
                      {analytics.reviews.averageRating} ({analytics.reviews.totalReviews})
                    </span>
                  </div>
                )}
              </div>
            </div>
            <Avatar>
              <AvatarImage src={analytics.quiz.creator.image} />
              <AvatarFallback>
                {analytics.quiz.creator.name.split(' ').map(n => n[0]).join('')}
              </AvatarFallback>
            </Avatar>
          </div>
        </CardHeader>
      </Card>

      {/* Your Performance */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Trophy className="h-5 w-5" />
            Your Performance
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">
                {analytics.userPerformance.totalAttempts}
              </div>
              <div className="text-sm text-muted-foreground">Attempts</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {analytics.userPerformance.bestScore?.toFixed(1) || '0.0'}%
              </div>
              <div className="text-sm text-muted-foreground">Best Score</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {analytics.userPerformance.averageScore?.toFixed(1) || '0.0'}%
              </div>
              <div className="text-sm text-muted-foreground">Average Score</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {formatTime(analytics.userPerformance.totalTimeSpent || 0)}
              </div>
              <div className="text-sm text-muted-foreground">Total Time</div>
            </div>
          </div>

          {analytics.userPerformance.attempts.length > 0 && (
            <div className="mt-6">
              <h4 className="font-medium mb-4">Your Attempts</h4>
              <ResponsiveContainer width="100%" height={200}>
                <LineChart data={analytics.userPerformance.attempts.map((attempt, index) => ({
                  attempt: index + 1,
                  score: attempt.score,
                  date: formatDate(attempt.completedAt)
                }))}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="attempt" />
                  <YAxis domain={[0, 100]} />
                  <Tooltip 
                    formatter={(value: number) => [`${value.toFixed(1)}%`, 'Score']}
                    labelFormatter={(label) => `Attempt ${label}`}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="score" 
                    stroke="#8884d8" 
                    strokeWidth={2}
                    dot={{ fill: '#8884d8', strokeWidth: 2, r: 4 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Overall Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Attempts</p>
                <p className="text-2xl font-bold">{analytics.overview.totalAttempts}</p>
              </div>
              <Users className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Average Score</p>
                <p className="text-2xl font-bold">{analytics.overview.averageScore?.toFixed(1) || '0.0'}%</p>
              </div>
              <Target className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Completion Rate</p>
                <p className="text-2xl font-bold">{analytics.overview.completionRate?.toFixed(1) || '0.0'}%</p>
              </div>
              <Activity className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Avg. Time</p>
                <p className="text-2xl font-bold">{analytics.overview.averageTime || 0}m</p>
              </div>
              <Clock className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Score Distribution */}
      <Card>
        <CardHeader>
          <CardTitle>Score Distribution</CardTitle>
          <CardDescription>
            How all students performed on this quiz
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={analytics.scoreDistribution}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="range" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="count" fill="#8884d8" />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Top Performers */}
      {analytics.topPerformers.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Award className="h-5 w-5" />
              Top Performers
            </CardTitle>
            <CardDescription>
              Students who excelled in this quiz
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analytics.topPerformers.slice(0, 5).map((performer, index) => (
                <div key={index} className="flex items-center justify-between p-3 rounded-lg border">
                  <div className="flex items-center gap-3">
                    <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary text-primary-foreground text-sm font-bold">
                      {index + 1}
                    </div>
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={performer.user.image} />
                      <AvatarFallback>
                        {performer.user.name.split(' ').map(n => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="font-medium">{performer.user.name}</p>
                      <p className="text-sm text-muted-foreground">
                        {formatDate(performer.completedAt)}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-bold text-green-600">{performer.score?.toFixed(1) || '0.0'}%</p>
                    {performer.timeSpent && (
                      <p className="text-sm text-muted-foreground">
                        {formatTime(performer.timeSpent)}
                      </p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Insights */}
      {analytics.insights.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Insights</CardTitle>
            <CardDescription>
              Key takeaways about your performance and this quiz
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analytics.insights.map((insight, index) => (
                <div key={index} className="flex items-start gap-3 p-4 rounded-lg border">
                  {getTrendIcon(insight.trend)}
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-medium">{insight.title}</h4>
                      <Badge variant="secondary">{insight.value}</Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {insight.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
