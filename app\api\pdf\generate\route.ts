import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { prisma } from '@/lib/prisma'
import { generateTestPaperPDF } from '@/lib/enhanced-pdf-generator'
import type { QuizData, TestPaperOptions } from '@/lib/enhanced-pdf-generator'

interface PDFGenerationRequest {
  type: 'test-paper' | 'bulk'
  data: any
  options?: TestPaperOptions & {
    template?: string
    format?: 'A4' | 'Letter'
    orientation?: 'portrait' | 'landscape'
    includeCharts?: boolean
    includeDetails?: boolean
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body: PDFGenerationRequest = await request.json()
    const { type, data, options = {} } = body

    // Validate request
    if (!type || !data) {
      return NextResponse.json(
        { error: 'Missing required fields: type and data' },
        { status: 400 }
      )
    }

    // Generate PDF based on type
    let pdfBuffer: Buffer
    let filename: string

    switch (type) {
      case 'test-paper':
        const testPaper = await generateTestPaperPDFWithData(data, options)
        pdfBuffer = testPaper.buffer
        filename = testPaper.filename
        break

      case 'bulk':
        const bulk = await generateBulkPDFWithData(data, options)
        pdfBuffer = bulk.buffer
        filename = bulk.filename
        break

      default:
        return NextResponse.json(
          { error: 'Invalid PDF type. Supported types: test-paper, bulk. Use specific endpoints for quiz-result and analytics PDFs.' },
          { status: 400 }
        )
    }

    // Log the generation
    await prisma.pdfExport.create({
      data: {
        type,
        filename,
        size: pdfBuffer.length,
        userId: session.user.id,
        status: 'completed',
        options: JSON.stringify(options)
      }
    })

    // Return PDF as response
    return new NextResponse(pdfBuffer as any, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Content-Length': pdfBuffer.length.toString()
      }
    })

  } catch (error) {
    console.error('PDF generation error:', error)
    
    // Log the error
    const session = await auth()
    if (session?.user?.id) {
      await prisma.pdfExport.create({
        data: {
          type: 'unknown',
          filename: 'error.pdf',
          size: 0,
          userId: session.user.id,
          status: 'failed',
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      })
    }

    return NextResponse.json(
      { error: 'PDF generation failed' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const type = searchParams.get('type')
    const status = searchParams.get('status')

    // Build query filters
    const where: any = {
      userId: session.user.id
    }

    if (type) {
      where.type = type
    }

    if (status) {
      where.status = status
    }

    // Get PDF exports with pagination
    const [exports, total] = await Promise.all([
      prisma.pdfExport.findMany({
        where,
        orderBy: { createdAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        }
      }),
      prisma.pdfExport.count({ where })
    ])

    return NextResponse.json({
      exports,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })

  } catch (error) {
    console.error('Get PDF exports error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch PDF exports' },
      { status: 500 }
    )
  }
}

// Enhanced PDF generation functions with database integration

async function generateTestPaperPDFWithData(data: { quizId: string }, options: TestPaperOptions): Promise<{ buffer: Buffer, filename: string }> {
  // Fetch quiz data for test paper generation
  const quiz = await prisma.quiz.findUnique({
    where: { id: data.quizId },
    include: {
      questions: {
        orderBy: { order: 'asc' }
      }
    }
  })

  if (!quiz) {
    throw new Error('Quiz not found')
  }

  // Transform database data to enhanced PDF format
  const quizData: QuizData = {
    id: quiz.id,
    title: quiz.title,
    description: quiz.description || '',
    questions: quiz.questions.map(question => ({
      id: question.id,
      text: question.text,
      type: question.type as 'MCQ' | 'TRUE_FALSE' | 'SHORT_ANSWER' | 'ESSAY',
      options: question.options || undefined,
      correctAnswer: question.correctAnswer,
      explanation: question.explanation || undefined,
      points: question.points,
      order: question.order
    })),
    metadata: {
      totalPoints: quiz.questions.reduce((sum, q) => sum + q.points, 0),
      estimatedDuration: quiz.timeLimit || 0,
      difficulty: quiz.difficulty || undefined,
      tags: quiz.tags || undefined
    },
    createdAt: quiz.createdAt.toISOString(),
    updatedAt: quiz.updatedAt.toISOString()
  }

  // Use the enhanced PDF generator
  const pdfBlob = await generateTestPaperPDF(quizData, options)

  // Convert blob to buffer
  const arrayBuffer = await pdfBlob.arrayBuffer()
  const buffer = Buffer.from(arrayBuffer)

  const filename = `test-paper-${quiz.title.replace(/\s+/g, '-')}-${new Date().toISOString().split('T')[0]}.pdf`

  return { buffer, filename }
}

async function generateBulkPDFWithData(_data: any, _options: TestPaperOptions): Promise<{ buffer: Buffer, filename: string }> {
  // For now, return an error as bulk PDF generation needs more complex implementation
  throw new Error('Bulk PDF generation not yet implemented with enhanced generator')
}

export async function DELETE(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const exportId = searchParams.get('id')

    if (!exportId) {
      return NextResponse.json(
        { error: 'Export ID is required' },
        { status: 400 }
      )
    }

    // Check if export exists and belongs to user
    const pdfExport = await prisma.pdfExport.findFirst({
      where: {
        id: exportId,
        userId: session.user.id
      }
    })

    if (!pdfExport) {
      return NextResponse.json(
        { error: 'PDF export not found' },
        { status: 404 }
      )
    }

    // Delete the export record
    await prisma.pdfExport.delete({
      where: { id: exportId }
    })

    return NextResponse.json({
      success: true,
      message: 'PDF export deleted successfully'
    })

  } catch (error) {
    console.error('Delete PDF export error:', error)
    return NextResponse.json(
      { error: 'Failed to delete PDF export' },
      { status: 500 }
    )
  }
}
