import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { notificationService } from '@/lib/notification-service'

// PATCH /api/notifications/[id] - Update notification status
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string } > }
) {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }
    const { id } = await params
    const body = await request.json()
    const { action } = body

    switch (action) {
      case 'mark_read':
        await notificationService.markAsRead(id, session.user.id)
        break
      
      case 'mark_clicked':
        await notificationService.markAsClicked(id, session.user.id)
        break
      
      case 'dismiss':
        await notificationService.dismissNotification(id, session.user.id)
        break
      
      default:
        return NextResponse.json(
          { error: 'Invalid action. Use: mark_read, mark_clicked, or dismiss' },
          { status: 400 }
        )
    }

    return NextResponse.json({
      success: true,
      message: `Notification ${action.replace('_', ' ')} successfully`
    })
  } catch (error) {
    console.error('Error updating notification:', error)
    return NextResponse.json(
      { error: 'Failed to update notification' },
      { status: 500 }
    )
  }
}

// DELETE /api/notifications/[id] - Delete notification (Admin only)
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string } > }
) {
  try {
    const session = await auth()
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // This would delete the notification for all users
    // Implementation depends on requirements
    
    return NextResponse.json({
      success: true,
      message: 'Notification deleted successfully'
    })
  } catch (error) {
    console.error('Error deleting notification:', error)
    return NextResponse.json(
      { error: 'Failed to delete notification' },
      { status: 500 }
    )
  }
}
