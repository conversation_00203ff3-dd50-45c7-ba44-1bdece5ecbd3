import { toast } from "sonner"

// Track recent toasts to prevent duplicates
const recentToasts = new Set<string>()

/**
 * Safely convert any value to a string for toast display
 */
function safeToastMessage(message: any): string {
  // Handle Error instances (including custom errors)
  if (message instanceof Error) {
    return message.message || 'An error occurred'
  }

  if (typeof message === 'string') {
    return message
  }

  if (message && typeof message === 'object') {
    // Try common message properties
    if (typeof message.message === 'string') {
      return message.message
    }
    if (typeof message.error === 'string') {
      return message.error
    }
    if (typeof message.msg === 'string') {
      return message.msg
    }

    // For API error responses, try to extract the actual error message
    if (message.error && typeof message.error === 'object' && message.error.message) {
      return String(message.error.message)
    }

    // Don't stringify complex objects for user display
    return 'An error occurred'
  }

  // Fallback to string conversion
  return String(message) === '[object Object]' ? 'An error occurred' : String(message)
}

/**
 * Show a toast with deduplication
 * Prevents the same toast from appearing multiple times within a short period
 */
export function showToast(
  type: 'success' | 'error' | 'info' | 'warning' | 'default',
  message: any,
  options?: {
    description?: string
    duration?: number
    deduplicationTime?: number
  }
) {
  const { description, duration = 4000, deduplicationTime = 3000 } = options || {}

  // Safely convert message to string
  const safeMessage = safeToastMessage(message)
  const safeDescription = description ? safeToastMessage(description) : undefined

  // Create a unique key for deduplication
  const toastKey = `${type}-${safeMessage}-${safeDescription || ''}`
  
  // Check if we've already shown this toast recently
  if (recentToasts.has(toastKey)) {
    return // Skip duplicate toast
  }
  
  // Add to recent toasts and remove after deduplication time
  recentToasts.add(toastKey)
  setTimeout(() => {
    recentToasts.delete(toastKey)
  }, deduplicationTime)
  
  // Show the toast
  const toastOptions = {
    description: safeDescription,
    duration
  }

  switch (type) {
    case 'success':
      return toast.success(safeMessage, toastOptions)
    case 'error':
      return toast.error(safeMessage, toastOptions)
    case 'warning':
      return toast.warning(safeMessage, toastOptions)
    case 'info':
      return toast.info(safeMessage, toastOptions)
    default:
      return toast(safeMessage, toastOptions)
  }
}

/**
 * Convenience functions for different toast types
 */
export const toastUtils = {
  success: (message: any, options?: { description?: any; duration?: number }) =>
    showToast('success', message, options),

  error: (message: any, options?: { description?: any; duration?: number }) =>
    showToast('error', message, options),

  warning: (message: any, options?: { description?: any; duration?: number }) =>
    showToast('warning', message, options),

  info: (message: any, options?: { description?: any; duration?: number }) =>
    showToast('info', message, options),

  default: (message: any, options?: { description?: any; duration?: number }) =>
    showToast('default', message, options),
}

/**
 * Clear all recent toast tracking
 * Useful for testing or when you want to reset the deduplication
 */
export function clearToastHistory() {
  recentToasts.clear()
}
