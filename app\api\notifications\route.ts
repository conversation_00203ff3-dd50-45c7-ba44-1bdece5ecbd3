import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { notificationService } from '@/lib/notification-service'
import { NotificationType } from '@prisma/client'

// GET /api/notifications - Get user notifications
export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const unreadOnly = searchParams.get('unreadOnly') === 'true'
    const category = searchParams.get('category') || undefined
    const type = searchParams.get('type') as NotificationType || undefined

    const result = await notificationService.getUserNotifications(
      session.user.id,
      { page, limit, unreadOnly, category, type }
    )

    return NextResponse.json(result)
  } catch (error) {
    console.error('Error fetching notifications:', error)
    return NextResponse.json(
      { error: 'Failed to fetch notifications' },
      { status: 500 }
    )
  }
}

// POST /api/notifications - Send notification (Admin only)
export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const {
      recipients,
      type,
      title,
      message,
      data,
      actionUrl,
      imageUrl,
      priority,
      category,
      expiresAt,
      scheduledAt,
      sendToAll,
      sendToRole
    } = body

    // Validate required fields
    if (!type || !title || !message) {
      return NextResponse.json(
        { error: 'Missing required fields: type, title, message' },
        { status: 400 }
      )
    }

    const notificationData = {
      type,
      title,
      message,
      data,
      actionUrl,
      imageUrl,
      priority,
      category,
      expiresAt: expiresAt ? new Date(expiresAt) : undefined,
      scheduledAt: scheduledAt ? new Date(scheduledAt) : undefined
    }

    let notificationId: string

    if (sendToAll) {
      // Send to all users
      notificationId = await notificationService.sendToAll(notificationData)
    } else if (sendToRole) {
      // Send to users with specific role
      notificationId = await notificationService.sendToRole(sendToRole, notificationData)
    } else if (recipients && Array.isArray(recipients)) {
      // Send to specific users
      notificationId = await notificationService.sendToUsers(recipients, notificationData)
    } else {
      return NextResponse.json(
        { error: 'Must specify recipients, sendToAll, or sendToRole' },
        { status: 400 }
      )
    }

    return NextResponse.json({
      success: true,
      notificationId,
      message: 'Notification sent successfully'
    })
  } catch (error) {
    console.error('Error sending notification:', error)
    return NextResponse.json(
      { error: 'Failed to send notification' },
      { status: 500 }
    )
  }
}

// GET /api/notifications/unread-count - Get unread notification count
export async function HEAD(request: NextRequest) {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const count = await notificationService.getUnreadCount(session.user.id)
    
    return NextResponse.json({ count })
  } catch (error) {
    console.error('Error getting unread count:', error)
    return NextResponse.json(
      { error: 'Failed to get unread count' },
      { status: 500 }
    )
  }
}
