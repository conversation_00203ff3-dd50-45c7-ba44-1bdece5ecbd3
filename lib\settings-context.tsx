"use client"

import React, { createContext, useContext, useEffect, useState, useCallback } from 'react'
import { useSession } from 'next-auth/react'

interface SystemSettings {
  siteName: string
  siteDescription: string
  siteUrl: string
  adminEmail: string
  allowRegistration: boolean
  requireEmailVerification: boolean
  defaultUserRole: 'STUDENT' | 'ADMIN'
  maxFileSize: number
  allowedFileTypes: string[]
  enableNotifications: boolean
  enableAnalytics: boolean
  maintenanceMode: boolean
  theme: 'light' | 'dark' | 'system'
  timezone: string
  language: string
}

interface SecuritySettings {
  sessionTimeout: number
  maxLoginAttempts: number
  passwordMinLength: number
  requireStrongPassword: boolean
  enableTwoFactor: boolean
}

interface EmailSettings {
  smtpHost: string
  smtpPort: number
  smtpUser: string
  smtpPassword: string
  smtpSecure: boolean
  fromEmail: string
  fromName: string
  enableEmailNotifications: boolean
}

interface SettingsContextType {
  systemSettings: SystemSettings
  securitySettings: SecuritySettings
  emailSettings: EmailSettings
  loading: boolean
  error: string | null
  refreshSettings: () => Promise<void>
}

const defaultSystemSettings: SystemSettings = {
  siteName: 'QuizMaster',
  siteDescription: 'AI-Powered Learning Platform',
  siteUrl: 'http://localhost:3000',
  adminEmail: '<EMAIL>',
  allowRegistration: true,
  requireEmailVerification: false,
  defaultUserRole: 'STUDENT',
  maxFileSize: 10,
  allowedFileTypes: ['pdf', 'doc', 'docx', 'txt', 'jpg', 'png'],
  enableNotifications: true,
  enableAnalytics: true,
  maintenanceMode: false,
  theme: 'system',
  timezone: 'UTC',
  language: 'en'
}

const defaultSecuritySettings: SecuritySettings = {
  sessionTimeout: 24,
  maxLoginAttempts: 5,
  passwordMinLength: 8,
  requireStrongPassword: true,
  enableTwoFactor: false
}

const defaultEmailSettings: EmailSettings = {
  smtpHost: '',
  smtpPort: 587,
  smtpUser: '',
  smtpPassword: '',
  smtpSecure: true,
  fromEmail: '',
  fromName: 'QuizMaster',
  enableEmailNotifications: true
}

const SettingsContext = createContext<SettingsContextType | undefined>(undefined)

export function SettingsProvider({ children }: { children: React.ReactNode }) {
  const { data: session, status } = useSession()
  const [systemSettings, setSystemSettings] = useState<SystemSettings>(defaultSystemSettings)
  const [securitySettings, setSecuritySettings] = useState<SecuritySettings>(defaultSecuritySettings)
  const [emailSettings, setEmailSettings] = useState<EmailSettings>(defaultEmailSettings)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const refreshSettings = useCallback(async () => {
    // Only allow admin users to fetch settings
    if (session?.user?.role !== 'ADMIN') {
      console.warn('Settings can only be fetched by admin users')
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)

      const response = await fetch('/api/admin/settings')
      const data = await response.json()

      if (response.ok && data.success) {
        if (data.data) {
          setSystemSettings(prev => ({ ...prev, ...data.data.system }))
          setSecuritySettings(prev => ({ ...prev, ...data.data.security }))
          setEmailSettings(prev => ({ ...prev, ...data.data.email }))
        }
      } else {
        throw new Error(data.message || 'Failed to fetch settings')
      }
    } catch (err: any) {
      console.error('Error fetching settings:', err)
      setError(err.message || 'Failed to load settings')
    } finally {
      setLoading(false)
    }
  }, [session])

  useEffect(() => {
    // Only fetch settings for admin users
    if (status === 'authenticated' && session?.user?.role === 'ADMIN') {
      refreshSettings()
    } else if (status === 'authenticated') {
      // For non-admin users, just set loading to false and use defaults
      setLoading(false)
    }
  }, [session, status, refreshSettings])

  const value: SettingsContextType = {
    systemSettings,
    securitySettings,
    emailSettings,
    loading,
    error,
    refreshSettings
  }

  return (
    <SettingsContext.Provider value={value}>
      {children}
    </SettingsContext.Provider>
  )
}

export function useSettings() {
  const context = useContext(SettingsContext)
  if (context === undefined) {
    throw new Error('useSettings must be used within a SettingsProvider')
  }
  return context
}

// Hook to check if maintenance mode is active
export function useMaintenanceMode() {
  const { systemSettings } = useSettings()
  return systemSettings.maintenanceMode
}

// Hook to get file upload settings
export function useFileUploadSettings() {
  const { systemSettings } = useSettings()
  return {
    maxFileSize: systemSettings.maxFileSize,
    allowedFileTypes: systemSettings.allowedFileTypes
  }
}

// Hook to get security settings
export function useSecuritySettings() {
  const { securitySettings } = useSettings()
  return securitySettings
}

// Hook to get email settings
export function useEmailSettings() {
  const { emailSettings } = useSettings()
  return emailSettings
}
