"use client"

import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  CheckCircle, 
  AlertTriangle, 
  XCircle, 
  Info,
  Shield,
  Mail,
  Globe,
  Database
} from "lucide-react"

interface SettingsOverviewProps {
  systemSettings?: any
  securitySettings?: any
  emailSettings?: any
}

export function SettingsOverview({ 
  systemSettings, 
  securitySettings, 
  emailSettings 
}: SettingsOverviewProps) {
  const getSystemStatus = () => {
    if (!systemSettings) return { status: 'unknown', message: 'Loading...' }
    
    const issues = []
    if (!systemSettings.siteName) issues.push('Site name not set')
    if (!systemSettings.adminEmail) issues.push('Admin email not set')
    if (systemSettings.maintenanceMode) issues.push('Maintenance mode active')
    
    if (issues.length === 0) {
      return { status: 'healthy', message: 'System configured properly' }
    } else if (issues.length <= 2) {
      return { status: 'warning', message: `${issues.length} configuration issue(s)` }
    } else {
      return { status: 'error', message: 'Multiple configuration issues' }
    }
  }

  const getSecurityStatus = () => {
    if (!securitySettings) return { status: 'unknown', message: 'Loading...' }
    
    const issues = []
    if (!securitySettings.requireStrongPassword) issues.push('Weak password policy')
    if (securitySettings.sessionTimeout > 48) issues.push('Long session timeout')
    if (!securitySettings.rateLimitEnabled) issues.push('Rate limiting disabled')
    
    if (issues.length === 0) {
      return { status: 'healthy', message: 'Security properly configured' }
    } else if (issues.length <= 1) {
      return { status: 'warning', message: `${issues.length} security concern(s)` }
    } else {
      return { status: 'error', message: 'Multiple security issues' }
    }
  }

  const getEmailStatus = () => {
    if (!emailSettings) return { status: 'unknown', message: 'Loading...' }
    
    const configured = emailSettings.smtpHost && 
                      emailSettings.smtpUser && 
                      emailSettings.fromEmail
    
    if (configured && emailSettings.enableEmailNotifications) {
      return { status: 'healthy', message: 'Email configured and enabled' }
    } else if (configured) {
      return { status: 'warning', message: 'Email configured but disabled' }
    } else {
      return { status: 'error', message: 'Email not configured' }
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />
      case 'error':
        return <XCircle className="h-5 w-5 text-red-500" />
      default:
        return <Info className="h-5 w-5 text-gray-500" />
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'healthy':
        return <Badge variant="default" className="bg-green-500">Healthy</Badge>
      case 'warning':
        return <Badge variant="secondary" className="bg-yellow-500 text-white">Warning</Badge>
      case 'error':
        return <Badge variant="destructive">Error</Badge>
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  const systemStatus = getSystemStatus()
  const securityStatus = getSecurityStatus()
  const emailStatus = getEmailStatus()

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">System Status</CardTitle>
          <Globe className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2">
            {getStatusIcon(systemStatus.status)}
            {getStatusBadge(systemStatus.status)}
          </div>
          <p className="text-xs text-muted-foreground mt-2">
            {systemStatus.message}
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Security</CardTitle>
          <Shield className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2">
            {getStatusIcon(securityStatus.status)}
            {getStatusBadge(securityStatus.status)}
          </div>
          <p className="text-xs text-muted-foreground mt-2">
            {securityStatus.message}
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Email</CardTitle>
          <Mail className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2">
            {getStatusIcon(emailStatus.status)}
            {getStatusBadge(emailStatus.status)}
          </div>
          <p className="text-xs text-muted-foreground mt-2">
            {emailStatus.message}
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Database</CardTitle>
          <Database className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2">
            <CheckCircle className="h-5 w-5 text-green-500" />
            <Badge variant="default" className="bg-green-500">Connected</Badge>
          </div>
          <p className="text-xs text-muted-foreground mt-2">
            Database connection active
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
