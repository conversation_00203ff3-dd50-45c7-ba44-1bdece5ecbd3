import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { prisma } from '@/lib/prisma'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth()
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { id } = await params

    // Get quiz details with questions
    const quiz = await prisma.quiz.findUnique({
      where: { id },
      include: {
        questions: {
          select: {
            id: true,
            text: true,
            type: true,
            points: true,
            difficulty: true
          }
        },
        _count: {
          select: {
            questions: true
          }
        }
      }
    })

    if (!quiz) {
      return NextResponse.json(
        { error: 'Quiz not found' },
        { status: 404 }
      )
    }

    // Get all attempts for this quiz with answers
    const attempts = await prisma.quizAttempt.findMany({
      where: { quizId: id },
      include: {
        user: {
          select: {
            name: true,
            email: true
          }
        }
      },
      orderBy: { startedAt: 'desc' }
    })

    // Get all answers for completed attempts to calculate question analytics
    const completedAttemptIds = attempts.filter(a => a.isCompleted).map(a => a.id)
    const allAnswers = await prisma.quizAnswer.findMany({
      where: {
        attemptId: { in: completedAttemptIds }
      },
      select: {
        questionId: true,
        answer: true,
        isCorrect: true,
        timeSpent: true
      }
    })

    // Calculate analytics
    const totalAttempts = attempts.length
    const uniqueUsers = new Set(attempts.map(a => a.userId)).size
    const completedAttempts = attempts.filter(a => a.isCompleted)
    const completionRate = totalAttempts > 0 ? (completedAttempts.length / totalAttempts) * 100 : 0

    // Calculate average score and time
    const averageScore = completedAttempts.length > 0 
      ? completedAttempts.reduce((sum, a) => sum + (a.percentage || 0), 0) / completedAttempts.length
      : 0

    const averageTime = completedAttempts.length > 0
      ? completedAttempts.reduce((sum, a) => sum + (a.timeSpent || 0), 0) / completedAttempts.length / 60 // Convert to minutes
      : 0

    // Calculate pass rate using quiz's actual passing score
    const passingScore = quiz.passingScore || 0
    const passedAttempts = completedAttempts.filter(a => (a.percentage || 0) >= passingScore)
    const passRate = completedAttempts.length > 0 ? (passedAttempts.length / completedAttempts.length) * 100 : 0

    // Score distribution
    const scoreRanges = [
      { range: '0-20%', count: 0 },
      { range: '21-40%', count: 0 },
      { range: '41-60%', count: 0 },
      { range: '61-80%', count: 0 },
      { range: '81-100%', count: 0 }
    ]

    completedAttempts.forEach(attempt => {
      const score = attempt.percentage || 0
      if (score <= 20) scoreRanges[0].count++
      else if (score <= 40) scoreRanges[1].count++
      else if (score <= 60) scoreRanges[2].count++
      else if (score <= 80) scoreRanges[3].count++
      else scoreRanges[4].count++
    })

    // Recent attempts (last 10)
    const recentAttempts = attempts.slice(0, 10).map(attempt => ({
      id: attempt.id,
      user: attempt.user,
      score: attempt.score || 0,
      percentage: attempt.percentage || 0,
      timeSpent: attempt.timeSpent || 0,
      isCompleted: attempt.isCompleted,
      startedAt: attempt.startedAt.toISOString(),
      completedAt: attempt.completedAt?.toISOString() || null
    }))

    // Performance over time (last 30 days)
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

    const recentAttempts30Days = attempts.filter(a => a.startedAt >= thirtyDaysAgo)
    const performanceOverTime = []

    // Group by day
    for (let i = 29; i >= 0; i--) {
      const date = new Date()
      date.setDate(date.getDate() - i)
      const dayStart = new Date(date.getFullYear(), date.getMonth(), date.getDate())
      const dayEnd = new Date(dayStart)
      dayEnd.setDate(dayEnd.getDate() + 1)

      const dayAttempts = recentAttempts30Days.filter(a => 
        a.startedAt >= dayStart && a.startedAt < dayEnd && a.isCompleted
      )

      const avgScore = dayAttempts.length > 0
        ? dayAttempts.reduce((sum, a) => sum + (a.percentage || 0), 0) / dayAttempts.length
        : 0

      performanceOverTime.push({
        date: dayStart.toISOString().split('T')[0],
        attempts: dayAttempts.length,
        averageScore: Math.round(avgScore * 100) / 100
      })
    }

    // Calculate question-level analytics
    const questionAnalytics = quiz.questions.map(question => {
      const questionAnswers = allAnswers.filter(a => a.questionId === question.id)
      const correctAnswers = questionAnswers.filter(a => a.isCorrect).length
      const totalAnswers = questionAnswers.length
      const averageTime = questionAnswers.length > 0
        ? questionAnswers.reduce((sum, a) => sum + (a.timeSpent || 0), 0) / questionAnswers.length
        : 0

      // Calculate difficulty based on correct answer rate (lower rate = higher difficulty)
      const correctRate = totalAnswers > 0 ? correctAnswers / totalAnswers : 0
      const difficulty = 1 - correctRate // 0 = easy (100% correct), 1 = hard (0% correct)

      return {
        questionId: question.id,
        questionText: question.text,
        questionType: question.type,
        correctAnswers,
        totalAnswers,
        averageTime: Math.round(averageTime),
        difficulty: Math.round(difficulty * 100) / 100, // Round to 2 decimal places
        correctRate: Math.round(correctRate * 100) / 100
      }
    })

    const analytics = {
      quiz: {
        id: quiz.id,
        title: quiz.title,
        questionCount: quiz._count.questions
      },
      overview: {
        totalAttempts,
        uniqueUsers,
        averageScore: Math.round(averageScore * 100) / 100,
        averageTime: Math.round(averageTime * 100) / 100,
        completionRate: Math.round(completionRate * 100) / 100,
        passRate: Math.round(passRate * 100) / 100
      },
      scoreDistribution: scoreRanges,
      recentAttempts,
      performanceOverTime,
      questionAnalytics,
      topPerformers: completedAttempts
        .sort((a, b) => (b.percentage || 0) - (a.percentage || 0))
        .slice(0, 5)
        .map(attempt => ({
          user: attempt.user,
          score: attempt.percentage || 0,
          timeSpent: attempt.timeSpent || 0,
          completedAt: attempt.completedAt?.toISOString() || null
        })),
      insights: [
        {
          type: 'completion_rate',
          title: 'Completion Rate',
          value: `${Math.round(completionRate)}%`,
          trend: completionRate >= 80 ? 'positive' : completionRate >= 60 ? 'neutral' : 'negative',
          description: completionRate >= 80 
            ? 'Excellent completion rate' 
            : completionRate >= 60 
              ? 'Good completion rate' 
              : 'Low completion rate - consider reviewing quiz difficulty'
        },
        {
          type: 'average_score',
          title: 'Average Score',
          value: `${Math.round(averageScore)}%`,
          trend: averageScore >= 80 ? 'positive' : averageScore >= 60 ? 'neutral' : 'negative',
          description: averageScore >= 80 
            ? 'Students are performing well' 
            : averageScore >= 60 
              ? 'Average performance' 
              : 'Students may need additional support'
        },
        {
          type: 'engagement',
          title: 'Student Engagement',
          value: `${uniqueUsers} students`,
          trend: uniqueUsers >= 50 ? 'positive' : uniqueUsers >= 20 ? 'neutral' : 'negative',
          description: uniqueUsers >= 50 
            ? 'High student engagement' 
            : uniqueUsers >= 20 
              ? 'Moderate engagement' 
              : 'Consider promoting the quiz more'
        }
      ]
    }

    return NextResponse.json(analytics)

  } catch (error) {
    console.error('Error fetching quiz analytics:', error)
    return NextResponse.json(
      { error: 'Failed to fetch analytics' },
      { status: 500 }
    )
  }
}
