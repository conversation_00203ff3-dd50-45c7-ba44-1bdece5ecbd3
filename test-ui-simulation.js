const { io } = require('socket.io-client');

// Simulate the browser UI behavior
async function testUISimulation() {
  console.log('🧪 Testing UI simulation...');

  // Create socket connection like the browser would
  const socket = io('http://localhost:3001', {
    transports: ['websocket', 'polling']
  });

  // Wait for connection
  await new Promise((resolve) => {
    socket.on('connect', () => {
      console.log('🔌 Connected:', socket.id);
      resolve();
    });
  });

  // Authenticate like the browser would
  let authenticated = false;
  socket.on('authenticated', () => {
    console.log('✅ Authenticated');
    authenticated = true;
  });

  socket.emit('authenticate', {
    userId: 'browser-user-1',
    name: 'Browser User',
    email: '<EMAIL>',
    role: 'STUDENT'
  });

  // Wait for authentication
  while (!authenticated) {
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  // Set up message listener like ChatRoom component would
  const receivedMessages = [];
  socket.on('chat:message_received', (message) => {
    console.log('📥 UI received message:', message);
    receivedMessages.push(message);
  });

  // Join room like ChatRoom component would
  console.log('🏠 Joining room: student-general');
  socket.emit('chat:join', { roomId: 'student-general' });

  await new Promise(resolve => setTimeout(resolve, 1000));

  // Send message like ChatRoom component would
  console.log('📤 Sending message from UI...');
  socket.emit('chat:message', {
    roomId: 'student-general',
    message: 'Hello from simulated UI!',
    type: 'text'
  });

  // Wait for message to be processed
  await new Promise(resolve => setTimeout(resolve, 2000));

  console.log('📊 Results:');
  console.log('Messages received by UI:', receivedMessages.length);
  
  if (receivedMessages.length > 0) {
    console.log('✅ UI simulation PASSED - messages are being received');
    receivedMessages.forEach((msg, index) => {
      console.log(`Message ${index + 1}:`, msg.message);
    });
  } else {
    console.log('❌ UI simulation FAILED - no messages received');
  }

  socket.disconnect();
}

// Run the test
testUISimulation().catch(console.error);
