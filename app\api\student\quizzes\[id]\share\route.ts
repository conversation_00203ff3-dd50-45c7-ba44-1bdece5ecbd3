import { NextRequest } from 'next/server'
import { z } from 'zod'
import { createAPIHandler, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'

const shareSchema = z.object({
  platform: z.enum(['facebook', 'twitter', 'linkedin', 'whatsapp', 'email', 'copy']),
  message: z.string().optional()
})

// POST /api/student/quizzes/[id]/share - Generate sharing links and track shares
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT',
    validateBody: shareSchema
  },
  async (request: NextRequest, { user, params, validatedBody }) => {
    const resolvedParams = await params
    const quizId = resolvedParams?.id as string
    const { platform, message } = validatedBody

    if (!quizId) {
      return APIResponse.error('Quiz ID is required', 400)
    }

    // Check if quiz exists and is published
    const quiz = await prisma.quiz.findFirst({
      where: {
        id: quizId,
        isPublished: true
      },
      include: {
        creator: {
          select: {
            name: true
          }
        },
        _count: {
          select: {
            questions: true,
            reviews: true
          }
        },
        reviews: {
          select: {
            rating: true
          },
          where: {
            isPublic: true
          }
        }
      }
    })

    if (!quiz) {
      return APIResponse.error('Quiz not found', 404)
    }

    // Calculate average rating
    const averageRating = quiz.reviews.length > 0 
      ? quiz.reviews.reduce((sum, review) => sum + review.rating, 0) / quiz.reviews.length
      : 0

    // Generate base URL for the quiz
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'
    const quizUrl = `${baseUrl}/student/browse/${quizId}`

    // Generate sharing content
    const defaultMessage = message || `Check out this ${quiz.type.toLowerCase()}: "${quiz.title}" by ${quiz.creator.name}. ${quiz._count.questions} questions${averageRating > 0 ? ` • ${averageRating.toFixed(1)}⭐ rating` : ''}. Test your knowledge now!`

    // Generate platform-specific sharing URLs
    const sharingUrls = {
      facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(quizUrl)}&quote=${encodeURIComponent(defaultMessage)}`,
      twitter: `https://twitter.com/intent/tweet?url=${encodeURIComponent(quizUrl)}&text=${encodeURIComponent(defaultMessage)}&hashtags=quiz,learning,education`,
      linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(quizUrl)}&title=${encodeURIComponent(quiz.title)}&summary=${encodeURIComponent(defaultMessage)}`,
      whatsapp: `https://wa.me/?text=${encodeURIComponent(`${defaultMessage} ${quizUrl}`)}`,
      email: `mailto:?subject=${encodeURIComponent(`Check out: ${quiz.title}`)}&body=${encodeURIComponent(`${defaultMessage}\n\nTake the quiz here: ${quizUrl}`)}`,
      copy: quizUrl
    }

    // Log the share action (optional - for analytics)
    try {
      // You could add a shares tracking table here if needed
      console.log(`Quiz ${quizId} shared on ${platform} by user ${user.id}`)
    } catch (error) {
      console.error('Error logging share action:', error)
    }

    return APIResponse.success({
      quiz: {
        id: quiz.id,
        title: quiz.title,
        description: quiz.description,
        type: quiz.type,
        difficulty: quiz.difficulty,
        questionCount: quiz._count.questions,
        averageRating: Math.round(averageRating * 10) / 10,
        reviewCount: quiz._count.reviews,
        creator: quiz.creator
      },
      sharing: {
        platform,
        url: sharingUrls[platform],
        message: defaultMessage,
        directUrl: quizUrl
      }
    })
  }
)

// GET /api/student/quizzes/[id]/share - Get sharing information for a quiz
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT'
  },
  async (request: NextRequest, { user, params }) => {
    const resolvedParams = await params
    const quizId = resolvedParams?.id as string

    if (!quizId) {
      return APIResponse.error('Quiz ID is required', 400)
    }

    // Check if quiz exists and is published
    const quiz = await prisma.quiz.findFirst({
      where: {
        id: quizId,
        isPublished: true
      },
      include: {
        creator: {
          select: {
            name: true
          }
        },
        _count: {
          select: {
            questions: true,
            reviews: true
          }
        },
        reviews: {
          select: {
            rating: true
          },
          where: {
            isPublic: true
          }
        }
      }
    })

    if (!quiz) {
      return APIResponse.error('Quiz not found', 404)
    }

    // Calculate average rating
    const averageRating = quiz.reviews.length > 0 
      ? quiz.reviews.reduce((sum, review) => sum + review.rating, 0) / quiz.reviews.length
      : 0

    // Generate base URL for the quiz
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'
    const quizUrl = `${baseUrl}/student/browse/${quizId}`

    // Generate default sharing message
    const defaultMessage = `Check out this ${quiz.type.toLowerCase()}: "${quiz.title}" by ${quiz.creator.name}. ${quiz._count.questions} questions${averageRating > 0 ? ` • ${averageRating.toFixed(1)}⭐ rating` : ''}. Test your knowledge now!`

    return APIResponse.success({
      quiz: {
        id: quiz.id,
        title: quiz.title,
        description: quiz.description,
        type: quiz.type,
        difficulty: quiz.difficulty,
        questionCount: quiz._count.questions,
        averageRating: Math.round(averageRating * 10) / 10,
        reviewCount: quiz._count.reviews,
        creator: quiz.creator
      },
      sharing: {
        url: quizUrl,
        message: defaultMessage,
        platforms: ['facebook', 'twitter', 'linkedin', 'whatsapp', 'email', 'copy']
      }
    })
  }
)
