"use client"

import React, { useState } from "react"
import { Card, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { 
  Wand2, 
  Upload, 
  FileText, 
  Loader2, 
  CheckCircle,
  AlertCircle,
  X,
  Plus
} from "lucide-react"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { motion, AnimatePresence } from "framer-motion"
import { experimental_useObject } from "ai/react"
import { flexibleQuestionsSchema } from "@/lib/schemas"
import { z } from "zod"
import { toast } from "sonner"

interface AIQuizGeneratorProps {
  onQuestionsGenerated: (questions: any[]) => void
  onClose: () => void
}

export function AIQuizGenerator({ onQuestionsGenerated, onClose }: AIQuizGeneratorProps) {
  const [generationMethod, setGenerationMethod] = useState<'text' | 'file'>('text')
  const [textContent, setTextContent] = useState("")
  const [customPrompt, setCustomPrompt] = useState("")
  const [files, setFiles] = useState<File[]>([])
  const [questionCount, setQuestionCount] = useState(5)
  const [difficulty, setDifficulty] = useState<'EASY' | 'MEDIUM' | 'HARD'>('MEDIUM')
  const [questionTypes, setQuestionTypes] = useState<string[]>(['MCQ'])

  const {
    submit,
    object: partialQuestions,
    isLoading,
    error
  } = experimental_useObject({
    api: "/api/generate-quiz",
    schema: flexibleQuestionsSchema,
    initialValue: undefined,
    onError: (error) => {
      console.error("Quiz generation error:", error)
      toast.error("Failed to generate quiz. Please try again.")
    },
    onFinish: ({ object }) => {
      if (object) {
        if (object.length !== questionCount) {
          console.warn(`Expected ${questionCount} questions, got ${object.length}`)
        }
        onQuestionsGenerated(object)
        toast.success(`${object.length} questions generated successfully!`)
      } else {
        console.error("Quiz generation failed - no questions generated")
        toast.error("Failed to generate questions. Please try again.")
      }
    },
  })



  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = Array.from(e.target.files || [])
    const validFiles = selectedFiles.filter(
      (file) => 
        (file.type === "application/pdf" || 
         file.type === "text/plain" ||
         file.type === "application/msword" ||
         file.type === "application/vnd.openxmlformats-officedocument.wordprocessingml.document") &&
        file.size <= 10 * 1024 * 1024 // 10MB limit
    )

    if (validFiles.length !== selectedFiles.length) {
      toast.error("Only PDF, DOC, DOCX, and TXT files under 10MB are allowed.")
    }

    setFiles(validFiles)
  }

  const encodeFileAsBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onload = () => resolve(reader.result as string)
      reader.onerror = (error) => reject(error)
    })
  }

  const handleGenerate = async () => {
    if (generationMethod === 'text' && !textContent.trim()) {
      toast.error("Please enter some text content to generate questions from.")
      return
    }

    if (generationMethod === 'file' && files.length === 0) {
      toast.error("Please upload at least one file to generate questions from.")
      return
    }

    try {
      let payload: any = {
        questionCount,
        difficulty,
        questionTypes,
        customPrompt: customPrompt.trim() || undefined
      }

      if (generationMethod === 'text') {
        payload.textContent = textContent
      } else {
        const encodedFiles = await Promise.all(
          files.map(async (file) => ({
            name: file.name,
            type: file.type,
            data: await encodeFileAsBase64(file),
          }))
        )
        payload.files = encodedFiles
      }


      submit(payload)
    } catch (error) {
      toast.error("Failed to process files. Please try again.")
    }
  }

  const toggleQuestionType = (type: string) => {
    setQuestionTypes(prev => 
      prev.includes(type) 
        ? prev.filter(t => t !== type)
        : [...prev, type]
    )
  }

  const progress = partialQuestions ? (partialQuestions.length / questionCount) * 100 : 0

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className="bg-background rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-auto"
      >
        <div className="flex items-center justify-between p-6 border-b">
          <div>
            <h2 className="text-2xl font-bold flex items-center gap-2">
              <Wand2 className="h-6 w-6 text-primary" />
              AI Quiz Generator
            </h2>
            <p className="text-muted-foreground">Generate questions automatically using AI</p>
          </div>
          <Button variant="outline" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        <div className="p-6 space-y-6">
          {/* Generation Method */}
          <div>
            <Label className="text-base font-semibold">Content Source</Label>
            <RadioGroup
              value={generationMethod}
              onValueChange={(value) => setGenerationMethod(value as 'text' | 'file')}
              className="mt-2"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="text" id="text-method" />
                <Label htmlFor="text-method">Text Input</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="file" id="file-method" />
                <Label htmlFor="file-method">File Upload</Label>
              </div>
            </RadioGroup>
          </div>

          {/* Content Input */}
          {generationMethod === 'text' ? (
            <div>
              <Label htmlFor="text-content">Text Content</Label>
              <Textarea
                id="text-content"
                value={textContent}
                onChange={(e) => setTextContent(e.target.value)}
                placeholder="Paste your content here (articles, notes, textbook chapters, etc.)"
                className="mt-1 min-h-[120px]"
              />
            </div>
          ) : (
            <div>
              <Label>Upload Files</Label>
              <div className="mt-1 border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center hover:border-muted-foreground/50 transition-colors">
                <input
                  type="file"
                  onChange={handleFileChange}
                  accept=".pdf,.doc,.docx,.txt"
                  multiple
                  className="hidden"
                  id="file-upload"
                />
                <label htmlFor="file-upload" className="cursor-pointer">
                  <Upload className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
                  <p className="text-sm text-muted-foreground">
                    Drop files here or click to browse
                  </p>
                  <p className="text-xs text-muted-foreground mt-1">
                    Supports PDF, DOC, DOCX, TXT (max 10MB each)
                  </p>
                </label>
              </div>
              {files.length > 0 && (
                <div className="mt-3 space-y-2">
                  {files.map((file, index) => (
                    <div key={index} className="flex items-center justify-between p-2 bg-muted rounded">
                      <div className="flex items-center gap-2">
                        <FileText className="h-4 w-4" />
                        <span className="text-sm">{file.name}</span>
                        <Badge variant="outline" className="text-xs">
                          {(file.size / 1024 / 1024).toFixed(1)} MB
                        </Badge>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setFiles(files.filter((_, i) => i !== index))}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Generation Settings */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <Label htmlFor="question-count">Number of Questions</Label>
              <Input
                id="question-count"
                type="number"
                min="1"
                max="20"
                value={questionCount}
                onChange={(e) => setQuestionCount(parseInt(e.target.value) || 5)}
                className="mt-1"
              />
            </div>

            <div>
              <Label>Difficulty Level</Label>
              <RadioGroup
                value={difficulty}
                onValueChange={(value) => setDifficulty(value as 'EASY' | 'MEDIUM' | 'HARD')}
                className="mt-2 flex gap-4"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="EASY" id="easy-diff" />
                  <Label htmlFor="easy-diff">Easy</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="MEDIUM" id="medium-diff" />
                  <Label htmlFor="medium-diff">Medium</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="HARD" id="hard-diff" />
                  <Label htmlFor="hard-diff">Hard</Label>
                </div>
              </RadioGroup>
            </div>
          </div>

          {/* Question Types */}
          <div>
            <Label>Question Types</Label>
            <div className="mt-2 flex flex-wrap gap-2">
              {[
                { id: 'MCQ', label: 'Multiple Choice' },
                { id: 'TRUE_FALSE', label: 'True/False' },
                { id: 'SHORT_ANSWER', label: 'Short Answer' },
                { id: 'MATCHING', label: 'Matching' }
              ].map((type) => (
                <Button
                  key={type.id}
                  variant={questionTypes.includes(type.id) ? "default" : "outline"}
                  size="sm"
                  onClick={() => toggleQuestionType(type.id)}
                >
                  {type.label}
                </Button>
              ))}
            </div>
          </div>

          {/* Custom Prompt */}
          <div>
            <Label htmlFor="custom-prompt">Custom Instructions (Optional)</Label>
            <Textarea
              id="custom-prompt"
              value={customPrompt}
              onChange={(e) => setCustomPrompt(e.target.value)}
              placeholder="Add specific instructions for question generation (e.g., 'Focus on practical applications', 'Include code examples', etc.)"
              className="mt-1"
              rows={3}
            />
          </div>

          {/* Progress */}
          {isLoading && (
            <Card>
              <CardContent className="pt-6">
                <div className="space-y-4">
                  <div className="flex justify-between text-sm">
                    <span>Generating questions...</span>
                    <span>{Math.round(progress)}%</span>
                  </div>
                  <Progress value={progress} className="h-2" />
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span>
                      {partialQuestions
                        ? `Generated ${partialQuestions.length} of ${questionCount} questions`
                        : "Analyzing content and preparing questions..."}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Error */}
          {error && (
            <Card className="border-destructive">
              <CardContent className="pt-6">
                <div className="flex items-center gap-2 text-destructive">
                  <AlertCircle className="h-4 w-4" />
                  <span>Failed to generate questions. Please try again.</span>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Actions */}
        <div className="flex justify-between items-center p-6 border-t bg-muted/50">
          <div className="text-sm text-muted-foreground">
            {questionTypes.length > 0 && (
              <span>Selected types: {questionTypes.join(', ')}</span>
            )}
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button 
              onClick={handleGenerate} 
              disabled={isLoading || (generationMethod === 'text' && !textContent.trim()) || (generationMethod === 'file' && files.length === 0)}
            >
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <Wand2 className="h-4 w-4 mr-2" />
                  Generate Questions
                </>
              )}
            </Button>
          </div>
        </div>
      </motion.div>
    </div>
  )
}
