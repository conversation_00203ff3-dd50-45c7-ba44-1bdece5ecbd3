import { generateText, generateObject, tool } from 'ai'
import { z } from 'zod'
import { getModelInstance, getBestModelForTask } from '@/lib/ai-providers'

// Schemas for structured outputs
const quizAnalysisSchema = z.object({
  topic: z.string(),
  difficulty: z.enum(['EASY', 'MEDIUM', 'HARD']),
  suggestedQuestionCount: z.number().min(5).max(50),
  learningObjectives: z.array(z.string()),
  keyTopics: z.array(z.string()),
  targetAudience: z.string(),
  estimatedDuration: z.number(),
  tags: z.array(z.string()),
  prerequisites: z.array(z.string()),
  suggestedSubject: z.string().optional(),
  suggestedChapter: z.string().optional(),
  suggestedTopic: z.string().optional()
})

const questionSchema = z.object({
  id: z.string(),
  type: z.enum(['MCQ', 'TRUE_FALSE', 'SHORT_ANSWER', 'MATCHING']),
  text: z.string(),
  options: z.array(z.string()).optional(),
  correctAnswer: z.string(),
  explanation: z.string(),
  difficulty: z.enum(['EASY', 'MEDIUM', 'HARD']),
  tags: z.array(z.string()),
  points: z.number().min(1).max(10),
  estimatedTime: z.number(), // in seconds
  bloomsLevel: z.enum(['remember', 'understand', 'apply', 'analyze', 'evaluate', 'create'])
})

const quizStructureSchema = z.object({
  title: z.string(),
  description: z.string(),
  instructions: z.string(),
  questions: z.array(questionSchema),
  metadata: z.object({
    totalPoints: z.number(),
    estimatedDuration: z.number(),
    tags: z.array(z.string()).optional(),
    difficultyDistribution: z.object({
      easy: z.number(),
      medium: z.number(),
      hard: z.number()
    }),
    bloomsDistribution: z.object({
      remember: z.number(),
      understand: z.number(),
      apply: z.number(),
      analyze: z.number(),
      evaluate: z.number(),
      create: z.number()
    })
  })
})

export class QuizCreationAgent {
  private orchestratorModel: any
  private contentAnalyzer: any
  private questionGenerator: any
  private qualityEvaluator: any

  constructor(options?: {
    orchestratorModel?: string
    contentAnalyzer?: string
    questionGenerator?: string
    qualityEvaluator?: string
    useOpenAIOnly?: boolean
  }) {
    try {
      // If useOpenAIOnly is true or no specific models provided, use OpenAI configuration
      if (options?.useOpenAIOnly !== false) {
        const { getOpenAIConfiguration } = require('@/lib/ai-providers')
        const openAIConfig = getOpenAIConfiguration()

        this.orchestratorModel = getModelInstance(
          options?.orchestratorModel || openAIConfig.orchestrator.id
        )

        this.contentAnalyzer = getModelInstance(
          options?.contentAnalyzer || openAIConfig.contentAnalyzer.id
        )

        this.questionGenerator = getModelInstance(
          options?.questionGenerator || openAIConfig.questionGenerator.id
        )

        this.qualityEvaluator = getModelInstance(
          options?.qualityEvaluator || openAIConfig.qualityEvaluator.id
        )
      } else {
        // Fallback to mixed providers
        this.orchestratorModel = getModelInstance(
          options?.orchestratorModel ||
          getBestModelForTask({ useCase: 'reasoning', prioritizeQuality: true }).id
        )

        this.contentAnalyzer = getModelInstance(
          options?.contentAnalyzer ||
          getBestModelForTask({ useCase: 'analysis', prioritizeSpeed: true }).id
        )

        this.questionGenerator = getModelInstance(
          options?.questionGenerator ||
          getBestModelForTask({ useCase: 'content-generation', prioritizeQuality: true }).id
        )

        this.qualityEvaluator = getModelInstance(
          options?.qualityEvaluator ||
          getBestModelForTask({ useCase: 'analysis', prioritizeQuality: true }).id
        )
      }
    } catch (error) {
      console.error('Error initializing AI models:', error)
      throw new Error('Failed to initialize AI models. Please ensure OpenAI API key is configured.')
    }
  }

  // Main orchestrator method
  async createQuiz(input: {
    content?: string
    files?: Array<{ name: string; content: string; type: string }>
    requirements?: {
      questionCount?: number
      difficulty?: 'EASY' | 'MEDIUM' | 'HARD'
      questionTypes?: string[]
      timeLimit?: number
      targetAudience?: string
      learningObjectives?: string[]
      subject?: string
      chapter?: string
      topic?: string
      examPattern?: string
      testType?: 'QUIZ' | 'WEEKLY_TEST' | 'MONTHLY_TEST' | 'TEST_SERIES' | 'DAILY_PRACTICE'
      syllabusCoverage?: 'CURRENT_WEEK' | 'CURRENT_MONTH' | 'FULL_SYLLABUS' | 'SPECIFIC_TOPICS'
      printFormat?: boolean
      includeAnswerKey?: boolean
      includeInstructions?: boolean
      institutionName?: string
      testDate?: string
      testDuration?: number
      language?: 'ENGLISH' | 'HINDI' | 'BILINGUAL'
    }
    preferences?: {
      prioritizeQuality?: boolean
      prioritizeSpeed?: boolean
      includeExplanations?: boolean
      generateTags?: boolean
    }
  }) {
    try {
      // Step 1: Analyze content and requirements
      const analysis = await this.analyzeContent(input)
      
      // Step 2: Create quiz structure plan
      const structure = await this.planQuizStructure(analysis, input.requirements)
      
      // Step 3: Generate questions in parallel batches
      const questions = await this.generateQuestions(structure, input.content || '', input.files, input.requirements)
      
      // Step 4: Evaluate and improve quality
      const evaluatedQuestions = await this.evaluateAndImproveQuestions(questions)
      
      // Step 5: Finalize quiz structure
      const finalQuiz = await this.finalizeQuiz(structure, evaluatedQuestions, input.requirements)

      return {
        success: true,
        quiz: finalQuiz,
        metadata: {
          analysisUsed: analysis,
          questionsGenerated: evaluatedQuestions.length,
          qualityScore: await this.calculateOverallQuality(evaluatedQuestions),
          processingTime: Date.now()
        }
      }
    } catch (error) {
      console.error('Quiz creation failed:', error)

      // Try to provide a minimal fallback quiz if possible
      if (input.content || (input.files && input.files.length > 0)) {
        try {
          const fallbackQuiz = {
            title: 'Quiz Creation Failed',
            description: 'There was an error creating your quiz. Please try again.',
            instructions: 'This is a fallback quiz due to an error in generation.',
            questions: [],
            metadata: {
              totalPoints: 0,
              estimatedDuration: 0,
              difficultyDistribution: { easy: 0, medium: 0, hard: 0 },
              bloomsDistribution: { remember: 0, understand: 0, apply: 0, analyze: 0, evaluate: 0, create: 0 }
            }
          }

          return {
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error occurred',
            quiz: fallbackQuiz,
            metadata: {
              analysisUsed: null,
              questionsGenerated: 0,
              qualityScore: 0,
              processingTime: Date.now()
            }
          }
        } catch (fallbackError) {
          console.error('Fallback quiz creation also failed:', fallbackError)
        }
      }

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        quiz: null
      }
    }
  }

  // Public method for content analysis
  async analyzeContentPublic(input: any) {
    return this.analyzeContent(input)
  }

  private async analyzeContent(input: any) {
    const contentToAnalyze = [
      input.content || '',
      ...(input.files?.map((f: any) => f.content) || [])
    ].join('\n\n')

    const { object: analysis } = await generateObject({
      model: this.contentAnalyzer,
      schema: quizAnalysisSchema,
      system: `You are an expert educational content analyzer. Analyze the provided content and determine the best approach for creating a quiz.`,
      prompt: `Analyze this content for quiz creation:

${contentToAnalyze}

Additional requirements: ${JSON.stringify(input.requirements || {})}

Provide a comprehensive analysis including:
1. Main topic and subtopics
2. Appropriate difficulty level
3. Suggested question count
4. Learning objectives
5. Key topics to cover
6. Target audience
7. Suggested subject (broad academic area like "Mathematics", "Biology", "History")
8. Suggested chapter (specific area within the subject like "Algebra", "Cell Biology", "World War II")
9. Suggested topic (specific focus like "Linear Equations", "Mitosis", "Causes of WWI")
7. Estimated duration
8. Relevant tags
9. Prerequisites needed`
    })

    return analysis
  }

  private async planQuizStructure(analysis: any, requirements?: any) {
    const { text: structurePlan } = await generateText({
      model: this.orchestratorModel,
      system: `You are an expert quiz designer. Create a detailed structure plan for a quiz based on the content analysis and requirements.`,
      prompt: `Based on this analysis: ${JSON.stringify(analysis)}
      
And these requirements: ${JSON.stringify(requirements || {})}

Create a detailed quiz structure plan including:
1. Quiz title and description
2. Question distribution by type and difficulty
3. Learning progression flow
4. Time allocation per section
5. Assessment strategy
6. Instructions for students`
    })

    return structurePlan
  }

  private async generateQuestions(
    structure: string,
    content: string,
    files?: any[],
    requirements?: any
  ) {
    // Use parallel processing for question generation
    const questionBatches = await this.createQuestionBatches(structure, requirements)
    
    const questionPromises = questionBatches.map(async (batch) => {
      return await generateObject({
        model: this.questionGenerator,
        schema: z.object({ questions: z.array(questionSchema) }),
        system: `You are an expert question writer. Create high-quality educational questions that test understanding, not just memorization.

${requirements?.examPattern ? this.getExamPatternInstructions(requirements.examPattern) : ''}
${requirements?.testType ? this.getTestTypeInstructions(requirements.testType, requirements.syllabusCoverage) : ''}
${requirements?.language ? this.getLanguageInstructions(requirements.language) : ''}`,
        prompt: `Generate ${batch.count} questions of type ${batch.type} with difficulty ${batch.difficulty}.

Content: ${content}
Files: ${JSON.stringify(files?.map(f => ({ name: f.name, type: f.type })) || [])}

Subject: ${requirements?.subject || 'General'}
Chapter: ${requirements?.chapter || 'General'}
Topic: ${requirements?.topic || 'General'}
${requirements?.examPattern ? `Exam Pattern: ${requirements.examPattern} - Follow the specific question format and style used in this Indian examination` : ''}

Requirements:
- Focus on ${batch.topics.join(', ')}
- Use Bloom's taxonomy level: ${batch.bloomsLevel}
- Include clear explanations
- Ensure questions are unambiguous
- Test practical understanding

Structure: ${structure}`
      })
    })

    const results = await Promise.all(questionPromises)
    return results.flatMap(result => result.object.questions)
  }

  private async createQuestionBatches(_structure: string, requirements?: any) {
    const questionCount = requirements?.questionCount || 10
    const questionTypes = requirements?.questionTypes || ['MCQ']
    const difficulty = requirements?.difficulty || 'MEDIUM'

    // Distribute questions across selected types
    const batches: any[] = []
    const questionsPerType = Math.floor(questionCount / questionTypes.length)
    const remainder = questionCount % questionTypes.length

    questionTypes.forEach((type: string, index: number) => {
      const count = questionsPerType + (index < remainder ? 1 : 0)
      if (count > 0) {
        batches.push({
          count,
          type,
          difficulty,
          topics: [requirements?.topic || requirements?.subject || 'general'],
          bloomsLevel: 'understand'
        })
      }
    })

    return batches
  }

  private async evaluateAndImproveQuestions(questions: any[]) {
    const evaluationPromises = questions.map(async (question) => {
      const { object: evaluation } = await generateObject({
        model: this.qualityEvaluator,
        schema: z.object({
          qualityScore: z.number().min(0).max(100),
          issues: z.array(z.string()),
          improvements: z.array(z.string()),
          isAcceptable: z.boolean(),
          improvedQuestion: questionSchema.optional()
        }),
        system: `You are an expert in educational assessment quality. Evaluate questions for clarity, accuracy, and educational value.`,
        prompt: `Evaluate this question:
        
${JSON.stringify(question, null, 2)}

Check for:
1. Clarity and unambiguous language
2. Appropriate difficulty level
3. Educational value
4. Correct answer accuracy
5. Quality of distractors (for MCQ)
6. Explanation quality

If the question needs improvement, provide an improved version.`
      })

      return evaluation.isAcceptable
        ? question
        : evaluation.improvedQuestion || question
    })

    return await Promise.all(evaluationPromises)
  }

  private async finalizeQuiz(structure: string, questions: any[], requirements?: any) {
    try {
      const { object: finalQuiz } = await generateObject({
        model: this.orchestratorModel,
        schema: quizStructureSchema,
        system: `You are finalizing a quiz. Create the complete quiz structure with metadata.`,
        prompt: `Finalize this quiz:

Structure Plan: ${structure}
Questions: ${JSON.stringify(questions, null, 2)}
Subject: ${requirements?.subject || 'General'}
Chapter: ${requirements?.chapter || 'General'}
Topic: ${requirements?.topic || 'General'}
${requirements?.examPattern ? `Exam Pattern: ${requirements.examPattern} - Follow the specific question format and style used in this Indian examination` : ''}
${requirements?.testType ? `Test Type: ${requirements.testType}` : ''}
${requirements?.syllabusCoverage ? `Syllabus Coverage: ${requirements.syllabusCoverage}` : ''}
${requirements?.institutionName ? `Institution: ${requirements.institutionName}` : ''}
${requirements?.testDate ? `Test Date: ${requirements.testDate}` : ''}

Create a complete quiz with:
1. ${this.getTitleFormat(requirements?.testType, requirements?.institutionName)}
2. ${requirements?.includeInstructions ? 'Detailed instructions for students including exam rules and guidelines' : 'Basic instructions'}
3. Properly ordered questions with ${requirements?.printFormat ? 'print-friendly formatting' : 'digital formatting'}
4. Complete metadata including points, duration, and distributions
5. Generate relevant tags based on the content, subject, and topic
${requirements?.printFormat ? '6. Format suitable for printing with proper spacing and layout' : ''}
${requirements?.includeAnswerKey ? '7. Include comprehensive answer key with explanations' : ''}`
      })

      // Validate the generated quiz
      if (!finalQuiz.questions || !Array.isArray(finalQuiz.questions)) {
        throw new Error('Generated quiz has invalid questions array')
      }

      return finalQuiz
    } catch (error) {
      console.error('Error finalizing quiz:', error)

      // Fallback: create a basic quiz structure
      const totalPoints = questions.reduce((sum, q) => sum + (q.points || 1), 0)
      const estimatedDuration = questions.length * 2 // 2 minutes per question

      return {
        title: 'AI Generated Quiz',
        description: 'A quiz created by AI based on your content.',
        instructions: 'Read each question carefully and select the best answer.',
        questions: questions.map((q, index) => ({
          id: q.id || `q-${index + 1}`,
          type: q.type || 'MCQ',
          text: q.text || `Question ${index + 1}`,
          options: Array.isArray(q.options) ? q.options : [],
          correctAnswer: q.correctAnswer || '',
          explanation: q.explanation || '',
          difficulty: q.difficulty || 'MEDIUM',
          tags: Array.isArray(q.tags) ? q.tags : [],
          points: q.points || 1,
          estimatedTime: q.estimatedTime || 120,
          bloomsLevel: q.bloomsLevel || 'understand'
        })),
        metadata: {
          totalPoints,
          estimatedDuration,
          difficultyDistribution: {
            easy: questions.filter(q => q.difficulty === 'EASY').length,
            medium: questions.filter(q => q.difficulty === 'MEDIUM').length,
            hard: questions.filter(q => q.difficulty === 'HARD').length
          },
          bloomsDistribution: {
            remember: questions.filter(q => q.bloomsLevel === 'remember').length,
            understand: questions.filter(q => q.bloomsLevel === 'understand').length,
            apply: questions.filter(q => q.bloomsLevel === 'apply').length,
            analyze: questions.filter(q => q.bloomsLevel === 'analyze').length,
            evaluate: questions.filter(q => q.bloomsLevel === 'evaluate').length,
            create: questions.filter(q => q.bloomsLevel === 'create').length
          }
        }
      }
    }
  }

  private async calculateOverallQuality(questions: any[]): Promise<number> {
    // Simple quality calculation - could be more sophisticated
    const scores = await Promise.all(
      questions.map(async (q) => {
        const { object } = await generateObject({
          model: this.qualityEvaluator,
          schema: z.object({ score: z.number().min(0).max(100) }),
          prompt: `Rate this question's quality (0-100): ${JSON.stringify(q)}`
        })
        return object.score
      })
    )

    return scores.reduce((sum, score) => sum + score, 0) / scores.length
  }

  private getExamPatternInstructions(examPattern: string): string {
    const patterns: Record<string, string> = {
      'JEE_MAIN': `
        Follow JEE Main pattern:
        - Single correct answer MCQs with 4 options
        - Focus on Physics, Chemistry, Mathematics
        - Emphasize conceptual understanding and application
        - Include numerical problems and theoretical concepts
        - Maintain high difficulty with precise calculations
      `,
      'JEE_ADVANCED': `
        Follow JEE Advanced pattern:
        - Multiple correct answers, matching, and numerical questions
        - Very high difficulty level
        - Multi-concept integration questions
        - Focus on deep understanding and problem-solving
        - Include complex numerical and theoretical problems
      `,
      'NEET': `
        Follow NEET pattern:
        - Single correct answer MCQs with 4 options
        - Focus on Biology, Physics, Chemistry
        - Emphasize factual knowledge and application
        - Include diagrams and biological processes
        - Balance between memorization and understanding
      `,
      'UPSC_PRELIMS': `
        Follow UPSC Prelims pattern:
        - Current affairs and general studies focus
        - Factual and analytical questions
        - Indian polity, history, geography emphasis
        - Recent developments and government schemes
        - Clear, unambiguous options
      `,
      'SSC_CGL': `
        Follow SSC CGL pattern:
        - Quick-solve questions with time constraints
        - General awareness, quantitative aptitude
        - English comprehension and reasoning
        - Government job-oriented content
        - Straightforward questions with clear answers
      `,
      'GATE': `
        Follow GATE pattern:
        - Engineering mathematics and technical subjects
        - Numerical answer type questions
        - High technical depth
        - Problem-solving and analytical questions
        - Industry-relevant technical content
      `,
      'CAT': `
        Follow CAT pattern:
        - Verbal ability and reading comprehension
        - Data interpretation and logical reasoning
        - Quantitative ability with shortcuts
        - Business and management context
        - Time-efficient problem-solving approach
      `,
      'CLAT': `
        Follow CLAT pattern:
        - Legal reasoning and current affairs
        - English language and comprehension
        - Logical reasoning and mathematics
        - Legal awareness and general knowledge
        - Law school entrance focus
      `,
      'CBSE_BOARD': `
        Follow CBSE Board pattern:
        - NCERT syllabus alignment
        - Mix of objective and subjective questions
        - Application-based problems
        - Real-life examples and case studies
        - Balanced difficulty progression
      `,
      'ICSE_BOARD': `
        Follow ICSE Board pattern:
        - Comprehensive and detailed questions
        - Application and analysis focus
        - Literature and language emphasis
        - Practical and theoretical balance
        - Higher-order thinking skills
      `,
      'AIIMS_NURSING': `
        Follow AIIMS Nursing Entrance pattern:
        - Focus on Physics, Chemistry, Biology fundamentals
        - Medical and healthcare applications
        - Anatomy, physiology, and basic nursing concepts
        - Current healthcare developments
        - Precise scientific terminology
      `,
      'PGIMER_NURSING': `
        Follow PGIMER Nursing pattern:
        - Advanced nursing concepts and practices
        - Medical-surgical nursing focus
        - Patient care and safety protocols
        - Healthcare ethics and communication
        - Evidence-based nursing practice
      `,
      'CMC_LUDHIANA': `
        Follow CMC Ludhiana pattern:
        - Christian healthcare values integration
        - Community health and preventive care
        - Holistic patient care approach
        - Medical missions and service orientation
        - Interdisciplinary healthcare teamwork
      `,
      'MILITARY_NURSING': `
        Follow Military Nursing (Army MNS) pattern:
        - Military healthcare protocols
        - Emergency and trauma care
        - Field nursing and combat medicine
        - Military discipline and ethics
        - Leadership in healthcare settings
      `,
      'BHU_NURSING': `
        Follow BHU Nursing Entrance pattern:
        - Traditional and modern nursing integration
        - Ayurvedic and allopathic healthcare
        - Cultural competency in nursing
        - Research-based nursing practice
        - Indian healthcare system knowledge
      `,
      'KGMU_NURSING': `
        Follow KGMU Nursing pattern:
        - Government healthcare system focus
        - Public health nursing emphasis
        - Rural and urban healthcare challenges
        - Healthcare policy and administration
        - Community-based nursing interventions
      `,
      'RUHS_NURSING': `
        Follow RUHS Nursing pattern:
        - Rajasthan state healthcare focus
        - Desert healthcare challenges
        - Maternal and child health emphasis
        - Communicable disease prevention
        - State health program implementation
      `,
      'JENPAS_UG': `
        Follow JENPAS (UG) pattern:
        - Undergraduate nursing foundation
        - Basic nursing principles and practices
        - Fundamental patient care skills
        - Healthcare communication basics
        - Professional nursing ethics
      `,
      'JEPBN_POST_BASIC': `
        Follow JEPBN (Post Basic) pattern:
        - Advanced nursing specializations
        - Leadership and management in nursing
        - Quality improvement in healthcare
        - Advanced clinical decision making
        - Nursing research and evidence-based practice
      `,
      'ANM_GNM_DIPLOMA': `
        Follow ANM & GNM (State Diploma) pattern:
        - Diploma-level nursing competencies
        - Primary healthcare delivery
        - Maternal and child health focus
        - Community health worker skills
        - Basic medical and surgical nursing
      `,
      'GNM_ELIGIBILITY': `
        Follow GNM Eligibility (States) pattern:
        - State-specific healthcare requirements
        - General nursing and midwifery basics
        - Local health challenges and solutions
        - Cultural sensitivity in healthcare
        - Basic nursing procedures and protocols
      `,
      'RRB_STAFF_NURSE': `
        Follow RRB Staff Nurse Recruitment pattern:
        - Railway healthcare system focus
        - Occupational health and safety
        - Emergency medical services
        - Government recruitment standards
        - Public sector healthcare delivery
      `,
      'AIIMS_NORCET': `
        Follow AIIMS NORCET pattern:
        - Nursing officer competencies
        - Advanced clinical nursing skills
        - Healthcare leadership and management
        - Quality assurance in nursing
        - Professional development in nursing
      `,
      'CHO_RECRUITMENT': `
        Follow CHO Recruitment pattern:
        - Community health officer responsibilities
        - Primary healthcare management
        - Health promotion and disease prevention
        - Community engagement and education
        - Public health program implementation
      `
    }

    return patterns[examPattern] || ''
  }

  private getTestTypeInstructions(testType: string, syllabusCoverage?: string): string {
    const testTypeInstructions: Record<string, string> = {
      'DAILY_PRACTICE': `
        Daily Practice Format:
        - Quick revision questions for daily learning
        - Focus on fundamental concepts and basic application
        - Mix of easy to medium difficulty questions
        - Encourage regular practice and concept reinforcement
        - Include variety to maintain student engagement
      `,
      'WEEKLY_TEST': `
        Weekly Test Format:
        - Comprehensive coverage of week's taught material
        - Progressive difficulty from basic to advanced
        - Include both conceptual and application-based questions
        - Test understanding of recently covered topics
        - Prepare students for monthly assessments
        - Format suitable for classroom evaluation
      `,
      'MONTHLY_TEST': `
        Monthly Test Format:
        - Comprehensive assessment of month's curriculum
        - Include questions from all weekly topics covered
        - Mix of difficulty levels with emphasis on application
        - Test retention and integration of concepts
        - Include some challenging questions for differentiation
        - Professional test format suitable for formal evaluation
      `,
      'TEST_SERIES': `
        Test Series Format:
        - Full syllabus coverage with exam-like difficulty
        - Simulate actual exam conditions and question patterns
        - Include high-difficulty analytical and application questions
        - Test comprehensive understanding and exam readiness
        - Include time-bound challenging scenarios
        - Professional format matching target exam standards
      `,
      'QUIZ': `
        Quiz Format:
        - Engaging and interactive question format
        - Focus on key concepts and quick assessment
        - Balanced difficulty for general evaluation
        - Suitable for both practice and assessment
      `
    }

    const coverageInstructions: Record<string, string> = {
      'CURRENT_WEEK': `
        Focus on topics taught in the current week only.
        Ensure questions directly relate to recently covered material.
        Test immediate understanding and retention.
      `,
      'CURRENT_MONTH': `
        Cover all topics taught in the current month.
        Include questions that integrate concepts from different weeks.
        Test cumulative learning and concept connections.
      `,
      'FULL_SYLLABUS': `
        Cover the complete syllabus comprehensively.
        Include questions from all major topics and chapters.
        Test overall preparation and readiness.
        Maintain balanced representation of all subjects/topics.
      `,
      'SPECIFIC_TOPICS': `
        Focus on the specified topics and subjects.
        Ensure deep coverage of mentioned areas.
        Test thorough understanding of selected content.
      `
    }

    let instructions = testTypeInstructions[testType] || ''
    if (syllabusCoverage) {
      instructions += '\n' + (coverageInstructions[syllabusCoverage] || '')
    }

    return instructions
  }

  private getTitleFormat(testType?: string, institutionName?: string): string {
    const formats: Record<string, string> = {
      'DAILY_PRACTICE': 'Create an engaging title for daily practice session',
      'WEEKLY_TEST': `Create a formal title for weekly test${institutionName ? ` for ${institutionName}` : ''} with proper academic formatting`,
      'MONTHLY_TEST': `Create a comprehensive title for monthly examination${institutionName ? ` - ${institutionName}` : ''} with formal structure`,
      'TEST_SERIES': `Create a professional title for test series${institutionName ? ` conducted by ${institutionName}` : ''} matching exam standards`,
      'QUIZ': 'Create an engaging title and description based on the subject/chapter/topic'
    }

    return formats[testType || 'QUIZ'] || formats['QUIZ']
  }

  private getLanguageInstructions(language: string): string {
    const languageInstructions: Record<string, string> = {
      'ENGLISH': `
        Language Instructions:
        - Generate all questions, options, and explanations in English only
        - Use clear, simple English appropriate for the target audience
        - Ensure proper grammar and spelling
        - Use standard English terminology
      `,
      'HINDI': `
        Language Instructions:
        - Generate all questions, options, and explanations in Hindi only (हिंदी में)
        - Use clear, simple Hindi appropriate for the target audience
        - Ensure proper Devanagari script and grammar
        - Use standard Hindi terminology for academic subjects
        - Include English terms in parentheses for technical/scientific terms when necessary
      `,
      'BILINGUAL': `
        Bilingual Language Instructions:
        - Generate questions in both English and Hindi for better comprehension
        - Format: Present the question first in English, then in Hindi
        - For options: Provide both English and Hindi versions
        - Structure: "Question in English / प्रश्न हिंदी में"
        - Options: "A) Option in English / विकल्प हिंदी में"
        - Explanations: Provide in both languages for clarity
        - Use proper formatting to distinguish between languages
        - Ensure both versions convey the same meaning accurately
      `
    }

    return languageInstructions[language] || ''
  }
}
