"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  Send, 
  Users, 
  UserCheck, 
  Bell, 
  Calendar,
  Image,
  Link,
  AlertTriangle,
  Info,
  CheckCircle,
  XCircle,
  Loader2
} from "lucide-react"
import { toast } from "@/lib/toast-utils"

interface NotificationForm {
  type: string
  title: string
  message: string
  actionUrl: string
  imageUrl: string
  priority: string
  category: string
  expiresAt: string
  scheduledAt: string
  recipients: string[]
  sendToAll: boolean
  sendToRole: string
}

export function NotificationManager() {
  const [form, setForm] = useState<NotificationForm>({
    type: 'ANNOUNCEMENT',
    title: '',
    message: '',
    actionUrl: '',
    imageUrl: '',
    priority: 'normal',
    category: '',
    expiresAt: '',
    scheduledAt: '',
    recipients: [],
    sendToAll: false,
    sendToRole: ''
  })
  const [isLoading, setIsLoading] = useState(false)
  const [recipientInput, setRecipientInput] = useState('')

  const notificationTypes = [
    { value: 'ANNOUNCEMENT', label: 'Announcement', icon: <Bell className="h-4 w-4" /> },
    { value: 'QUIZ_AVAILABLE', label: 'Quiz Available', icon: <CheckCircle className="h-4 w-4" /> },
    { value: 'QUIZ_REMINDER', label: 'Quiz Reminder', icon: <Calendar className="h-4 w-4" /> },
    { value: 'SYSTEM_UPDATE', label: 'System Update', icon: <Info className="h-4 w-4" /> },
    { value: 'MAINTENANCE', label: 'Maintenance', icon: <AlertTriangle className="h-4 w-4" /> },
    { value: 'WELCOME', label: 'Welcome', icon: <Users className="h-4 w-4" /> }
  ]

  const priorities = [
    { value: 'low', label: 'Low', color: 'bg-gray-500' },
    { value: 'normal', label: 'Normal', color: 'bg-blue-500' },
    { value: 'high', label: 'High', color: 'bg-orange-500' },
    { value: 'urgent', label: 'Urgent', color: 'bg-red-500' }
  ]

  const handleInputChange = (field: keyof NotificationForm, value: any) => {
    setForm(prev => ({ ...prev, [field]: value }))
  }

  const addRecipient = () => {
    if (recipientInput.trim() && !form.recipients.includes(recipientInput.trim())) {
      setForm(prev => ({
        ...prev,
        recipients: [...prev.recipients, recipientInput.trim()]
      }))
      setRecipientInput('')
    }
  }

  const removeRecipient = (recipient: string) => {
    setForm(prev => ({
      ...prev,
      recipients: prev.recipients.filter(r => r !== recipient)
    }))
  }

  const sendNotification = async () => {
    if (!form.title.trim() || !form.message.trim()) {
      toast.error('Please fill in title and message')
      return
    }

    setIsLoading(true)
    try {
      const payload = {
        type: form.type,
        title: form.title,
        message: form.message,
        actionUrl: form.actionUrl || undefined,
        imageUrl: form.imageUrl || undefined,
        priority: form.priority,
        category: form.category || undefined,
        expiresAt: form.expiresAt || undefined,
        scheduledAt: form.scheduledAt || undefined,
        sendToAll: form.sendToAll,
        sendToRole: form.sendToRole || undefined,
        recipients: form.recipients.length > 0 ? form.recipients.map(email => ({ userId: email })) : undefined
      }

      const response = await fetch('/api/notifications', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      })

      if (response.ok) {
        const result = await response.json()
        toast.success('Notification sent successfully!')
        
        // Reset form
        setForm({
          type: 'ANNOUNCEMENT',
          title: '',
          message: '',
          actionUrl: '',
          imageUrl: '',
          priority: 'normal',
          category: '',
          expiresAt: '',
          scheduledAt: '',
          recipients: [],
          sendToAll: false,
          sendToRole: ''
        })
      } else {
        const error = await response.json()
        toast.error(error.error || 'Failed to send notification')
      }
    } catch (error) {
      console.error('Error sending notification:', error)
      toast.error('Failed to send notification')
    } finally {
      setIsLoading(false)
    }
  }

  const getRecipientSummary = () => {
    if (form.sendToAll) return 'All Users'
    if (form.sendToRole) return `All ${form.sendToRole}s`
    if (form.recipients.length > 0) return `${form.recipients.length} specific user(s)`
    return 'No recipients selected'
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Send Notification
          </CardTitle>
          <CardDescription>
            Send notifications to users across the platform
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="compose" className="space-y-6">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="compose">Compose</TabsTrigger>
              <TabsTrigger value="recipients">Recipients</TabsTrigger>
              <TabsTrigger value="settings">Settings</TabsTrigger>
            </TabsList>

            {/* Compose Tab */}
            <TabsContent value="compose" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">Type</label>
                  <Select value={form.type} onValueChange={(value) => handleInputChange('type', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {notificationTypes.map(type => (
                        <SelectItem key={type.value} value={type.value}>
                          <div className="flex items-center gap-2">
                            {type.icon}
                            {type.label}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">Priority</label>
                  <Select value={form.priority} onValueChange={(value) => handleInputChange('priority', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {priorities.map(priority => (
                        <SelectItem key={priority.value} value={priority.value}>
                          <div className="flex items-center gap-2">
                            <div className={`w-3 h-3 rounded-full ${priority.color}`} />
                            {priority.label}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">Title</label>
                <Input
                  value={form.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  placeholder="Notification title"
                  maxLength={100}
                />
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">Message</label>
                <Textarea
                  value={form.message}
                  onChange={(e) => handleInputChange('message', e.target.value)}
                  placeholder="Notification message"
                  rows={4}
                  maxLength={500}
                />
                <div className="text-xs text-muted-foreground mt-1">
                  {form.message.length}/500 characters
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">Action URL (Optional)</label>
                  <div className="relative">
                    <Link className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      value={form.actionUrl}
                      onChange={(e) => handleInputChange('actionUrl', e.target.value)}
                      placeholder="/student/quiz/123"
                      className="pl-10"
                    />
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">Image URL (Optional)</label>
                  <div className="relative">
                    <Image className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      value={form.imageUrl}
                      onChange={(e) => handleInputChange('imageUrl', e.target.value)}
                      placeholder="https://example.com/image.jpg"
                      className="pl-10"
                    />
                  </div>
                </div>
              </div>
            </TabsContent>

            {/* Recipients Tab */}
            <TabsContent value="recipients" className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="sendToAll"
                    checked={form.sendToAll}
                    onChange={(e) => {
                      handleInputChange('sendToAll', e.target.checked)
                      if (e.target.checked) {
                        handleInputChange('sendToRole', '')
                        handleInputChange('recipients', [])
                      }
                    }}
                    className="rounded"
                  />
                  <label htmlFor="sendToAll" className="text-sm font-medium">
                    Send to all users
                  </label>
                </div>

                {!form.sendToAll && (
                  <>
                    <div>
                      <label className="text-sm font-medium mb-2 block">Send to Role</label>
                      <Select
                        value={form.sendToRole || "none"}
                        onValueChange={(value) => {
                          const roleValue = value === "none" ? "" : value
                          handleInputChange('sendToRole', roleValue)
                          if (roleValue) handleInputChange('recipients', [])
                        }}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select role (optional)" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="none">None</SelectItem>
                          <SelectItem value="ADMIN">Admins</SelectItem>
                          <SelectItem value="STUDENT">Students</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {!form.sendToRole && (
                      <div>
                        <label className="text-sm font-medium mb-2 block">Specific Recipients</label>
                        <div className="flex gap-2">
                          <Input
                            value={recipientInput}
                            onChange={(e) => setRecipientInput(e.target.value)}
                            placeholder="Enter user email or ID"
                            onKeyPress={(e) => e.key === 'Enter' && addRecipient()}
                          />
                          <Button onClick={addRecipient} variant="outline">
                            Add
                          </Button>
                        </div>
                        
                        {form.recipients.length > 0 && (
                          <div className="mt-3 space-y-2">
                            <div className="text-sm font-medium">Recipients:</div>
                            <div className="flex flex-wrap gap-2">
                              {form.recipients.map(recipient => (
                                <Badge key={recipient} variant="secondary" className="flex items-center gap-1">
                                  {recipient}
                                  <button
                                    onClick={() => removeRecipient(recipient)}
                                    className="ml-1 hover:text-red-500"
                                  >
                                    <XCircle className="h-3 w-3" />
                                  </button>
                                </Badge>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </>
                )}

                <div className="p-3 bg-muted rounded-lg">
                  <div className="text-sm font-medium">Recipients Summary:</div>
                  <div className="text-sm text-muted-foreground">{getRecipientSummary()}</div>
                </div>
              </div>
            </TabsContent>

            {/* Settings Tab */}
            <TabsContent value="settings" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">Category (Optional)</label>
                  <Input
                    value={form.category}
                    onChange={(e) => handleInputChange('category', e.target.value)}
                    placeholder="e.g., quiz, announcement, system"
                  />
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">Expires At (Optional)</label>
                  <Input
                    type="datetime-local"
                    value={form.expiresAt}
                    onChange={(e) => handleInputChange('expiresAt', e.target.value)}
                  />
                </div>
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">Schedule For Later (Optional)</label>
                <Input
                  type="datetime-local"
                  value={form.scheduledAt}
                  onChange={(e) => handleInputChange('scheduledAt', e.target.value)}
                />
                <div className="text-xs text-muted-foreground mt-1">
                  Leave empty to send immediately
                </div>
              </div>
            </TabsContent>
          </Tabs>

          <div className="flex justify-end pt-6 border-t">
            <Button onClick={sendNotification} disabled={isLoading} className="min-w-32">
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Sending...
                </>
              ) : (
                <>
                  <Send className="h-4 w-4 mr-2" />
                  Send Notification
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
