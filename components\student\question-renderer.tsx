"use client"

import { useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { useLiveRegion } from "@/components/accessibility/live-region"
import { Image, HelpCircle } from "lucide-react"
import { motion } from "framer-motion"

interface Question {
  id: string
  type: 'MCQ' | 'TRUE_FALSE' | 'FILL_BLANK' | 'ESSAY' | 'MATCHING' | 'SHORT_ANSWER'
  text: string
  options?: string[]
  correctAnswer?: string
  explanation?: string
  points: number
  difficulty?: 'EASY' | 'MEDIUM' | 'HARD'
  tags?: string[]
  imageUrl?: string
}

interface QuestionRendererProps {
  question: Question
  answer: any
  onAnswerChange: (answer: any) => void
  disabled?: boolean
  showExplanation?: boolean
  showCorrectAnswer?: boolean
}

export function QuestionRenderer({
  question,
  answer,
  onAnswerChange,
  disabled = false,
  showExplanation = false,
  showCorrectAnswer = false
}: QuestionRendererProps) {
  const [showHint, setShowHint] = useState(false)
  const { announce } = useLiveRegion()

  const renderMCQ = () => (
    <div className="space-y-4">
      <RadioGroup
        value={answer || ""}
        onValueChange={(value) => {
          onAnswerChange(value)
          const optionIndex = question.options?.indexOf(value) ?? -1
          if (optionIndex >= 0) {
            announce(`Selected option ${String.fromCharCode(65 + optionIndex)}`)
          }
        }}
        disabled={disabled}
        className="space-y-3"
        aria-label="Answer options"
      >
        {question.options?.map((option, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3, delay: 0.1 * index }}
            className={`flex items-center space-x-3 p-4 rounded-lg border transition-all hover:bg-muted/50 ${
              answer === option ? 'bg-primary/10 border-primary' : 'border-muted'
            } ${
              showCorrectAnswer && option === question.correctAnswer
                ? 'bg-green-50 border-green-500 dark:bg-green-900/20'
                : ''
            } ${
              showCorrectAnswer && answer === option && option !== question.correctAnswer
                ? 'bg-red-50 border-red-500 dark:bg-red-900/20'
                : ''
            }`}
          >
            <RadioGroupItem
              value={option}
              id={`option-${index}`}
              data-answer-option
              aria-describedby={showCorrectAnswer && option === question.correctAnswer ? `correct-${index}` : undefined}
            />
            <Label
              htmlFor={`option-${index}`}
              className="flex-1 cursor-pointer text-sm leading-relaxed"
            >
              <span className="font-medium mr-2">{String.fromCharCode(65 + index)}.</span>
              {option}
            </Label>
            {showCorrectAnswer && option === question.correctAnswer && (
              <>
                <Badge className="bg-green-500">Correct</Badge>
                <span id={`correct-${index}`} className="sr-only">
                  This is the correct answer
                </span>
              </>
            )}
          </motion.div>
        ))}
      </RadioGroup>
    </div>
  )

  const renderTrueFalse = () => (
    <div className="space-y-4">
      <RadioGroup
        value={answer || ""}
        onValueChange={onAnswerChange}
        disabled={disabled}
        className="space-y-3"
      >
        {['True', 'False'].map((option, index) => (
          <motion.div
            key={option}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3, delay: 0.1 * index }}
            className={`flex items-center space-x-3 p-4 rounded-lg border transition-all hover:bg-muted/50 ${
              answer === option ? 'bg-primary/10 border-primary' : 'border-muted'
            } ${
              showCorrectAnswer && option === question.correctAnswer
                ? 'bg-green-50 border-green-500 dark:bg-green-900/20'
                : ''
            } ${
              showCorrectAnswer && answer === option && option !== question.correctAnswer
                ? 'bg-red-50 border-red-500 dark:bg-red-900/20'
                : ''
            }`}
          >
            <RadioGroupItem value={option} id={`tf-${option}`} />
            <Label
              htmlFor={`tf-${option}`}
              className="flex-1 cursor-pointer text-sm font-medium"
            >
              {option}
            </Label>
            {showCorrectAnswer && option === question.correctAnswer && (
              <Badge className="bg-green-500">Correct</Badge>
            )}
          </motion.div>
        ))}
      </RadioGroup>
    </div>
  )

  const renderFillBlank = () => (
    <div className="space-y-4">
      <div className="p-4 bg-muted/50 rounded-lg">
        <p className="text-sm text-muted-foreground mb-2">
          Fill in the blank with the most appropriate answer:
        </p>
        <Input
          value={answer || ""}
          onChange={(e) => onAnswerChange(e.target.value)}
          disabled={disabled}
          placeholder="Type your answer here..."
          className={`${
            showCorrectAnswer
              ? answer?.toLowerCase().trim() === question.correctAnswer?.toLowerCase().trim()
                ? 'border-green-500 bg-green-50 dark:bg-green-900/20'
                : 'border-red-500 bg-red-50 dark:bg-red-900/20'
              : ''
          }`}
        />
        {showCorrectAnswer && (
          <div className="mt-2 text-sm">
            <span className="font-medium">Correct answer: </span>
            <span className="text-green-600 font-medium">{question.correctAnswer}</span>
          </div>
        )}
      </div>
    </div>
  )

  const renderEssay = () => (
    <div className="space-y-4">
      <div className="p-4 bg-muted/50 rounded-lg">
        <p className="text-sm text-muted-foreground mb-2">
          Provide a detailed answer to the question below:
        </p>
        <Textarea
          value={answer || ""}
          onChange={(e) => onAnswerChange(e.target.value)}
          disabled={disabled}
          placeholder="Write your answer here..."
          rows={6}
          className="resize-none"
        />
        <div className="flex justify-between items-center mt-2 text-xs text-muted-foreground">
          <span>
            {answer ? answer.length : 0} characters
          </span>
          <span>
            Recommended: 200-500 words
          </span>
        </div>
      </div>
    </div>
  )

  const renderMatching = () => {
    // Parse the pairs from options (stored as "left|right" strings)
    const pairs = question.options?.map(option => {
      const [left, right] = option.split('|')
      return { left: left?.trim() || '', right: right?.trim() || '' }
    }).filter(pair => pair.left && pair.right) || []

    // Get left and right items separately
    const leftItems = pairs.map(pair => pair.left)
    const rightItems = pairs.map(pair => pair.right)

    // Parse current answer (should be an array of matches in the same order as pairs)
    const currentMatches = Array.isArray(answer) ? answer : []

    const handleMatchChange = (leftIndex: number, selectedRight: string) => {
      const newMatches = [...currentMatches]
      newMatches[leftIndex] = selectedRight
      onAnswerChange(newMatches)
      announce(`Matched ${leftItems[leftIndex]} with ${selectedRight}`)
    }

    return (
      <div className="space-y-4">
        <div className="p-4 bg-muted/50 rounded-lg">
          <p className="text-sm text-muted-foreground mb-4">
            Match each item on the left with the correct item on the right:
          </p>

          <div className="space-y-4">
            {leftItems.map((leftItem, index) => (
              <motion.div
                key={leftItem}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.1 * index }}
                className="flex items-center gap-4 p-3 rounded-lg border bg-background"
              >
                <div className="flex-1 font-medium">
                  {leftItem}
                </div>
                <div className="text-muted-foreground">→</div>
                <div className="flex-1">
                  <select
                    value={currentMatches[index] || ''}
                    onChange={(e) => handleMatchChange(index, e.target.value)}
                    disabled={disabled}
                    className="w-full p-2 border rounded-md bg-background"
                  >
                    <option value="">Select a match...</option>
                    {rightItems.map((rightItem, rightIndex) => (
                      <option key={rightIndex} value={rightItem}>
                        {rightItem}
                      </option>
                    ))}
                  </select>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Show correct answers if enabled */}
          {showCorrectAnswer && (
            <div className="mt-4 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200">
              <h4 className="font-medium text-green-800 dark:text-green-200 mb-2">Correct Matches:</h4>
              <div className="space-y-1 text-sm">
                {pairs.map((pair, index) => {
                  const isCorrect = currentMatches[index] === pair.right
                  return (
                    <div key={index} className={`flex items-center gap-2 ${
                      isCorrect ? 'text-green-700 dark:text-green-300' : 'text-red-700 dark:text-red-300'
                    }`}>
                      <span>{pair.left} → {pair.right}</span>
                      {isCorrect ? (
                        <Badge className="bg-green-500 text-white">✓</Badge>
                      ) : (
                        <Badge className="bg-red-500 text-white">✗</Badge>
                      )}
                    </div>
                  )
                })}
              </div>
            </div>
          )}
        </div>
      </div>
    )
  }

  const renderQuestionContent = () => {
    switch (question.type) {
      case 'MCQ':
        return renderMCQ()
      case 'TRUE_FALSE':
        return renderTrueFalse()
      case 'FILL_BLANK':
        return renderFillBlank()
      case 'ESSAY':
        return renderEssay()
      case 'MATCHING':
        return renderMatching()
      case 'SHORT_ANSWER':
        return renderFillBlank() // Reuse fill blank for short answer
      default:
        return <div>Unsupported question type: {question.type}</div>
    }
  }

  return (
    <div className="space-y-6">
      {/* Question Text */}
      <div className="space-y-4">
        <div className="prose prose-sm max-w-none">
          <p className="text-lg font-medium leading-relaxed">
            {question.text}
          </p>
        </div>

        {/* Question Image */}
        {question.imageUrl && (
          <div className="relative">
            <img
              src={question.imageUrl}
              alt="Question illustration"
              className="max-w-full h-auto rounded-lg border"
            />
          </div>
        )}

        {/* Question Tags */}
        {question.tags && question.tags.length > 0 && (
          <div className="flex flex-wrap gap-2">
            {question.tags.map((tag) => (
              <Badge key={tag} variant="secondary" className="text-xs">
                {tag}
              </Badge>
            ))}
          </div>
        )}
      </div>

      {/* Answer Section */}
      <div className="space-y-4">
        {renderQuestionContent()}
      </div>

      {/* Hint Section */}
      {!showExplanation && (
        <div className="space-y-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowHint(!showHint)}
            className="text-xs"
          >
            <HelpCircle className="h-3 w-3 mr-1" />
            {showHint ? 'Hide Hint' : 'Show Hint'}
          </Button>
          
          {showHint && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800"
            >
              <p className="text-sm text-blue-800 dark:text-blue-200">
                <strong>Hint:</strong> Consider the fundamental concepts related to{' '}
                {question.tags && question.tags.length > 0 ? question.tags.join(', ') : 'this topic'}. Think about the most common and widely accepted practices.
              </p>
            </motion.div>
          )}
        </div>
      )}

      {/* Explanation Section */}
      {showExplanation && question.explanation && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800"
        >
          <h4 className="font-semibold text-green-800 dark:text-green-200 mb-2">
            Explanation:
          </h4>
          <p className="text-sm text-green-700 dark:text-green-300 leading-relaxed">
            {question.explanation}
          </p>
        </motion.div>
      )}
    </div>
  )
}
