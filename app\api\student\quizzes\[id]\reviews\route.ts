import { NextRequest } from 'next/server'
import { z } from 'zod'
import { createAPIHandler, APIResponse, commonSchemas } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'

const createReviewSchema = z.object({
  rating: z.number().min(1).max(5),
  title: z.string().optional(),
  comment: z.string().optional(),
  isPublic: z.boolean().default(true)
})

const querySchema = commonSchemas.pagination.extend({
  sortBy: z.enum(['rating', 'createdAt']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc')
})

// GET /api/student/quizzes/[id]/reviews - Get reviews for a quiz
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT',
    validateQuery: querySchema
  },
  async (request: NextRequest, { user, params, validatedQuery }) => {
    const resolvedParams = await params
    const quizId = resolvedParams?.id as string
    const { page, limit, sortBy, sortOrder } = validatedQuery

    if (!quizId) {
      return APIResponse.error('Quiz ID is required', 400)
    }

    // Check if quiz exists and is published
    const quiz = await prisma.quiz.findFirst({
      where: {
        id: quizId,
        isPublished: true
      }
    })

    if (!quiz) {
      return APIResponse.error('Quiz not found', 404)
    }

    // Get reviews with pagination
    const [reviews, totalCount] = await Promise.all([
      prisma.quizReview.findMany({
        where: {
          quizId,
          isPublic: true
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              image: true
            }
          }
        },
        orderBy: {
          [sortBy]: sortOrder
        },
        skip: (page - 1) * limit,
        take: limit
      }),
      prisma.quizReview.count({
        where: {
          quizId,
          isPublic: true
        }
      })
    ])

    // Get review statistics
    const reviewStats = await prisma.quizReview.aggregate({
      where: {
        quizId,
        isPublic: true
      },
      _avg: {
        rating: true
      },
      _count: {
        rating: true
      }
    })

    // Get rating distribution
    const ratingDistribution = await prisma.quizReview.groupBy({
      by: ['rating'],
      where: {
        quizId,
        isPublic: true
      },
      _count: {
        rating: true
      },
      orderBy: {
        rating: 'desc'
      }
    })

    return APIResponse.success({
      reviews,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit)
      },
      stats: {
        averageRating: reviewStats._avg.rating || 0,
        totalReviews: reviewStats._count.rating || 0,
        ratingDistribution: ratingDistribution.map(item => ({
          rating: item.rating,
          count: item._count.rating
        }))
      }
    })
  }
)

// POST /api/student/quizzes/[id]/reviews - Create or update a review
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT',
    validateBody: createReviewSchema
  },
  async (request: NextRequest, { user, params, validatedBody }) => {
    const resolvedParams = await params
    const quizId = resolvedParams?.id as string
    const { rating, title, comment, isPublic } = validatedBody

    if (!quizId) {
      return APIResponse.error('Quiz ID is required', 400)
    }

    // Check if quiz exists and is published
    const quiz = await prisma.quiz.findFirst({
      where: {
        id: quizId,
        isPublished: true
      }
    })

    if (!quiz) {
      return APIResponse.error('Quiz not found', 404)
    }

    // Check if user has completed the quiz at least once
    const hasCompleted = await prisma.quizAttempt.findFirst({
      where: {
        quizId,
        userId: user.id,
        isCompleted: true
      }
    })

    if (!hasCompleted) {
      return APIResponse.error('You must complete the quiz before reviewing it', 400)
    }

    // Create or update review
    const review = await prisma.quizReview.upsert({
      where: {
        quizId_userId: {
          quizId,
          userId: user.id
        }
      },
      update: {
        rating,
        title,
        comment,
        isPublic
      },
      create: {
        quizId,
        userId: user.id,
        rating,
        title,
        comment,
        isPublic
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            image: true
          }
        }
      }
    })

    return APIResponse.success(review, 201)
  }
)
