import { NextRequest } from 'next/server'
import { auth } from '@/auth'
import { prisma } from '@/lib/prisma'
import { APIResponse } from '@/lib/api-middleware'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string } > }
) {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return APIResponse.error('Please sign in to complete practice')
    }
    const { id } = await params
    const practiceId = id
    const body = await request.json()
    const { attemptId, answers, timeSpent } = body

    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      include: {
        practiceStats: true
      }
    })

    if (!user) {
      return APIResponse.notFound('User not found')
    }

    // Get the practice session and attempt
    const practiceQuiz = await prisma.quiz.findUnique({
      where: {
        id: practiceId,
        type: 'DAILY_PRACTICE'
      },
      include: {
        questions: {
          select: {
            id: true,
            correctAnswer: true,
            points: true
          }
        }
      }
    })

    if (!practiceQuiz) {
      return APIResponse.notFound('Practice session not found')
    }

    const attempt = await prisma.quizAttempt.findUnique({
      where: {
        id: attemptId,
        userId: user.id,
        quizId: practiceId
      }
    })

    if (!attempt) {
      return APIResponse.notFound('Practice attempt not found')
    }

    if (attempt.completedAt) {
      return APIResponse.error('Practice session already completed', 400)
    }

    // Calculate score
    let correctAnswers = 0
    let totalPoints = 0
    let earnedPoints = 0

    practiceQuiz.questions.forEach(question => {
      totalPoints += question.points
      const userAnswer = answers[question.id]
      
      if (userAnswer === question.correctAnswer) {
        correctAnswers++
        earnedPoints += question.points
      }
    })

    const percentage = totalPoints > 0 ? Math.round((earnedPoints / totalPoints) * 100) : 0
    const practicePoints = Math.round((percentage / 100) * (practiceQuiz.points || 50))

    // Update attempt
    const completedAttempt = await prisma.quizAttempt.update({
      where: { id: attemptId },
      data: {
        answers,
        score: earnedPoints,
        percentage,
        correctAnswers,
        incorrectAnswers: practiceQuiz.questions.length - correctAnswers,
        unansweredQuestions: 0,
        completedAt: new Date()
      }
    })

    // Update or create practice stats
    let practiceStats = user.practiceStats
    if (!practiceStats) {
      practiceStats = await prisma.userPracticeStats.create({
        data: {
          userId: user.id,
          totalSessions: 1,
          totalPoints: practicePoints,
          currentStreak: 1,
          longestStreak: 1,
          lastPracticeDate: new Date()
        }
      })
    } else {
      // Calculate new streak
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      const lastPracticeDate = practiceStats.lastPracticeDate
      
      let newStreak = practiceStats.currentStreak
      
      if (lastPracticeDate) {
        const daysSinceLastPractice = Math.floor(
          (today.getTime() - lastPracticeDate.getTime()) / (1000 * 60 * 60 * 24)
        )
        
        if (daysSinceLastPractice === 0) {
          // Same day, no streak change
        } else if (daysSinceLastPractice === 1) {
          // Consecutive day, increment streak
          newStreak += 1
        } else {
          // Gap in practice, reset streak
          newStreak = 1
        }
      } else {
        newStreak = 1
      }

      const newLongestStreak = Math.max(practiceStats.longestStreak, newStreak)

      practiceStats = await prisma.userPracticeStats.update({
        where: { id: practiceStats.id },
        data: {
          totalSessions: practiceStats.totalSessions + 1,
          totalPoints: practiceStats.totalPoints + practicePoints,
          currentStreak: newStreak,
          longestStreak: newLongestStreak,
          lastPracticeDate: new Date()
        }
      })
    }

    // Update user points
    await prisma.user.update({
      where: { id: user.id },
      data: {
        points: user.points + practicePoints
      }
    })

    return APIResponse.success(
      {
        attempt: {
          id: completedAttempt.id,
          score: completedAttempt.score,
          percentage: completedAttempt.percentage,
          correctAnswers: completedAttempt.correctAnswers,
          totalQuestions: practiceQuiz.questions.length,
          pointsEarned: practicePoints
        },
        stats: {
          currentStreak: practiceStats.currentStreak,
          longestStreak: practiceStats.longestStreak,
          totalSessions: practiceStats.totalSessions,
          totalPoints: practiceStats.totalPoints
        }
      },
      'Practice session completed successfully'
    )

  } catch (error) {
    console.error('Error completing practice session:', error)
    return APIResponse.error('Failed to complete practice session', 500)
  }
}
