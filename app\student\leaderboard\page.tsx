"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  Trophy,
  Medal,
  Award,
  Search,
  Filter,
  Crown,
  TrendingUp,
  TrendingDown,
  Users,
  Target,
  RefreshCw,
  User
} from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { motion, AnimatePresence } from "framer-motion"
import { toast } from "sonner"

interface LeaderboardEntry {
  id: string
  name: string
  email?: string
  image?: string
  totalPoints: number
  totalQuizzes: number
  averageScore: number
  streak: number
  createdAt: string
  rank: number
  user: {
    id: string
    name: string
    avatar?: string
    isCurrentUser: boolean
  }
  badges: string[]
  change: number
  score: number
}

interface LeaderboardStats {
  totalUsers: number
  currentUserRank: number
  currentUserScore: number
  averageScore: number
  topScore: number
}

export default function StudentLeaderboard() {
  const [leaderboard, setLeaderboard] = useState<LeaderboardEntry[]>([])
  const [stats, setStats] = useState<LeaderboardStats | null>(null)
  const [userRank, setUserRank] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedPeriod, setSelectedPeriod] = useState("all-time")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [showFilters, setShowFilters] = useState(false)

  const periods = [
    { value: "all-time", label: "All Time" },
    { value: "monthly", label: "This Month" },
    { value: "weekly", label: "This Week" },
    { value: "daily", label: "Today" }
  ]

  const categories = ["All Categories", "Programming", "Web Development", "Data Science", "Mobile Development", "Design"]

  useEffect(() => {
    fetchLeaderboard()
  }, [selectedPeriod, selectedCategory])

  const fetchLeaderboard = async () => {
    setLoading(true)
    try {
      // Build query parameters
      const params = new URLSearchParams({
        period: selectedPeriod,
        limit: '50'
      })

      if (selectedCategory !== 'All Categories') {
        params.append('category', selectedCategory)
      }

      const response = await fetch(`/api/student/leaderboard?${params}`)

      if (!response.ok) {
        throw new Error('Failed to fetch leaderboard')
      }

      const data = await response.json()

      if (data.success) {
        const leaderboardData = data.data?.leaderboard || []
        const currentUserData = data.data?.currentUser

        setLeaderboard(leaderboardData)
        setUserRank(currentUserData)

        // Calculate average score from leaderboard data
        const avgScore = leaderboardData.length > 0
          ? Math.round(leaderboardData.reduce((sum: number, entry: any) => sum + (entry.averageScore || 0), 0) / leaderboardData.length)
          : 0

        // Set basic stats
        setStats({
          totalUsers: data.data?.totalUsers || 0,
          currentUserRank: currentUserData?.rank || 0,
          currentUserScore: currentUserData?.totalPoints || currentUserData?.score || 0,
          averageScore: avgScore,
          topScore: leaderboardData.length > 0 ? (leaderboardData[0].totalPoints || leaderboardData[0].score || 0) : 0
        })
      } else {
        throw new Error(data.message || 'Failed to fetch leaderboard')
      }

    } catch (error) {
      console.error('Error fetching leaderboard:', error)
      toast.error('Failed to load leaderboard. Please try again.')

      // Set empty state to prevent crashes
      setLeaderboard([])
      setUserRank(null)
      setStats({
        totalUsers: 0,
        currentUserRank: 0,
        currentUserScore: 0,
        averageScore: 0,
        topScore: 0
      })
    } finally {
      setLoading(false)
    }
  }

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return (
          <div className="relative">
            <Crown className="h-6 w-6 text-yellow-500 drop-shadow-lg" />
            <div className="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-full animate-pulse"></div>
          </div>
        )
      case 2:
        return (
          <div className="relative">
            <Medal className="h-6 w-6 text-gray-300 drop-shadow-md" style={{ color: '#C0C0C0' }} />
            <div className="absolute -top-1 -right-1 w-2 h-2 bg-gradient-to-r from-gray-300 to-gray-500 rounded-full"></div>
          </div>
        )
      case 3:
        return (
          <div className="relative">
            <Award className="h-6 w-6 text-amber-600 drop-shadow-md" style={{ color: '#CD7F32' }} />
            <div className="absolute -top-1 -right-1 w-2 h-2 bg-gradient-to-r from-amber-400 to-amber-600 rounded-full"></div>
          </div>
        )
      default:
        return <span className="text-lg font-bold text-muted-foreground">#{rank}</span>
    }
  }

  const getRankBgColor = (rank: number) => {
    switch (rank) {
      case 1:
        return "bg-gradient-to-r from-yellow-50 to-yellow-100 dark:from-yellow-900/20 dark:to-yellow-800/20 border-yellow-200 dark:border-yellow-700"
      case 2:
        return "bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-900/20 dark:to-gray-800/20 border-gray-200 dark:border-gray-700"
      case 3:
        return "bg-gradient-to-r from-amber-50 to-amber-100 dark:from-amber-900/20 dark:to-amber-800/20 border-amber-200 dark:border-amber-700"
      default:
        return "bg-background border-border"
    }
  }

  const getRankColor = (rank: number) => {
    switch (rank) {
      case 1:
        return "bg-gradient-to-r from-yellow-400 via-yellow-500 to-yellow-600 text-white shadow-lg shadow-yellow-500/30"
      case 2:
        return "bg-gradient-to-r from-gray-300 via-gray-400 to-gray-500 text-white shadow-lg shadow-gray-400/30"
      case 3:
        return "bg-gradient-to-r from-amber-400 via-amber-500 to-amber-600 text-white shadow-lg shadow-amber-500/30"
      default:
        return "bg-muted text-muted-foreground"
    }
  }

  const getChangeIcon = (change: number) => {
    if (change > 0) return <TrendingUp className="h-4 w-4 text-green-600" />
    if (change < 0) return <TrendingDown className="h-4 w-4 text-red-600" />
    return <div className="w-4 h-4" />
  }

  const filteredLeaderboard = leaderboard.filter(entry =>
    entry.name.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const currentUser = userRank

  return (
    <div className="p-6 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Leaderboard</h1>
          <p className="text-muted-foreground mt-1">
            See how you rank against other learners
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={fetchLeaderboard}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
          >
            <Filter className="h-4 w-4 mr-2" />
            {showFilters ? 'Hide Filters' : 'Show Filters'}
          </Button>
        </div>
      </div>

      {/* Stats Overview */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Card className="hover:shadow-lg transition-shadow">
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-3xl font-bold">#{stats.currentUserRank}</div>
                    <p className="text-sm text-muted-foreground">Your Rank</p>
                  </div>
                  <Trophy className="h-8 w-8 text-yellow-600" />
                </div>
                <div className="mt-4">
                  <p className="text-xs text-muted-foreground">
                    Out of {stats.totalUsers.toLocaleString()} users
                  </p>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            <Card className="hover:shadow-lg transition-shadow">
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-3xl font-bold">{stats.currentUserScore}</div>
                    <p className="text-sm text-muted-foreground">Your Score</p>
                  </div>
                  <Target className="h-8 w-8 text-blue-600" />
                </div>
                <div className="mt-4">
                  <p className="text-xs text-green-600">
                    +{stats.currentUserScore - stats.averageScore} above average
                  </p>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <Card className="hover:shadow-lg transition-shadow">
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-3xl font-bold">{stats.topScore}</div>
                    <p className="text-sm text-muted-foreground">Top Score</p>
                  </div>
                  <Crown className="h-8 w-8 text-yellow-600" />
                </div>
                <div className="mt-4">
                  <p className="text-xs text-muted-foreground">
                    Gap: {stats.topScore - stats.currentUserScore} points
                  </p>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <Card className="hover:shadow-lg transition-shadow">
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-3xl font-bold">{stats.totalUsers.toLocaleString()}</div>
                    <p className="text-sm text-muted-foreground">Total Users</p>
                  </div>
                  <Users className="h-8 w-8 text-purple-600" />
                </div>
                <div className="mt-4">
                  <p className="text-xs text-muted-foreground">
                    Active learners
                  </p>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      )}

      {/* Current User Highlight */}
      {currentUser && (
        <Card className="border-2 border-primary bg-primary/5">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Your Position
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className={`w-12 h-12 rounded-full flex items-center justify-center ${getRankColor(currentUser.rank || 0)}`}>
                  {getRankIcon(currentUser.rank || 0)}
                </div>
                <div className="flex items-center gap-3">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={currentUser.image || currentUser.user?.avatar} />
                    <AvatarFallback>
                      {(currentUser.name || currentUser.user?.name || 'User').split(' ').map((n: string) => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <h3 className="font-semibold">{currentUser.name || currentUser.user?.name || 'Unknown User'}</h3>
                    <p className="text-sm text-muted-foreground">
                      {currentUser.totalQuizzes || 0} quizzes • {currentUser.averageScore || 0}% avg
                    </p>
                  </div>
                </div>
              </div>

              <div className="text-right">
                <div className="text-2xl font-bold">{currentUser.totalPoints || currentUser.score || 0}</div>
                <div className="flex items-center gap-1 text-sm">
                  {getChangeIcon(currentUser.change || 0)}
                  <span className={(currentUser.change || 0) > 0 ? 'text-green-600' : (currentUser.change || 0) < 0 ? 'text-red-600' : 'text-muted-foreground'}>
                    {(currentUser.change || 0) > 0 ? '+' : ''}{currentUser.change || 0}
                  </span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="space-y-4">
            {/* Search Bar */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder="Search users..."
                className="pl-10"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>

            {/* Advanced Filters */}
            <AnimatePresence>
              {showFilters && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t"
                >
                  <div>
                    <label className="text-sm font-medium mb-2 block">Time Period</label>
                    <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {periods.map(period => (
                          <SelectItem key={period.value} value={period.value}>
                            {period.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium mb-2 block">Category</label>
                    <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {categories.map(category => (
                          <SelectItem key={category} value={category.toLowerCase().replace(' ', '-')}>
                            {category}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </CardContent>
      </Card>

      {/* Leaderboard */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Trophy className="h-5 w-5" />
            Top Performers
          </CardTitle>
          <CardDescription>
            {selectedPeriod === 'all-time' ? 'All-time' : 
             selectedPeriod === 'monthly' ? 'This month' :
             selectedPeriod === 'weekly' ? 'This week' : 'Today'} leaderboard
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-4">
              {[...Array(10)].map((_, i) => (
                <div key={i} className="animate-pulse flex items-center gap-4 p-4">
                  <div className="w-12 h-12 bg-muted rounded-full"></div>
                  <div className="w-10 h-10 bg-muted rounded-full"></div>
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-muted rounded w-1/4"></div>
                    <div className="h-3 bg-muted rounded w-1/2"></div>
                  </div>
                  <div className="h-6 bg-muted rounded w-16"></div>
                </div>
              ))}
            </div>
          ) : (
            <div className="space-y-2">
              {filteredLeaderboard.slice(0, 50).map((entry, index) => (
                <motion.div
                  key={entry.user.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: 0.05 * index }}
                  className={`flex items-center gap-4 p-4 rounded-lg transition-all hover:bg-muted/50 ${
                    entry.user.isCurrentUser ? 'bg-primary/10 border border-primary/20' :
                    entry.rank <= 3 ? getRankBgColor(entry.rank) : ''
                  }`}
                >
                  <div className={`w-12 h-12 rounded-full flex items-center justify-center ${getRankColor(entry.rank)}`}>
                    {getRankIcon(entry.rank)}
                  </div>
                  
                  <div className="flex items-center gap-3 flex-1">
                    <Avatar className="h-10 w-10">
                      <AvatarImage src={entry.image || entry.user?.avatar} />
                      <AvatarFallback>
                        {(entry.name || entry.user?.name || 'User').split(' ').map((n: string) => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <h3 className={`font-semibold ${entry.user?.isCurrentUser ? 'text-primary' : ''}`}>
                          {entry.name || entry.user?.name || 'Unknown User'}
                        </h3>
                        {entry.user?.isCurrentUser && (
                          <Badge variant="outline" className="text-xs">You</Badge>
                        )}
                        <div className="flex gap-1">
                          {(entry.badges || []).map((badge, i) => (
                            <span key={i} className="text-sm">{badge}</span>
                          ))}
                        </div>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {entry.totalQuizzes || 0} quizzes • {entry.averageScore || 0}% avg • {entry.streak || 0} day streak
                      </p>
                    </div>
                  </div>

                  <div className="text-right">
                    <div className="text-xl font-bold">{entry.totalPoints || entry.score || 0}</div>
                    <div className="flex items-center gap-1 text-sm">
                      {getChangeIcon(entry.change || 0)}
                      <span className={(entry.change || 0) > 0 ? 'text-green-600' : (entry.change || 0) < 0 ? 'text-red-600' : 'text-muted-foreground'}>
                        {(entry.change || 0) > 0 ? '+' : ''}{entry.change || 0}
                      </span>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          )}

          {!loading && filteredLeaderboard.length === 0 && (
            <div className="text-center py-12">
              <Trophy className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">No leaderboard data</h3>
              <p className="text-muted-foreground mb-6">
                No users found for the selected criteria. Try adjusting your filters or check back later.
              </p>
              <Button onClick={fetchLeaderboard}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
