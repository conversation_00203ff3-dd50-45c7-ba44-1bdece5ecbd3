'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface FloatingShapeProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  color?: 'violet' | 'blue' | 'cyan' | 'pink' | 'gradient';
  animation?: 'float' | 'rotate' | 'pulse' | 'drift';
  delay?: number;
  duration?: number;
}

export function FloatingShape({
  className,
  size = 'md',
  color = 'violet',
  animation = 'float',
  delay = 0,
  duration = 6,
}: FloatingShapeProps) {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-16 h-16',
    lg: 'w-24 h-24',
    xl: 'w-32 h-32'
  };

  const colorClasses = {
    violet: 'bg-violet-400/20',
    blue: 'bg-electric-400/20',
    cyan: 'bg-cyan-400/20',
    pink: 'bg-pink-400/20',
    gradient: 'bg-gradient-accent'
  };

  const animations = {
    float: {
      y: [-20, 20, -20],
      transition: {
        duration,
        repeat: Infinity,
        ease: 'easeInOut',
        delay
      }
    },
    rotate: {
      rotate: [0, 360],
      transition: {
        duration,
        repeat: Infinity,
        ease: 'linear',
        delay
      }
    },
    pulse: {
      scale: [1, 1.2, 1],
      opacity: [0.5, 0.8, 0.5],
      transition: {
        duration,
        repeat: Infinity,
        ease: 'easeInOut',
        delay
      }
    },
    drift: {
      x: [-30, 30, -30],
      y: [-20, 20, -20],
      rotate: [0, 180, 360],
      transition: {
        duration: duration * 2,
        repeat: Infinity,
        ease: 'easeInOut',
        delay
      }
    }
  };

  return (
    <motion.div
      className={cn(
        'absolute rounded-full blur-sm',
        sizeClasses[size],
        colorClasses[color],
        className
      )}
      animate={animations[animation]}
    />
  );
}

// Background with multiple floating shapes
interface FloatingBackgroundProps {
  className?: string;
  density?: 'low' | 'medium' | 'high';
  colors?: Array<'violet' | 'blue' | 'cyan' | 'pink' | 'gradient'>;
}

export function FloatingBackground({
  className,
  density = 'medium',
  colors = ['violet', 'blue', 'cyan', 'pink']
}: FloatingBackgroundProps) {
  const [shapes, setShapes] = React.useState<Array<{
    id: number;
    size: 'sm' | 'md' | 'lg';
    color: string;
    animation: 'float' | 'rotate' | 'pulse' | 'drift';
    delay: number;
    duration: number;
    position: { top: string; left: string };
  }>>([]);

  const densityCount = {
    low: 8,
    medium: 15,
    high: 25
  };

  React.useEffect(() => {
    // Generate shapes on client side only to avoid hydration mismatch
    const generatedShapes = Array.from({ length: densityCount[density] }, (_, i) => ({
      id: i,
      size: ['sm', 'md', 'lg'][Math.floor(Math.random() * 3)] as 'sm' | 'md' | 'lg',
      color: colors[Math.floor(Math.random() * colors.length)],
      animation: ['float', 'rotate', 'pulse', 'drift'][Math.floor(Math.random() * 4)] as 'float' | 'rotate' | 'pulse' | 'drift',
      delay: Math.random() * 5,
      duration: 4 + Math.random() * 8,
      position: {
        top: `${Math.random() * 100}%`,
        left: `${Math.random() * 100}%`,
      }
    }));
    setShapes(generatedShapes);
  }, [density, colors]);

  return (
    <div className={cn('absolute inset-0 overflow-hidden pointer-events-none', className)}>
      {shapes.map((shape) => (
        <FloatingShape
          key={shape.id}
          size={shape.size}
          color={shape.color}
          animation={shape.animation}
          delay={shape.delay}
          duration={shape.duration}
          style={shape.position}
        />
      ))}
    </div>
  );
}

// Animated Grid Background
interface AnimatedGridProps {
  className?: string;
  size?: number;
  opacity?: number;
  color?: string;
}

export function AnimatedGrid({
  className,
  size = 50,
  opacity = 0.1,
  color = 'currentColor'
}: AnimatedGridProps) {
  return (
    <motion.div
      className={cn('absolute inset-0 pointer-events-none', className)}
      initial={{ opacity: 0 }}
      animate={{ opacity }}
      transition={{ duration: 2, ease: 'easeOut' }}
    >
      <svg
        className="w-full h-full"
        xmlns="http://www.w3.org/2000/svg"
      >
        <defs>
          <pattern
            id="grid"
            width={size}
            height={size}
            patternUnits="userSpaceOnUse"
          >
            <motion.path
              d={`M ${size} 0 L 0 0 0 ${size}`}
              fill="none"
              stroke={color}
              strokeWidth="1"
              initial={{ pathLength: 0 }}
              animate={{ pathLength: 1 }}
              transition={{ duration: 3, ease: 'easeInOut' }}
            />
          </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#grid)" />
      </svg>
    </motion.div>
  );
}

// Particle System
interface ParticleSystemProps {
  className?: string;
  count?: number;
  colors?: string[];
}

export function ParticleSystem({
  className,
  count = 50,
  colors = ['#8b5cf6', '#3b82f6', '#06b6d4', '#f472b6']
}: ParticleSystemProps) {
  const [particles, setParticles] = React.useState<Array<{
    id: number;
    color: string;
    size: number;
    initialX: number;
    initialY: number;
    duration: number;
    delay: number;
  }>>([]);

  React.useEffect(() => {
    // Generate particles on client side only to avoid hydration mismatch
    const generatedParticles = Array.from({ length: count }, (_, i) => ({
      id: i,
      color: colors[Math.floor(Math.random() * colors.length)],
      size: Math.random() * 4 + 1,
      initialX: Math.random() * 100,
      initialY: Math.random() * 100,
      duration: Math.random() * 20 + 10,
      delay: Math.random() * 5,
    }));
    setParticles(generatedParticles);
  }, [count, colors]);

  return (
    <div className={cn('absolute inset-0 overflow-hidden pointer-events-none', className)}>
      {particles.map((particle) => (
        <motion.div
          key={particle.id}
          className="absolute rounded-full opacity-60"
          style={{
            backgroundColor: particle.color,
            width: particle.size,
            height: particle.size,
            left: `${particle.initialX}%`,
            top: `${particle.initialY}%`,
          }}
          animate={{
            y: [0, -100, 0],
            x: [0, Math.random() * 100 - 50, 0],
            opacity: [0, 1, 0],
          }}
          transition={{
            duration: particle.duration,
            repeat: Infinity,
            ease: 'easeInOut',
            delay: particle.delay,
          }}
        />
      ))}
    </div>
  );
}

// Gradient Orbs
interface GradientOrbProps {
  className?: string;
  size?: number;
  colors?: string[];
  blur?: number;
}

export function GradientOrb({
  className,
  size = 200,
  colors = ['#8b5cf6', '#3b82f6'],
  blur = 40
}: GradientOrbProps) {
  return (
    <motion.div
      className={cn('absolute rounded-full pointer-events-none', className)}
      style={{
        width: size,
        height: size,
        background: `radial-gradient(circle, ${colors[0]}40 0%, ${colors[1]}20 50%, transparent 100%)`,
        filter: `blur(${blur}px)`,
      }}
      animate={{
        scale: [1, 1.2, 1],
        opacity: [0.3, 0.6, 0.3],
      }}
      transition={{
        duration: 8,
        repeat: Infinity,
        ease: 'easeInOut',
      }}
    />
  );
}
