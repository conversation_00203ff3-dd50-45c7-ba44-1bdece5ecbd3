import { NextRequest, NextResponse } from 'next/server'

import { prisma } from '@/lib/prisma'
import { z } from 'zod'
import { auth } from '@/auth'

const createTopicSchema = z.object({
  name: z.string().min(1, "Topic name is required"),
  description: z.string().optional(),
  chapterId: z.string().min(1, "Chapter ID is required"),
  order: z.number().optional(),
})

// GET /api/admin/categories/topics - Get topics by chapter
export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const chapterId = searchParams.get('chapterId')

    const whereClause = {
      isActive: true,
      ...(chapterId && { chapterId })
    }

    const topics = await prisma.topic.findMany({
      where: whereClause,
      include: {
        chapter: {
          include: {
            subject: true
          }
        },
        _count: {
          select: {
            quizzes: true
          }
        }
      },
      orderBy: { order: 'asc' }
    })

    return NextResponse.json({
      topics,
      success: true
    })
  } catch (error) {
    console.error('Error fetching topics:', error)
    return NextResponse.json(
      { error: 'Failed to fetch topics' },
      { status: 500 }
    )
  }
}

// POST /api/admin/categories/topics - Create new topic
export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const validatedData = createTopicSchema.parse(body)

    // Check if topic already exists in this chapter
    const existingTopic = await prisma.topic.findUnique({
      where: {
        chapterId_name: {
          chapterId: validatedData.chapterId,
          name: validatedData.name
        }
      }
    })

    if (existingTopic) {
      return NextResponse.json(
        { error: 'Topic with this name already exists in this chapter' },
        { status: 400 }
      )
    }

    // Get the next order number if not provided
    let order = validatedData.order
    if (order === undefined) {
      const lastTopic = await prisma.topic.findFirst({
        where: { chapterId: validatedData.chapterId },
        orderBy: { order: 'desc' }
      })
      order = (lastTopic?.order || 0) + 1
    }

    const topic = await prisma.topic.create({
      data: {
        ...validatedData,
        order
      },
      include: {
        chapter: {
          include: {
            subject: true
          }
        },
        _count: {
          select: {
            quizzes: true
          }
        }
      }
    })

    return NextResponse.json({
      topic,
      success: true
    })
  } catch (error) {
    console.error('Error creating topic:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Failed to create topic' },
      { status: 500 }
    )
  }
}
