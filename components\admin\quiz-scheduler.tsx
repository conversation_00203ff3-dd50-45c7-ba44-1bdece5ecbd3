"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { 
  Calendar, 
  Clock, 
  X,
  Save,
  AlertCircle,
  Users,
  Timer,
  Settings,
  Bell
} from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { motion } from "framer-motion"
import { toast } from "@/lib/toast-utils"

interface Quiz {
  id: string
  title: string
  type: string
  difficulty: string
  questionCount: number
  estimatedDuration: number
}

interface QuizSchedulerProps {
  onClose: () => void
  onSchedule: (scheduleData: any) => void
  editingSchedule?: any
}

export function QuizScheduler({ onClose, onSchedule, editingSchedule }: QuizSchedulerProps) {
  const [availableQuizzes, setAvailableQuizzes] = useState<Quiz[]>([])
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    quizId: editingSchedule?.quiz?.id || "",
    title: editingSchedule?.title || "",
    description: editingSchedule?.description || "",
    startDate: editingSchedule?.startTime ? new Date(editingSchedule.startTime).toISOString().split('T')[0] : "",
    startTime: editingSchedule?.startTime ? new Date(editingSchedule.startTime).toTimeString().slice(0, 5) : "",
    endDate: editingSchedule?.endTime ? new Date(editingSchedule.endTime).toISOString().split('T')[0] : "",
    endTime: editingSchedule?.endTime ? new Date(editingSchedule.endTime).toTimeString().slice(0, 5) : "",
    duration: editingSchedule?.duration || 30,
    maxAttempts: editingSchedule?.maxAttempts || 1,
    allowLateSubmission: false,
    sendReminders: true,
    reminderTime: 24,
    autoStart: false,
    shuffleQuestions: false,
    showResults: true,
    allowReview: true
  })

  useEffect(() => {
    fetchAvailableQuizzes()
  }, [])

  const fetchAvailableQuizzes = async () => {
    try {
      const response = await fetch('/api/admin/quizzes?published=true&limit=50')

      if (!response.ok) {
        throw new Error('Failed to fetch quizzes')
      }

      const data = await response.json()

      if (data.success && data.data.quizzes) {
        const formattedQuizzes: Quiz[] = data.data.quizzes.map((quiz: any) => ({
          id: quiz.id,
          title: quiz.title,
          type: quiz.type,
          difficulty: quiz.difficulty,
          questionCount: quiz._count?.questions || 0,
          estimatedDuration: quiz.timeLimit || 30
        }))

        setAvailableQuizzes(formattedQuizzes)
      } else {
        console.error('Invalid API response:', data)
        setAvailableQuizzes([])
      }
    } catch (error) {
      console.error('Error fetching quizzes:', error)
      toast.error('Failed to load available quizzes')
      setAvailableQuizzes([])
    }
  }

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleQuizSelect = (quizId: string) => {
    const selectedQuiz = availableQuizzes.find(q => q.id === quizId)
    if (selectedQuiz) {
      setFormData(prev => ({
        ...prev,
        quizId,
        duration: selectedQuiz.estimatedDuration,
        title: prev.title || `${selectedQuiz.title} - Scheduled Session`
      }))
    }
  }

  const validateForm = () => {
    if (!formData.quizId) {
      toast.error('Please select a quiz')
      return false
    }
    if (!formData.title.trim()) {
      toast.error('Please enter a title')
      return false
    }
    if (!formData.startDate || !formData.startTime) {
      toast.error('Please set start date and time')
      return false
    }
    if (!formData.endDate || !formData.endTime) {
      toast.error('Please set end date and time')
      return false
    }
    
    const startDateTime = new Date(`${formData.startDate}T${formData.startTime}`)
    const endDateTime = new Date(`${formData.endDate}T${formData.endTime}`)
    
    if (startDateTime >= endDateTime) {
      toast.error('End time must be after start time')
      return false
    }
    
    if (startDateTime <= new Date()) {
      toast.error('Start time must be in the future')
      return false
    }
    
    return true
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) return
    
    setLoading(true)
    try {
      const scheduleData = {
        ...formData,
        startTime: new Date(`${formData.startDate}T${formData.startTime}`).toISOString(),
        endTime: new Date(`${formData.endDate}T${formData.endTime}`).toISOString(),
        quiz: availableQuizzes.find(q => q.id === formData.quizId)
      }
      
      await onSchedule(scheduleData)
      toast.success(editingSchedule ? 'Schedule updated successfully' : 'Quiz scheduled successfully')
      onClose()
    } catch (error) {
      toast.error('Failed to schedule quiz')
    } finally {
      setLoading(false)
    }
  }

  const selectedQuiz = availableQuizzes.find(q => q.id === formData.quizId)

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className="bg-background rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-auto"
      >
        <div className="flex items-center justify-between p-6 border-b">
          <div>
            <h2 className="text-2xl font-bold">
              {editingSchedule ? 'Edit Schedule' : 'Schedule Quiz'}
            </h2>
            <p className="text-muted-foreground">
              Set up timing and access controls for your quiz
            </p>
          </div>
          <Button variant="outline" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Quiz Selection */}
          <div className="space-y-4">
            <div>
              <Label htmlFor="quiz">Select Quiz *</Label>
              <Select value={formData.quizId} onValueChange={handleQuizSelect}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose a quiz to schedule" />
                </SelectTrigger>
                <SelectContent>
                  {availableQuizzes.map((quiz) => (
                    <SelectItem key={quiz.id} value={quiz.id}>
                      <div className="flex items-center justify-between w-full">
                        <span>{quiz.title}</span>
                        <div className="flex items-center gap-2 text-xs text-muted-foreground ml-4">
                          <span>{quiz.questionCount} questions</span>
                          <span>•</span>
                          <span>{quiz.estimatedDuration} min</span>
                          <span>•</span>
                          <span>{quiz.difficulty}</span>
                        </div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {selectedQuiz && (
              <Card className="bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800">
                <CardContent className="pt-4">
                  <div className="flex items-center gap-4 text-sm">
                    <div className="flex items-center gap-1">
                      <Users className="h-4 w-4 text-blue-600" />
                      <span>{selectedQuiz.questionCount} questions</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Timer className="h-4 w-4 text-blue-600" />
                      <span>~{selectedQuiz.estimatedDuration} minutes</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Settings className="h-4 w-4 text-blue-600" />
                      <span>{selectedQuiz.difficulty} difficulty</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="title">Schedule Title *</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                placeholder="Enter schedule title"
              />
            </div>
            <div>
              <Label htmlFor="maxAttempts">Max Attempts</Label>
              <Select value={formData.maxAttempts.toString()} onValueChange={(value) => handleInputChange('maxAttempts', parseInt(value))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">1 attempt</SelectItem>
                  <SelectItem value="2">2 attempts</SelectItem>
                  <SelectItem value="3">3 attempts</SelectItem>
                  <SelectItem value="5">5 attempts</SelectItem>
                  <SelectItem value="-1">Unlimited</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Optional description for participants"
              rows={3}
            />
          </div>

          {/* Timing */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Schedule Timing
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="startDate">Start Date *</Label>
                <Input
                  id="startDate"
                  type="date"
                  value={formData.startDate}
                  onChange={(e) => handleInputChange('startDate', e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="startTime">Start Time *</Label>
                <Input
                  id="startTime"
                  type="time"
                  value={formData.startTime}
                  onChange={(e) => handleInputChange('startTime', e.target.value)}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="endDate">End Date *</Label>
                <Input
                  id="endDate"
                  type="date"
                  value={formData.endDate}
                  onChange={(e) => handleInputChange('endDate', e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="endTime">End Time *</Label>
                <Input
                  id="endTime"
                  type="time"
                  value={formData.endTime}
                  onChange={(e) => handleInputChange('endTime', e.target.value)}
                />
              </div>
            </div>

            <div>
              <Label htmlFor="duration">Quiz Duration (minutes)</Label>
              <Input
                id="duration"
                type="number"
                min="1"
                max="300"
                value={formData.duration}
                onChange={(e) => handleInputChange('duration', parseInt(e.target.value) || 30)}
              />
            </div>
          </div>

          {/* Advanced Settings */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Advanced Settings
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="allowLateSubmission"
                  checked={formData.allowLateSubmission}
                  onCheckedChange={(checked) => handleInputChange('allowLateSubmission', checked)}
                />
                <Label htmlFor="allowLateSubmission">Allow late submission</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="autoStart"
                  checked={formData.autoStart}
                  onCheckedChange={(checked) => handleInputChange('autoStart', checked)}
                />
                <Label htmlFor="autoStart">Auto-start when available</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="shuffleQuestions"
                  checked={formData.shuffleQuestions}
                  onCheckedChange={(checked) => handleInputChange('shuffleQuestions', checked)}
                />
                <Label htmlFor="shuffleQuestions">Shuffle questions</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="showResults"
                  checked={formData.showResults}
                  onCheckedChange={(checked) => handleInputChange('showResults', checked)}
                />
                <Label htmlFor="showResults">Show results immediately</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="allowReview"
                  checked={formData.allowReview}
                  onCheckedChange={(checked) => handleInputChange('allowReview', checked)}
                />
                <Label htmlFor="allowReview">Allow answer review</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="sendReminders"
                  checked={formData.sendReminders}
                  onCheckedChange={(checked) => handleInputChange('sendReminders', checked)}
                />
                <Label htmlFor="sendReminders">Send email reminders</Label>
              </div>
            </div>

            {formData.sendReminders && (
              <div>
                <Label htmlFor="reminderTime">Send reminder (hours before start)</Label>
                <Select value={formData.reminderTime.toString()} onValueChange={(value) => handleInputChange('reminderTime', parseInt(value))}>
                  <SelectTrigger className="w-48">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1 hour before</SelectItem>
                    <SelectItem value="6">6 hours before</SelectItem>
                    <SelectItem value="24">24 hours before</SelectItem>
                    <SelectItem value="48">48 hours before</SelectItem>
                    <SelectItem value="168">1 week before</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="flex justify-between items-center pt-6 border-t">
            <div className="text-sm text-muted-foreground">
              * Required fields
            </div>
            <div className="flex gap-2">
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit" disabled={loading}>
                <Save className="h-4 w-4 mr-2" />
                {loading ? 'Scheduling...' : editingSchedule ? 'Update Schedule' : 'Schedule Quiz'}
              </Button>
            </div>
          </div>
        </form>
      </motion.div>
    </div>
  )
}
