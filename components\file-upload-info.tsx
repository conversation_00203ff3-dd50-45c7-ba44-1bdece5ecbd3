"use client"

import React from "react"
import { useFileUploadSettings } from "@/lib/settings-context"
import { Badge } from "@/components/ui/badge"
import { FileText, AlertCircle } from "lucide-react"

interface FileUploadInfoProps {
  className?: string
}

export function FileUploadInfo({ className = "" }: FileUploadInfoProps) {
  const { maxFileSize, allowedFileTypes } = useFileUploadSettings()

  return (
    <div className={`space-y-2 ${className}`}>
      <div className="flex items-center gap-2 text-sm text-muted-foreground">
        <FileText className="h-4 w-4" />
        <span>Upload Requirements</span>
      </div>
      
      <div className="space-y-1">
        <div className="flex items-center gap-2">
          <span className="text-sm">Max file size:</span>
          <Badge variant="outline">{maxFileSize}MB</Badge>
        </div>
        
        <div className="space-y-1">
          <span className="text-sm">Allowed types:</span>
          <div className="flex flex-wrap gap-1">
            {allowedFileTypes.map((type) => (
              <Badge key={type} variant="secondary" className="text-xs">
                {type.toUpperCase()}
              </Badge>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

export function FileUploadValidator({ 
  file, 
  onValidation 
}: { 
  file: File
  onValidation: (isValid: boolean, error?: string) => void 
}) {
  const { maxFileSize, allowedFileTypes } = useFileUploadSettings()

  const validateFile = () => {
    // Check file size
    const fileSizeMB = file.size / (1024 * 1024)
    if (fileSizeMB > maxFileSize) {
      onValidation(false, `File size (${fileSizeMB.toFixed(1)}MB) exceeds maximum allowed size of ${maxFileSize}MB`)
      return
    }

    // Check file type
    const fileExtension = file.name.split('.').pop()?.toLowerCase()
    if (!fileExtension || !allowedFileTypes.includes(fileExtension)) {
      onValidation(false, `File type .${fileExtension} is not allowed. Allowed types: ${allowedFileTypes.join(', ')}`)
      return
    }

    onValidation(true)
  }

  // Validate on mount and when file changes
  React.useEffect(() => {
    validateFile()
  }, [file, maxFileSize, allowedFileTypes])

  return null
}
