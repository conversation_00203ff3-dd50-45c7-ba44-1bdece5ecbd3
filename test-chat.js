const { io } = require('socket.io-client');

// Test chat functionality
async function testChat() {
  console.log('🧪 Starting chat system test...');

  // Create two socket connections to simulate admin and student
  const adminSocket = io('http://localhost:3001', {
    transports: ['websocket', 'polling']
  });

  const studentSocket = io('http://localhost:3001', {
    transports: ['websocket', 'polling']
  });

  // Wait for connections
  await new Promise((resolve) => {
    let connected = 0;
    
    adminSocket.on('connect', () => {
      console.log('👨‍💼 Admin connected:', adminSocket.id);
      connected++;
      if (connected === 2) resolve();
    });

    studentSocket.on('connect', () => {
      console.log('👨‍🎓 Student connected:', studentSocket.id);
      connected++;
      if (connected === 2) resolve();
    });
  });

  // Authenticate both users and wait for confirmation
  let adminAuthenticated = false;
  let studentAuthenticated = false;

  adminSocket.on('authenticated', () => {
    console.log('👨‍💼 Admin authenticated');
    adminAuthenticated = true;
  });

  studentSocket.on('authenticated', () => {
    console.log('👨‍🎓 Student authenticated');
    studentAuthenticated = true;
  });

  adminSocket.emit('authenticate', {
    userId: 'admin-test-1',
    name: 'Test Admin',
    email: '<EMAIL>',
    role: 'ADMIN'
  });

  studentSocket.emit('authenticate', {
    userId: 'student-test-1',
    name: 'Test Student',
    email: '<EMAIL>',
    role: 'STUDENT'
  });

  // Wait for both to be authenticated
  while (!adminAuthenticated || !studentAuthenticated) {
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  console.log('✅ Both users authenticated');

  // Join the same chat room
  const roomId = 'student-general';
  console.log(`🏠 Joining room: ${roomId}`);
  
  adminSocket.emit('chat:join', { roomId });
  studentSocket.emit('chat:join', { roomId });

  // Set up message listeners
  adminSocket.on('chat:message_received', (message) => {
    console.log('👨‍💼 Admin received:', message);
  });

  studentSocket.on('chat:message_received', (message) => {
    console.log('👨‍🎓 Student received:', message);
  });

  // Wait a bit for room joining
  await new Promise(resolve => setTimeout(resolve, 1000));

  // Send test messages
  console.log('💬 Sending test messages...');
  
  adminSocket.emit('chat:message', {
    roomId,
    message: 'Hello from admin!',
    type: 'text'
  });

  await new Promise(resolve => setTimeout(resolve, 500));

  studentSocket.emit('chat:message', {
    roomId,
    message: 'Hello from student!',
    type: 'text'
  });

  // Wait for messages to be processed
  await new Promise(resolve => setTimeout(resolve, 2000));

  console.log('✅ Chat test completed');
  
  // Close connections
  adminSocket.disconnect();
  studentSocket.disconnect();
}

// Run the test
testChat().catch(console.error);
