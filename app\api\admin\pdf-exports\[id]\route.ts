import { NextRequest } from 'next/server'
 import { createAP<PERSON>Handler, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'

// GET /api/admin/pdf-exports/[id] - Get specific PDF export
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
     
  },
  async (request: NextRequest, { params }: { params: Promise<{ id: string } > }) => {
    const { id } = await params
    const exportId = id

    const pdfExport = await prisma.pdfExport.findUnique({
      where: { id: exportId },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    })

    if (!pdfExport) {
      return APIResponse.error('PDF export not found', 404, 'EXPORT_NOT_FOUND')
    }

    const result = {
      id: pdfExport.id,
      type: pdfExport.type,
      filename: pdfExport.filename,
      size: pdfExport.size,
      status: pdfExport.status,
      options: pdfExport.options ? JSON.parse(pdfExport.options) : null,
      error: pdfExport.error,
      createdAt: pdfExport.createdAt.toISOString(),
      updatedAt: pdfExport.updatedAt.toISOString(),
      user: pdfExport.user
    }

    return APIResponse.success(result, 'PDF export retrieved successfully')
  }
)

// DELETE /api/admin/pdf-exports/[id] - Delete specific PDF export
export const DELETE = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
     
  },
  async (request: NextRequest, { params }: { params: Promise<{ id: string } > }) => {
    const { id } = await params
    const exportId = id

    // Check if export exists
    const existingExport = await prisma.pdfExport.findUnique({
      where: { id: exportId }
    })

    if (!existingExport) {
      return APIResponse.error('PDF export not found', 404, 'EXPORT_NOT_FOUND')
    }

    await prisma.pdfExport.delete({
      where: { id: exportId }
    })

    return APIResponse.success(
      { deletedExportId: exportId },
      'PDF export deleted successfully'
    )
  }
)

// POST /api/admin/pdf-exports/[id]/download - Download PDF export
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
     
  },
  async (request: NextRequest, { params }: { params: Promise<{ id: string } > }) => {
    const { id } = await params
    const exportId = id

    const pdfExport = await prisma.pdfExport.findUnique({
      where: { id: exportId }
    })

    if (!pdfExport) {
      return APIResponse.error('PDF export not found', 404, 'EXPORT_NOT_FOUND')
    }

    if (pdfExport.status !== 'completed') {
      return APIResponse.error(
        'PDF export is not ready for download',
        400,
        'EXPORT_NOT_READY'
      )
    }

    // In a real implementation, you would:
    // 1. Generate a signed URL for the PDF file
    // 2. Or stream the PDF file directly
    // 3. Track download analytics
    
    // For now, we'll return a mock download URL
    const downloadUrl = `/api/admin/pdf-exports/${exportId}/file`

    return APIResponse.success(
      {
        downloadUrl,
        filename: pdfExport.filename,
        size: pdfExport.size,
        expiresAt: new Date(Date.now() + 3600000).toISOString() // 1 hour from now
      },
      'Download URL generated successfully'
    )
  }
)
