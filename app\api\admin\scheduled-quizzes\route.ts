import { NextRequest } from 'next/server'
import { z } from 'zod'
 import { createAPIHandler, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'

const createScheduledQuizSchema = z.object({
  quizId: z.string().min(1, "Quiz ID is required"),
  title: z.string().min(1, "Title is required"),
  description: z.string().optional(),
  startTime: z.string().datetime("Invalid start time"),
  endTime: z.string().datetime("Invalid end time"),
  duration: z.number().min(1).optional(),
  maxAttempts: z.number().min(1).default(1),
  allowLateSubmission: z.boolean().default(false),
  showResults: z.boolean().default(true),
  shuffleQuestions: z.boolean().default(false)
})

const querySchema = z.object({
  page: z.string().transform(val => parseInt(val) || 1),
  limit: z.string().transform(val => Math.min(parseInt(val) || 10, 50)),
  status: z.enum(['all', 'active', 'upcoming', 'completed', 'expired']).optional().default('all'),
  search: z.string().optional()
})

// GET /api/admin/scheduled-quizzes - Get scheduled quizzes
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
     
    validateQuery: querySchema
  },
  async (request: NextRequest, { validatedQuery }) => {
    const { page, limit, status, search } = validatedQuery
    const skip = (page - 1) * limit
    const now = new Date()

    // Build where clause
    const where: any = {}
    
    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
        { quiz: { title: { contains: search, mode: 'insensitive' } } }
      ]
    }

    // Filter by status
    if (status !== 'all') {
      switch (status) {
        case 'active':
          where.isActive = true
          where.startTime = { lte: now }
          where.endTime = { gte: now }
          break
        case 'upcoming':
          where.isActive = true
          where.startTime = { gt: now }
          break
        case 'completed':
          where.endTime = { lt: now }
          break
        case 'expired':
          where.OR = [
            { isActive: false },
            { endTime: { lt: now } }
          ]
          break
      }
    }

    const [scheduledQuizzes, totalCount] = await Promise.all([
      prisma.scheduledQuiz.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        include: {
          quiz: {
            select: {
              id: true,
              title: true,
              type: true,
              difficulty: true,
              _count: {
                select: {
                  questions: true
                }
              }
            }
          },
          creator: {
            select: {
              name: true,
              email: true
            }
          },
          _count: {
            select: {
              enrollments: true
            }
          }
        }
      }),
      prisma.scheduledQuiz.count({ where })
    ])

    // Calculate additional stats for each scheduled quiz
    const scheduledQuizzesWithStats = await Promise.all(
      scheduledQuizzes.map(async (scheduledQuiz) => {
        // Get attempt count for this scheduled quiz
        const attemptCount = await prisma.quizAttempt.count({
          where: {
            quizId: scheduledQuiz.quizId,
            startedAt: {
              gte: scheduledQuiz.startTime,
              lte: scheduledQuiz.endTime
            }
          }
        })

        // Get completed attempts count
        const completedAttempts = await prisma.quizAttempt.count({
          where: {
            quizId: scheduledQuiz.quizId,
            isCompleted: true,
            startedAt: {
              gte: scheduledQuiz.startTime,
              lte: scheduledQuiz.endTime
            }
          }
        })

        // Determine status
        let currentStatus: 'upcoming' | 'active' | 'completed' | 'expired'
        if (!scheduledQuiz.isActive) {
          currentStatus = 'expired'
        } else if (now < scheduledQuiz.startTime) {
          currentStatus = 'upcoming'
        } else if (now > scheduledQuiz.endTime) {
          currentStatus = 'completed'
        } else {
          currentStatus = 'active'
        }

        return {
          id: scheduledQuiz.id,
          quiz: {
            id: scheduledQuiz.quiz.id,
            title: scheduledQuiz.quiz.title,
            type: scheduledQuiz.quiz.type,
            difficulty: scheduledQuiz.quiz.difficulty,
            questionCount: scheduledQuiz.quiz._count.questions
          },
          title: scheduledQuiz.title,
          description: scheduledQuiz.description,
          startTime: scheduledQuiz.startTime.toISOString(),
          endTime: scheduledQuiz.endTime.toISOString(),
          duration: scheduledQuiz.duration,
          maxAttempts: scheduledQuiz.maxAttempts,
          isActive: scheduledQuiz.isActive,
          allowLateSubmission: scheduledQuiz.allowLateSubmission,
          showResults: scheduledQuiz.showResults,
          shuffleQuestions: scheduledQuiz.shuffleQuestions,
          status: currentStatus,
          participants: scheduledQuiz._count.enrollments,
          totalAttempts: attemptCount,
          completedAttempts,
          createdAt: scheduledQuiz.createdAt.toISOString(),
          createdBy: scheduledQuiz.creator
        }
      })
    )

    const totalPages = Math.ceil(totalCount / limit)

    return APIResponse.success({
      scheduledQuizzes: scheduledQuizzesWithStats,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    }, 'Scheduled quizzes retrieved successfully')
  }
)

// POST /api/admin/scheduled-quizzes - Create scheduled quiz
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
 
    validateBody: createScheduledQuizSchema
  },
  async (request: NextRequest, { validatedBody, user }) => {
    const {
      quizId,
      title,
      description,
      startTime,
      endTime,
      duration,
      maxAttempts,
      allowLateSubmission,
      showResults,
      shuffleQuestions
    } = validatedBody

    // Validate that quiz exists
    const quiz = await prisma.quiz.findUnique({
      where: { id: quizId },
      select: { id: true, title: true }
    })

    if (!quiz) {
      return APIResponse.error('Quiz not found', 404, 'QUIZ_NOT_FOUND')
    }

    // Validate time range
    const start = new Date(startTime)
    const end = new Date(endTime)

    if (start >= end) {
      return APIResponse.error(
        'End time must be after start time',
        400,
        'INVALID_TIME_RANGE'
      )
    }

    if (start < new Date()) {
      return APIResponse.error(
        'Start time cannot be in the past',
        400,
        'INVALID_START_TIME'
      )
    }

    const scheduledQuiz = await prisma.scheduledQuiz.create({
      data: {
        quizId,
        title,
        description,
        startTime: start,
        endTime: end,
        duration,
        maxAttempts,
        allowLateSubmission,
        showResults,
        shuffleQuestions,
        createdBy: user.id
      },
      include: {
        quiz: {
          select: {
            id: true,
            title: true,
            type: true,
            difficulty: true,
            _count: {
              select: {
                questions: true
              }
            }
          }
        },
        creator: {
          select: {
            name: true,
            email: true
          }
        }
      }
    })

    return APIResponse.success(
      {
        id: scheduledQuiz.id,
        quiz: {
          id: scheduledQuiz.quiz.id,
          title: scheduledQuiz.quiz.title,
          type: scheduledQuiz.quiz.type,
          difficulty: scheduledQuiz.quiz.difficulty,
          questionCount: scheduledQuiz.quiz._count.questions
        },
        title: scheduledQuiz.title,
        description: scheduledQuiz.description,
        startTime: scheduledQuiz.startTime.toISOString(),
        endTime: scheduledQuiz.endTime.toISOString(),
        duration: scheduledQuiz.duration,
        maxAttempts: scheduledQuiz.maxAttempts,
        isActive: scheduledQuiz.isActive,
        allowLateSubmission: scheduledQuiz.allowLateSubmission,
        showResults: scheduledQuiz.showResults,
        shuffleQuestions: scheduledQuiz.shuffleQuestions,
        status: 'upcoming',
        participants: 0,
        totalAttempts: 0,
        completedAttempts: 0,
        createdAt: scheduledQuiz.createdAt.toISOString(),
        createdBy: scheduledQuiz.creator
      },
      'Scheduled quiz created successfully',
      201
    )
  }
)
