import { createOpenAI } from '@ai-sdk/openai'
import { createAnthropic } from '@ai-sdk/anthropic'
import { createGoogleGenerativeAI } from '@ai-sdk/google'
import { createXai } from '@ai-sdk/xai'
import { createMistral } from '@ai-sdk/mistral'
import { createGroq } from '@ai-sdk/groq'
import { createDeepSeek } from '@ai-sdk/deepseek'

export interface AIModel {
  id: string
  name: string
  provider: string
  description: string
  capabilities: {
    textGeneration: boolean
    imageInput: boolean
    toolCalling: boolean
    streaming: boolean
    objectGeneration: boolean
    reasoning: boolean
  }
  pricing: {
    input: number // per 1M tokens
    output: number // per 1M tokens
  }
  contextWindow: number
  maxOutput: number
  speed: 'fast' | 'medium' | 'slow'
  quality: 'high' | 'medium' | 'low'
  useCase: string[]
}

export const AI_MODELS: AIModel[] = [
  // OpenAI Models
  {
    id: 'gpt-4o',
    name: 'GPT-4o',
    provider: 'OpenAI',
    description: 'Most capable multimodal model, great for complex reasoning',
    capabilities: {
      textGeneration: true,
      imageInput: true,
      toolCalling: true,
      streaming: true,
      objectGeneration: true,
      reasoning: true
    },
    pricing: { input: 2.5, output: 10 },
    contextWindow: 128000,
    maxOutput: 16384,
    speed: 'medium',
    quality: 'high',
    useCase: ['complex-analysis', 'content-generation', 'reasoning', 'multimodal']
  },
  {
    id: 'gpt-4o-mini',
    name: 'GPT-4o Mini',
    provider: 'OpenAI',
    description: 'Fast and cost-effective, great for simple tasks',
    capabilities: {
      textGeneration: true,
      imageInput: true,
      toolCalling: true,
      streaming: true,
      objectGeneration: true,
      reasoning: false
    },
    pricing: { input: 0.15, output: 0.6 },
    contextWindow: 128000,
    maxOutput: 16384,
    speed: 'fast',
    quality: 'medium',
    useCase: ['simple-tasks', 'classification', 'basic-generation']
  },
  {
    id: 'o3-mini',
    name: 'o3 Mini',
    provider: 'OpenAI',
    description: 'Advanced reasoning model for complex problem solving',
    capabilities: {
      textGeneration: true,
      imageInput: false,
      toolCalling: true,
      streaming: true,
      objectGeneration: true,
      reasoning: true
    },
    pricing: { input: 3.0, output: 12 },
    contextWindow: 200000,
    maxOutput: 65536,
    speed: 'slow',
    quality: 'high',
    useCase: ['reasoning', 'problem-solving', 'analysis', 'complex-tasks']
  },

  // Anthropic Models
  {
    id: 'claude-3-5-sonnet-20241022',
    name: 'Claude 3.5 Sonnet',
    provider: 'Anthropic',
    description: 'Excellent for analysis, writing, and complex reasoning',
    capabilities: {
      textGeneration: true,
      imageInput: true,
      toolCalling: true,
      streaming: true,
      objectGeneration: true,
      reasoning: true
    },
    pricing: { input: 3.0, output: 15 },
    contextWindow: 200000,
    maxOutput: 8192,
    speed: 'medium',
    quality: 'high',
    useCase: ['analysis', 'writing', 'reasoning', 'content-creation']
  },
  {
    id: 'claude-3-5-haiku-20241022',
    name: 'Claude 3.5 Haiku',
    provider: 'Anthropic',
    description: 'Fast and efficient for quick tasks',
    capabilities: {
      textGeneration: true,
      imageInput: true,
      toolCalling: true,
      streaming: true,
      objectGeneration: true,
      reasoning: false
    },
    pricing: { input: 0.8, output: 4.0 },
    contextWindow: 200000,
    maxOutput: 8192,
    speed: 'fast',
    quality: 'medium',
    useCase: ['quick-tasks', 'classification', 'simple-analysis']
  },

  // Google Models
  {
    id: 'gemini-2.0-flash-exp',
    name: 'Gemini 2.0 Flash',
    provider: 'Google',
    description: 'Latest multimodal model with excellent performance',
    capabilities: {
      textGeneration: true,
      imageInput: true,
      toolCalling: true,
      streaming: true,
      objectGeneration: true,
      reasoning: true
    },
    pricing: { input: 0.075, output: 0.3 },
    contextWindow: 1000000,
    maxOutput: 8192,
    speed: 'fast',
    quality: 'high',
    useCase: ['multimodal', 'long-context', 'analysis', 'generation']
  },

  // xAI Models
  {
    id: 'grok-3',
    name: 'Grok 3',
    provider: 'xAI',
    description: 'Advanced reasoning with real-time information',
    capabilities: {
      textGeneration: true,
      imageInput: false,
      toolCalling: true,
      streaming: true,
      objectGeneration: true,
      reasoning: true
    },
    pricing: { input: 2.0, output: 8.0 },
    contextWindow: 131072,
    maxOutput: 4096,
    speed: 'medium',
    quality: 'high',
    useCase: ['reasoning', 'real-time', 'analysis', 'problem-solving']
  },

  // DeepSeek Models
  {
    id: 'deepseek-reasoner',
    name: 'DeepSeek Reasoner',
    provider: 'DeepSeek',
    description: 'Specialized reasoning model for complex problems',
    capabilities: {
      textGeneration: true,
      imageInput: false,
      toolCalling: true,
      streaming: true,
      objectGeneration: true,
      reasoning: true
    },
    pricing: { input: 0.55, output: 2.19 },
    contextWindow: 64000,
    maxOutput: 8192,
    speed: 'slow',
    quality: 'high',
    useCase: ['reasoning', 'problem-solving', 'analysis', 'mathematics']
  },

  // Groq Models (Fast inference)
  {
    id: 'llama-3.3-70b-versatile',
    name: 'Llama 3.3 70B',
    provider: 'Groq',
    description: 'Ultra-fast inference for real-time applications',
    capabilities: {
      textGeneration: true,
      imageInput: false,
      toolCalling: true,
      streaming: true,
      objectGeneration: true,
      reasoning: false
    },
    pricing: { input: 0.59, output: 0.79 },
    contextWindow: 131072,
    maxOutput: 32768,
    speed: 'fast',
    quality: 'medium',
    useCase: ['real-time', 'fast-generation', 'chat', 'simple-tasks']
  }
]

// Create provider instances with API keys (only if keys exist)
const openaiProvider = process.env.OPENAI_API_KEY ? createOpenAI({
  apiKey: "********************************************************************************************************************************************************************"
}) : null

const anthropicProvider = process.env.ANTHROPIC_API_KEY ? createAnthropic({
  apiKey: process.env.ANTHROPIC_API_KEY
}) : null

const googleProvider = process.env.GOOGLE_AI_API_KEY ? createGoogleGenerativeAI({
  apiKey: process.env.GOOGLE_AI_API_KEY
}) : null

const xaiProvider = process.env.XAI_API_KEY ? createXai({
  apiKey: process.env.XAI_API_KEY
}) : null

const mistralProvider = process.env.MISTRAL_API_KEY ? createMistral({
  apiKey: process.env.MISTRAL_API_KEY
}) : null

const groqProvider = process.env.GROQ_API_KEY ? createGroq({
  apiKey: process.env.GROQ_API_KEY
}) : null

const deepseekProvider = process.env.DEEPSEEK_API_KEY ? createDeepSeek({
  apiKey: process.env.DEEPSEEK_API_KEY
}) : null

export function getModelInstance(modelId: string) {
  const model = AI_MODELS.find(m => m.id === modelId)
  if (!model) {
    throw new Error(`Model ${modelId} not found`)
  }

  // Validate API key exists for the provider
  const apiKeyMap = {
    'OpenAI': process.env.OPENAI_API_KEY,
    'Anthropic': process.env.ANTHROPIC_API_KEY,
    'Google': process.env.GOOGLE_AI_API_KEY,
    'xAI': process.env.XAI_API_KEY,
    'Mistral': process.env.MISTRAL_API_KEY,
    'Groq': process.env.GROQ_API_KEY,
    'DeepSeek': process.env.DEEPSEEK_API_KEY
  }

  const apiKey = apiKeyMap[model.provider as keyof typeof apiKeyMap]
  if (!apiKey) {
    throw new Error(`API key not found for ${model.provider}. Please set ${model.provider.toUpperCase()}_API_KEY in your environment variables.`)
  }

  switch (model.provider) {
    case 'OpenAI':
      if (!openaiProvider) throw new Error('OpenAI API key not configured')
      return openaiProvider(modelId)
    case 'Anthropic':
      if (!anthropicProvider) throw new Error('Anthropic API key not configured')
      return anthropicProvider(modelId)
    case 'Google':
      if (!googleProvider) throw new Error('Google AI API key not configured')
      return googleProvider(modelId)
    case 'xAI':
      if (!xaiProvider) throw new Error('xAI API key not configured')
      return xaiProvider(modelId)
    case 'Mistral':
      if (!mistralProvider) throw new Error('Mistral API key not configured')
      return mistralProvider(modelId)
    case 'Groq':
      if (!groqProvider) throw new Error('Groq API key not configured')
      return groqProvider(modelId)
    case 'DeepSeek':
      if (!deepseekProvider) throw new Error('DeepSeek API key not configured')
      return deepseekProvider(modelId)
    default:
      throw new Error(`Provider ${model.provider} not supported`)
  }
}

export function getModelsByUseCase(useCase: string): AIModel[] {
  return AI_MODELS.filter(model => model.useCase.includes(useCase))
}

export function getModelsBySpeed(speed: 'fast' | 'medium' | 'slow'): AIModel[] {
  return AI_MODELS.filter(model => model.speed === speed)
}

export function getModelsByQuality(quality: 'high' | 'medium' | 'low'): AIModel[] {
  return AI_MODELS.filter(model => model.quality === quality)
}

export function getAvailableModels(): AIModel[] {
  const apiKeyMap = {
    'OpenAI': process.env.OPENAI_API_KEY,
    'Anthropic': process.env.ANTHROPIC_API_KEY,
    'Google': process.env.GOOGLE_AI_API_KEY,
    'xAI': process.env.XAI_API_KEY,
    'Mistral': process.env.MISTRAL_API_KEY,
    'Groq': process.env.GROQ_API_KEY,
    'DeepSeek': process.env.DEEPSEEK_API_KEY
  }

  return AI_MODELS.filter(model =>
    apiKeyMap[model.provider as keyof typeof apiKeyMap]
  )
}

export function getBestModelForTask(task: {
  useCase: string
  prioritizeSpeed?: boolean
  prioritizeQuality?: boolean
  maxCost?: number
  preferOpenAI?: boolean
}): AIModel {
  // Only consider models with available API keys
  const availableModels = getAvailableModels()

  // If preferOpenAI is true, prioritize OpenAI models
  if (task.preferOpenAI) {
    const openAIModels = availableModels.filter(model => model.provider === 'OpenAI')
    if (openAIModels.length > 0) {
      // For OpenAI, choose based on task requirements
      if (task.prioritizeSpeed || task.useCase === 'analysis') {
        return openAIModels.find(m => m.id === 'gpt-4o-mini') || openAIModels[0]
      } else if (task.useCase === 'reasoning' || task.prioritizeQuality) {
        return openAIModels.find(m => m.id === 'gpt-4o') || openAIModels[0]
      } else {
        return openAIModels.find(m => m.id === 'gpt-4o') || openAIModels[0]
      }
    }
  }

  let candidates = availableModels.filter(model =>
    model.useCase.includes(task.useCase)
  )

  if (candidates.length === 0) {
    // Fallback to any available model if no specific use case match
    candidates = availableModels
  }

  if (task.maxCost !== undefined) {
    candidates = candidates.filter(model => model.pricing.input <= task.maxCost!)
  }

  if (task.prioritizeSpeed) {
    candidates.sort((a, b) => {
      const speedOrder = { fast: 3, medium: 2, slow: 1 }
      return speedOrder[b.speed] - speedOrder[a.speed]
    })
  } else if (task.prioritizeQuality) {
    candidates.sort((a, b) => {
      const qualityOrder = { high: 3, medium: 2, low: 1 }
      return qualityOrder[b.quality] - qualityOrder[a.quality]
    })
  }

  if (candidates.length === 0) {
    throw new Error('No AI models available. Please configure at least one API key.')
  }

  return candidates[0]
}

// Helper function to get OpenAI-only configuration
export function getOpenAIConfiguration(): {
  orchestrator: AIModel
  contentAnalyzer: AIModel
  questionGenerator: AIModel
  qualityEvaluator: AIModel
} {
  const availableModels = getAvailableModels()
  const openAIModels = availableModels.filter(model => model.provider === 'OpenAI')

  if (openAIModels.length === 0) {
    throw new Error('OpenAI API key not configured. Please add OPENAI_API_KEY to your environment variables.')
  }

  // Prefer GPT-4o for most tasks, GPT-4o Mini for speed-critical tasks
  const gpt4o = openAIModels.find(m => m.id === 'gpt-4o')
  const gpt4oMini = openAIModels.find(m => m.id === 'gpt-4o-mini')
  const fallback = openAIModels[0]

  return {
    orchestrator: gpt4o || fallback,           // Complex reasoning and planning
    contentAnalyzer: gpt4oMini || fallback,   // Fast content analysis
    questionGenerator: gpt4o || fallback,     // High-quality question generation
    qualityEvaluator: gpt4o || fallback       // Thorough quality evaluation
  }
}
