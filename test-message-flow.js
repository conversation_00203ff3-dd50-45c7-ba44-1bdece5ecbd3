const { io } = require('socket.io-client');

// Test the complete message flow to identify where it breaks
async function testMessageFlow() {
  console.log('🧪 Testing complete message flow...');

  // Create socket connection
  const socket = io('http://localhost:3001', {
    transports: ['websocket', 'polling']
  });

  // Wait for connection
  await new Promise((resolve) => {
    socket.on('connect', () => {
      console.log('🔌 Connected:', socket.id);
      resolve();
    });
  });

  // Authenticate
  let authenticated = false;
  socket.on('authenticated', () => {
    console.log('✅ Authenticated');
    authenticated = true;
  });

  socket.emit('authenticate', {
    userId: 'test-user-1',
    name: 'Test User',
    email: '<EMAIL>',
    role: 'STUDENT'
  });

  // Wait for authentication
  while (!authenticated) {
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  // Set up message listener
  const receivedMessages = [];
  socket.on('chat:message_received', (message) => {
    console.log('📥 Received message:', message);
    receivedMessages.push(message);
  });

  // Join room
  console.log('🏠 Joining room: student-general');
  socket.emit('chat:join', { roomId: 'student-general' });

  // Wait a moment for room join
  await new Promise(resolve => setTimeout(resolve, 1000));

  // Send message
  console.log('📤 Sending message...');
  socket.emit('chat:message', {
    roomId: 'student-general',
    message: 'Test message from flow test',
    type: 'text'
  });

  // Wait for message processing
  await new Promise(resolve => setTimeout(resolve, 3000));

  console.log('📊 Results:');
  console.log('Messages received:', receivedMessages.length);
  
  if (receivedMessages.length > 0) {
    console.log('✅ Message flow test PASSED');
    receivedMessages.forEach((msg, index) => {
      console.log(`Message ${index + 1}:`, msg.message);
    });
  } else {
    console.log('❌ Message flow test FAILED - no messages received');
  }

  socket.disconnect();
}

// Run the test
testMessageFlow().catch(console.error);
