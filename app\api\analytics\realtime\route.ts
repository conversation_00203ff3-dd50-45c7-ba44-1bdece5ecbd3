import { NextRequest } from 'next/server'
import { createAPIHandler, APIResponse } from '@/lib/api-middleware'
import { AnalyticsService } from '@/lib/analytics-service'

// GET /api/analytics/realtime - Get real-time metrics
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest) => {
    const realTimeMetrics = await AnalyticsService.getRealTimeMetrics()
    
    return APIResponse.success(
      realTimeMetrics,
      'Real-time metrics retrieved successfully'
    )
  }
)
