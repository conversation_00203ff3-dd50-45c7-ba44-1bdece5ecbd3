"use client"

import { useState, useEffect } from "react"
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { 
  Users, 
  CheckCircle, 
  Clock, 
  XCircle, 
  Mail,
  Calendar,
  Trophy,
  Timer,
  Loader2
} from "lucide-react"
import { toast } from "@/lib/toast-utils"

interface Participant {
  id: string
  name: string
  email: string
  avatar?: string
  enrolledAt: string
  status: 'completed' | 'in_progress' | 'not_started'
  score: number
  percentage: number
  completedAt?: string
  timeSpent: number
  attemptId?: string
}

interface ParticipantsStats {
  totalEnrolled: number
  completed: number
  inProgress: number
  notStarted: number
  averageScore: number
}

interface ScheduledQuiz {
  id: string
  title: string
  startTime: string
  endTime: string
  quiz: {
    id: string
    title: string
    type: string
  }
}

interface ParticipantsModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  scheduledQuizId: string
}

export function ParticipantsModal({ open, onOpenChange, scheduledQuizId }: ParticipantsModalProps) {
  const [loading, setLoading] = useState(false)
  const [participants, setParticipants] = useState<Participant[]>([])
  const [stats, setStats] = useState<ParticipantsStats | null>(null)
  const [scheduledQuiz, setScheduledQuiz] = useState<ScheduledQuiz | null>(null)

  useEffect(() => {
    if (open && scheduledQuizId) {
      fetchParticipants()
    }
  }, [open, scheduledQuizId])

  const fetchParticipants = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/admin/scheduled-quizzes/${scheduledQuizId}/participants`)
      
      if (!response.ok) {
        throw new Error('Failed to fetch participants')
      }
      
      const data = await response.json()
      setParticipants(data.participants || [])
      setStats(data.stats)
      setScheduledQuiz(data.scheduledQuiz)
    } catch (error) {
      console.error('Error fetching participants:', error)
      toast.error('Failed to load participants')
    } finally {
      setLoading(false)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'in_progress':
        return <Clock className="h-4 w-4 text-yellow-600" />
      case 'not_started':
        return <XCircle className="h-4 w-4 text-gray-400" />
      default:
        return <XCircle className="h-4 w-4 text-gray-400" />
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge variant="default" className="bg-green-100 text-green-800">Completed</Badge>
      case 'in_progress':
        return <Badge variant="default" className="bg-yellow-100 text-yellow-800">In Progress</Badge>
      case 'not_started':
        return <Badge variant="secondary">Not Started</Badge>
      default:
        return <Badge variant="secondary">Unknown</Badge>
    }
  }

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}m ${remainingSeconds}s`
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Quiz Participants
          </DialogTitle>
          <DialogDescription>
            {scheduledQuiz && (
              <>View and manage participants for "{scheduledQuiz.title}"</>
            )}
          </DialogDescription>
        </DialogHeader>

        {loading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        ) : (
          <div className="space-y-6">
            {/* Statistics */}
            {stats && (
              <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                <Card>
                  <CardContent className="pt-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold">{stats.totalEnrolled}</div>
                      <div className="text-sm text-muted-foreground">Total</div>
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="pt-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">{stats.completed}</div>
                      <div className="text-sm text-muted-foreground">Completed</div>
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="pt-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-yellow-600">{stats.inProgress}</div>
                      <div className="text-sm text-muted-foreground">In Progress</div>
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="pt-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-gray-500">{stats.notStarted}</div>
                      <div className="text-sm text-muted-foreground">Not Started</div>
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="pt-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold">{Math.round(stats.averageScore)}%</div>
                      <div className="text-sm text-muted-foreground">Avg Score</div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Participants List */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Participants ({participants.length})</h3>
              
              {participants.length === 0 ? (
                <Card>
                  <CardContent className="pt-6">
                    <div className="text-center py-8">
                      <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <h3 className="text-lg font-semibold mb-2">No participants yet</h3>
                      <p className="text-muted-foreground">
                        No students have enrolled in this scheduled quiz.
                      </p>
                    </div>
                  </CardContent>
                </Card>
              ) : (
                <div className="space-y-3">
                  {participants.map((participant) => (
                    <Card key={participant.id}>
                      <CardContent className="pt-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <Avatar>
                              <AvatarImage src={participant.avatar} />
                              <AvatarFallback>
                                {participant.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <div className="font-medium">{participant.name}</div>
                              <div className="text-sm text-muted-foreground flex items-center gap-1">
                                <Mail className="h-3 w-3" />
                                {participant.email}
                              </div>
                              <div className="text-xs text-muted-foreground flex items-center gap-1 mt-1">
                                <Calendar className="h-3 w-3" />
                                Enrolled: {new Date(participant.enrolledAt).toLocaleDateString()}
                              </div>
                            </div>
                          </div>
                          
                          <div className="flex items-center gap-4">
                            <div className="text-right">
                              {getStatusBadge(participant.status)}
                              {participant.status === 'completed' && (
                                <div className="text-sm text-muted-foreground mt-1 flex items-center gap-1">
                                  <Trophy className="h-3 w-3" />
                                  {participant.percentage}% ({participant.score} pts)
                                </div>
                              )}
                              {participant.timeSpent > 0 && (
                                <div className="text-xs text-muted-foreground flex items-center gap-1 mt-1">
                                  <Timer className="h-3 w-3" />
                                  {formatTime(participant.timeSpent)}
                                </div>
                              )}
                            </div>
                            {getStatusIcon(participant.status)}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}
