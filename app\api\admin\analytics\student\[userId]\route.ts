import { NextRequest } from 'next/server'
 import { createAPIHandler, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const getStudentAnalyticsSchema = z.object({
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
  period: z.enum(['7d', '30d', '90d', '1y', 'all']).optional().default('30d')
})

// GET /api/admin/analytics/student/[userId] - Get analytics data for a specific student
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
     
    validateQuery: getStudentAnalyticsSchema
  },
  async (request: NextRequest, { params, validatedQuery }) => {
    const resolvedParams = await params
    const userId = resolvedParams?.userId as string
    const { startDate, endDate, period } = validatedQuery

    if (!userId) {
      return APIResponse.error('User ID is required', 400)
    }

    try {
      // Calculate date range
      let dateRange: { start: Date; end: Date }
      
      if (startDate && endDate) {
        dateRange = {
          start: new Date(startDate),
          end: new Date(endDate)
        }
      } else {
        const end = new Date()
        const start = new Date()
        
        switch (period) {
          case '7d':
            start.setDate(end.getDate() - 7)
            break
          case '30d':
            start.setDate(end.getDate() - 30)
            break
          case '90d':
            start.setDate(end.getDate() - 90)
            break
          case '1y':
            start.setFullYear(end.getFullYear() - 1)
            break
          case 'all':
            start.setFullYear(2020) // Set to a very early date
            break
        }
        
        dateRange = { start, end }
      }

      // Verify user exists
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          name: true,
          email: true,
          points: true,
          level: true,
          totalQuizzes: true,
          averageScore: true,
          streak: true,
          totalTimeSpent: true,
          longestStreak: true
        }
      })

      if (!user) {
        return APIResponse.error('User not found', 404)
      }

      // Get quiz attempts in date range
      const attempts = await prisma.quizAttempt.findMany({
        where: {
          userId,
          isCompleted: true,
          completedAt: {
            gte: dateRange.start,
            lte: dateRange.end
          }
        },
        include: {
          quiz: {
            select: {
              id: true,
              title: true,
              type: true,
              difficulty: true,
              subject: {
                select: {
                  id: true,
                  name: true
                }
              },
              chapter: {
                select: {
                  id: true,
                  name: true
                }
              }
            }
          }
        },
        orderBy: {
          completedAt: 'asc'
        }
      })

      // Calculate analytics
      const totalAttempts = attempts.length
      const totalScore = attempts.reduce((sum, attempt) => sum + attempt.score, 0)
      const totalPossibleScore = attempts.reduce((sum, attempt) => sum + attempt.totalPoints, 0)
      const averageScore = totalAttempts > 0 ? totalScore / totalAttempts : 0
      const averagePercentage = totalAttempts > 0 ? 
        attempts.reduce((sum, attempt) => sum + attempt.percentage, 0) / totalAttempts : 0
      const totalTimeSpent = attempts.reduce((sum, attempt) => sum + (attempt.timeSpent || 0), 0)

      // Performance by category (subject)
      const performanceByCategory = attempts.reduce((acc: any[], attempt) => {
        const categoryName = attempt.quiz.subject?.name || 'Uncategorized'
        const existing = acc.find(item => item.category === categoryName)
        
        if (existing) {
          existing.attempts += 1
          existing.totalScore += attempt.score
          existing.totalPossible += attempt.totalPoints
          existing.averageScore = existing.totalScore / existing.attempts
          existing.averagePercentage = (existing.averagePercentage * (existing.attempts - 1) + attempt.percentage) / existing.attempts
        } else {
          acc.push({
            category: categoryName,
            attempts: 1,
            totalScore: attempt.score,
            totalPossible: attempt.totalPoints,
            averageScore: attempt.score,
            averagePercentage: attempt.percentage
          })
        }
        
        return acc
      }, [])

      // Performance by difficulty
      const performanceByDifficulty = attempts.reduce((acc: any[], attempt) => {
        const difficulty = attempt.quiz.difficulty
        const existing = acc.find(item => item.difficulty === difficulty)
        
        if (existing) {
          existing.attempts += 1
          existing.totalScore += attempt.score
          existing.totalPossible += attempt.totalPoints
          existing.averageScore = existing.totalScore / existing.attempts
          existing.averagePercentage = (existing.averagePercentage * (existing.attempts - 1) + attempt.percentage) / existing.attempts
        } else {
          acc.push({
            difficulty,
            attempts: 1,
            totalScore: attempt.score,
            totalPossible: attempt.totalPoints,
            averageScore: attempt.score,
            averagePercentage: attempt.percentage
          })
        }
        
        return acc
      }, [])

      // Weekly progress (group by week)
      const weeklyProgress = attempts.reduce((acc: any[], attempt) => {
        if (!attempt.completedAt) return acc
        
        const weekStart = new Date(attempt.completedAt)
        weekStart.setDate(weekStart.getDate() - weekStart.getDay()) // Start of week
        weekStart.setHours(0, 0, 0, 0)
        const weekKey = weekStart.toISOString().split('T')[0]
        
        const existing = acc.find(item => item.week === weekKey)
        
        if (existing) {
          existing.attempts += 1
          existing.totalScore += attempt.score
          existing.totalPossible += attempt.totalPoints
          existing.averageScore = existing.totalScore / existing.attempts
          existing.averagePercentage = (existing.averagePercentage * (existing.attempts - 1) + attempt.percentage) / existing.attempts
        } else {
          acc.push({
            week: weekKey,
            attempts: 1,
            totalScore: attempt.score,
            totalPossible: attempt.totalPoints,
            averageScore: attempt.score,
            averagePercentage: attempt.percentage
          })
        }
        
        return acc
      }, [])

      // Calculate improvement rate
      const improvementRate = weeklyProgress.length > 1 ? 
        ((weeklyProgress[weeklyProgress.length - 1]?.averagePercentage || 0) - 
         (weeklyProgress[0]?.averagePercentage || 0)) : 0

      const analyticsData = {
        user,
        dateRange: {
          start: dateRange.start.toISOString(),
          end: dateRange.end.toISOString(),
          period
        },
        overview: {
          totalAttempts,
          averageScore: Math.round(averageScore * 100) / 100,
          averagePercentage: Math.round(averagePercentage * 100) / 100,
          totalTimeSpent,
          improvementRate: Math.round(improvementRate * 100) / 100
        },
        performanceByCategory,
        performanceByDifficulty,
        weeklyProgress: weeklyProgress.sort((a, b) => a.week.localeCompare(b.week))
      }

      return APIResponse.success(analyticsData, 'Student analytics retrieved successfully')

    } catch (error) {
      console.error('Error fetching student analytics:', error)
      return APIResponse.error(
        error instanceof Error ? error.message : 'Failed to fetch student analytics',
        500
      )
    }
  }
)
